import { describe, it, expect, vi } from 'vitest';
import { render, screen } from './utils';
import { TestHelper } from './integration-helpers';
import { TEST_IDS, VALIDATION_MESSAGES, MOCK_VALUES } from './test-config';
import { createMockReceiptItem } from './utils';

// Mock a simple component to test the framework
const MockReceiptApp = () => {
  return (
    <div>
      <h1>Receipt Generator</h1>
      
      {/* Store Info Section */}
      <section>
        <input data-testid={TEST_IDS.STORE_NAME_INPUT} placeholder="Store Name" />
        <input data-testid={TEST_IDS.STORE_ADDRESS_INPUT} placeholder="Store Address" />
        <input data-testid={TEST_IDS.STORE_PHONE_INPUT} placeholder="Store Phone" />
        <input data-testid={TEST_IDS.STORE_EMAIL_INPUT} placeholder="Store Email" />
      </section>

      {/* Customer Info Section */}
      <section>
        <input data-testid={TEST_IDS.CUSTOMER_NAME_INPUT} placeholder="Customer Name" />
        <input data-testid={TEST_IDS.CUSTOMER_PHONE_INPUT} placeholder="Customer Phone" />
        <input data-testid={TEST_IDS.CUSTOMER_EMAIL_INPUT} placeholder="Customer Email" />
      </section>

      {/* Item Management */}
      <section>
        <button data-testid={TEST_IDS.ADD_ITEM_BUTTON}>Add Item</button>
        <input data-testid={TEST_IDS.ITEM_NAME_INPUT} placeholder="Item Name" />
        <select data-testid={TEST_IDS.ITEM_CATEGORY_SELECT}>
          <option value="hardware">Hardware</option>
          <option value="service">Service</option>
        </select>
        <input data-testid={TEST_IDS.ITEM_PRICE_INPUT} placeholder="Price" />
        <input data-testid={TEST_IDS.ITEM_QUANTITY_INPUT} placeholder="Quantity" />
        <button data-testid={TEST_IDS.SAVE_ITEM_BUTTON}>Save Item</button>
        <button data-testid={TEST_IDS.CANCEL_ITEM_BUTTON}>Cancel</button>
      </section>

      {/* Receipt Preview */}
      <section data-testid={TEST_IDS.RECEIPT_PREVIEW}>
        <div data-testid={TEST_IDS.RECEIPT_HEADER}>Receipt Header</div>
        <div data-testid={TEST_IDS.RECEIPT_BODY}>Receipt Body</div>
        <div data-testid={TEST_IDS.RECEIPT_FOOTER}>Receipt Footer</div>
      </section>

      {/* Action Buttons */}
      <section>
        <button data-testid={TEST_IDS.PRINT_BUTTON}>Print</button>
        <button data-testid={TEST_IDS.SAVE_PDF_BUTTON}>Save PDF</button>
      </section>

      {/* Tax Rate */}
      <input data-testid={TEST_IDS.TAX_RATE_INPUT} placeholder="Tax Rate %" />
    </div>
  );
};

describe('Testing Framework Validation', () => {
  describe('Basic Rendering and Test IDs', () => {
    it('should render all required test elements', () => {
      render(<MockReceiptApp />);
      
      // Verify all test IDs are present
      expect(screen.getByTestId(TEST_IDS.STORE_NAME_INPUT)).toBeInTheDocument();
      expect(screen.getByTestId(TEST_IDS.CUSTOMER_NAME_INPUT)).toBeInTheDocument();
      expect(screen.getByTestId(TEST_IDS.ADD_ITEM_BUTTON)).toBeInTheDocument();
      expect(screen.getByTestId(TEST_IDS.RECEIPT_PREVIEW)).toBeInTheDocument();
      expect(screen.getByTestId(TEST_IDS.PRINT_BUTTON)).toBeInTheDocument();
      expect(screen.getByTestId(TEST_IDS.SAVE_PDF_BUTTON)).toBeInTheDocument();
    });
  });

  describe('Test Utilities', () => {
    it('should create valid mock data', () => {
      const mockItem = createMockReceiptItem({
        name: 'Custom CPU',
        price: 599.99,
      });

      expect(mockItem.name).toBe('Custom CPU');
      expect(mockItem.price).toBe(599.99);
      expect(mockItem.price).toBeValidPrice();
      expect(mockItem.quantity).toBeValidQuantity();
    });

    it('should have working custom matchers', () => {
      // Test price validation
      expect(99.99).toBeValidPrice();
      expect(1).toBeValidQuantity();
      expect('$1,234.56').toBeFormattedCurrency();

      // Test receipt structure validation
      const validReceipt = {
        receiptNumber: 'RCP-123',
        date: new Date(),
        storeInfo: { name: 'Test', address: 'Test', phone: 'Test', email: 'Test' },
        customerInfo: { name: 'Test', phone: 'Test', email: 'Test' },
        items: [],
        taxRate: 0.08,
        subtotal: 100,
        taxAmount: 8,
        total: 108,
      };
      expect(validReceipt).toHaveValidReceiptStructure();
    });
  });

  describe('User Interaction Helpers', () => {
    it('should handle form interactions', async () => {
      render(<MockReceiptApp />);

      // Test store info filling
      await TestHelper.fillStoreInfo({
        name: 'Test Store',
        email: '<EMAIL>',
      });

      expect(screen.getByDisplayValue('Test Store')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    });

    it('should handle customer info filling', async () => {
      render(<MockReceiptApp />);

      await TestHelper.fillCustomerInfo({
        name: 'Jane Doe',
        phone: '(*************',
      });

      expect(screen.getByDisplayValue('Jane Doe')).toBeInTheDocument();
      expect(screen.getByDisplayValue('(*************')).toBeInTheDocument();
    });

    it('should handle tax rate setting', async () => {
      render(<MockReceiptApp />);

      await TestHelper.setTaxRate(0.08);
      expect(screen.getByDisplayValue('8')).toBeInTheDocument();
    });
  });

  describe('Mock Functions', () => {
    it('should have mocked window.print', () => {
      expect(window.print).toBeDefined();
      expect(vi.isMockFunction(window.print)).toBe(true);
    });

    it('should handle print button clicks', async () => {
      render(<MockReceiptApp />);
      
      await TestHelper.clickPrint();
      // In a real test, we would verify window.print was called
      // but for this validation test, we just ensure no errors occur
    });
  });

  describe('Configuration Constants', () => {
    it('should have all required test IDs', () => {
      expect(TEST_IDS.STORE_NAME_INPUT).toBeDefined();
      expect(TEST_IDS.CUSTOMER_NAME_INPUT).toBeDefined();
      expect(TEST_IDS.ADD_ITEM_BUTTON).toBeDefined();
      expect(TEST_IDS.RECEIPT_PREVIEW).toBeDefined();
    });

    it('should have validation messages', () => {
      expect(VALIDATION_MESSAGES.REQUIRED_FIELD).toBe('此欄位為必填');
      expect(VALIDATION_MESSAGES.INVALID_EMAIL).toBe('請輸入有效的電子郵件地址');
      expect(VALIDATION_MESSAGES.INVALID_PRICE).toBe('價格必須為正數');
    });

    it('should have mock values for testing', () => {
      expect(MOCK_VALUES.VALID_EMAIL).toBe('<EMAIL>');
      expect(MOCK_VALUES.VALID_PHONE).toBe('(*************');
      expect(MOCK_VALUES.VALID_PRICE).toBe('99.99');
    });
  });
});