import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { ReceiptFooter } from './ReceiptFooter';

describe('ReceiptFooter', () => {
  const defaultProps = {
    subtotal: 10000,
    taxAmount: 500,
    total: 10500,
    taxRate: 5,
  };

  it('renders calculation summary correctly', () => {
    render(<ReceiptFooter {...defaultProps} />);

    expect(screen.getByText('小計:')).toBeInTheDocument();
    expect(screen.getByText('$10,000.00')).toBeInTheDocument();
    
    expect(screen.getByText('稅額 (5%):')).toBeInTheDocument();
    expect(screen.getByText('$500.00')).toBeInTheDocument();
    
    expect(screen.getByText('總計:')).toBeInTheDocument();
    expect(screen.getByText('$10,500.00')).toBeInTheDocument();
  });

  it('displays tax rate correctly in label', () => {
    const propsWithDifferentTaxRate = {
      ...defaultProps,
      taxRate: 10,
      taxAmount: 1000,
      total: 11000,
    };

    render(<ReceiptFooter {...propsWithDifferentTaxRate} />);

    expect(screen.getByText('稅額 (10%):')).toBeInTheDocument();
    expect(screen.getByText('$1,000.00')).toBeInTheDocument();
  });

  it('renders thank you message', () => {
    render(<ReceiptFooter {...defaultProps} />);

    expect(screen.getByText('感謝您的惠顧！')).toBeInTheDocument();
  });

  it('renders return policy information', () => {
    render(<ReceiptFooter {...defaultProps} />);

    expect(screen.getByText('商品如有問題，請於購買後 7 天內攜帶收據辦理退換貨')).toBeInTheDocument();
  });

  it('renders warranty information', () => {
    render(<ReceiptFooter {...defaultProps} />);

    expect(screen.getByText('硬體商品享有原廠保固，服務項目提供 30 天品質保證')).toBeInTheDocument();
  });

  it('renders print information', () => {
    render(<ReceiptFooter {...defaultProps} />);

    expect(screen.getByText('此收據為電腦生成，無需簽章')).toBeInTheDocument();
  });

  it('handles zero values correctly', () => {
    const zeroProps = {
      subtotal: 0,
      taxAmount: 0,
      total: 0,
      taxRate: 0,
    };

    render(<ReceiptFooter {...zeroProps} />);

    // 會有三個 $0.00：小計、稅額、總計
    expect(screen.getAllByText('$0.00')).toHaveLength(3);
    expect(screen.getByText('稅額 (0%):')).toBeInTheDocument();
  });

  it('handles decimal values correctly', () => {
    const decimalProps = {
      subtotal: 1234.56,
      taxAmount: 61.73,
      total: 1296.29,
      taxRate: 5,
    };

    render(<ReceiptFooter {...decimalProps} />);

    expect(screen.getByText('$1,234.56')).toBeInTheDocument();
    expect(screen.getByText('$61.73')).toBeInTheDocument();
    expect(screen.getByText('$1,296.29')).toBeInTheDocument();
  });

  it('handles large amounts correctly', () => {
    const largeAmountProps = {
      subtotal: 999999.99,
      taxAmount: 49999.99,
      total: 1049999.98,
      taxRate: 5,
    };

    render(<ReceiptFooter {...largeAmountProps} />);

    expect(screen.getByText('$999,999.99')).toBeInTheDocument();
    expect(screen.getByText('$49,999.99')).toBeInTheDocument();
    expect(screen.getByText('$1,049,999.98')).toBeInTheDocument();
  });

  it('applies correct CSS classes to elements', () => {
    render(<ReceiptFooter {...defaultProps} />);

    const totalLabel = screen.getByText('總計:');
    const totalValue = screen.getByText('$10,500.00');
    
    // 檢查 CSS 類別是否包含相應的樣式（CSS modules 會產生 hash）
    expect(totalLabel.className).toMatch(/totalLabel/);
    expect(totalValue.className).toMatch(/totalValue/);
  });

  it('renders all required sections', () => {
    const { container } = render(<ReceiptFooter {...defaultProps} />);

    // 檢查主要區塊是否存在（使用 CSS modules 的 hash 類別名稱）
    expect(container.querySelector('[class*="calculationSummary"]')).toBeInTheDocument();
    expect(container.querySelector('[class*="footerMessage"]')).toBeInTheDocument();
    expect(container.querySelector('[class*="receiptBottom"]')).toBeInTheDocument();
  });
});