import React, { useState, useEffect } from 'react';
import { type PresetItem, presetItems as defaultPresetItems, presetItemsByCategory } from '../../data/presetItems';
import styles from './PresetItemSelectorNew.module.css';

interface PresetItemSelectorProps {
  onSelectItem: (item: PresetItem) => void;
  isVisible: boolean;
  onToggle: () => void;
}

const PresetItemSelector: React.FC<PresetItemSelectorProps> = ({
  onSelectItem,
  isVisible,
  onToggle
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [presetItems, setPresetItems] = useState<PresetItem[]>([]);
  const [editingItem, setEditingItem] = useState<PresetItem | null>(null);
  const [draggedItem, setDraggedItem] = useState<PresetItem | null>(null);

  // 載入預設項目和自定義項目
  useEffect(() => {
    const customItems = JSON.parse(localStorage.getItem('customPresetItems') || '[]');
    const allItems = [...defaultPresetItems, ...customItems].sort((a, b) => (a.order || 0) - (b.order || 0));
    setPresetItems(allItems);
  }, []);

  // 保存項目到 localStorage
  const saveItems = (items: PresetItem[]) => {
    const customItems = items.filter(item => item.isCustom);
    localStorage.setItem('customPresetItems', JSON.stringify(customItems));
    setPresetItems(items);
  };

  // 獲取類別列表
  const categories = ['All', ...Object.keys(presetItemsByCategory)];
  
  // 過濾項目
  const filteredItems = selectedCategory === 'All' 
    ? presetItems 
    : presetItems.filter(item => item.category === selectedCategory);

  // 獲取類別圖示
  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: string } = {
      'PC Case': '🏠',
      'CPU': '🧠',
      'CPU Cooler': '❄️',
      'GPU': '🎮',
      'RAM': '💾',
      'SSD': '💿',
      'Motherboard': '🔌',
      'PSU': '⚡',
      'RGB': '🌈',
      'Other': '🔧',
      'Labor Fee': '👷',
      'OS': '💻',
      'OC': '⚡',
      'Package': '📦',
      'Other Services': '⚙️'
    };
    return icons[category] || '📦';
  };

  // 編輯項目
  const handleEditItem = (item: PresetItem) => {
    setEditingItem({ ...item });
  };

  // 保存編輯
  const handleSaveEdit = () => {
    if (!editingItem) return;

    if (editingItem.isCustom) {
      // 更新自定義項目
      const updatedItems = presetItems.map(item =>
        item.id === editingItem.id ? editingItem : item
      );
      saveItems(updatedItems);
    } else {
      // 將預設項目轉為自定義項目
      const newCustomItem = {
        ...editingItem,
        id: `custom-${Date.now()}`,
        isCustom: true
      };
      const updatedItems = [...presetItems, newCustomItem];
      saveItems(updatedItems);
    }
    setEditingItem(null);
  };

  // 刪除項目
  const handleDeleteItem = (itemId: string) => {
    if (confirm('確定要刪除這個項目嗎？')) {
      const updatedItems = presetItems.filter(item => item.id !== itemId);
      saveItems(updatedItems);
    }
  };

  // 拖拽開始
  const handleDragStart = (e: React.DragEvent, item: PresetItem) => {
    setDraggedItem(item);
    e.dataTransfer.effectAllowed = 'move';
  };

  // 拖拽結束
  const handleDragEnd = () => {
    setDraggedItem(null);
  };

  // 拖拽經過
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // 放置
  const handleDrop = (e: React.DragEvent, targetItem: PresetItem) => {
    e.preventDefault();
    if (!draggedItem || draggedItem.id === targetItem.id) return;

    const draggedIndex = presetItems.findIndex(item => item.id === draggedItem.id);
    const targetIndex = presetItems.findIndex(item => item.id === targetItem.id);

    const newItems = [...presetItems];
    newItems.splice(draggedIndex, 1);
    newItems.splice(targetIndex, 0, draggedItem);

    // 更新 order
    const updatedItems = newItems.map((item, index) => ({
      ...item,
      order: index + 1,
      isCustom: item.isCustom || item.id === draggedItem.id
    }));

    saveItems(updatedItems);
  };

  if (!isVisible) return null;

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h4>Quick Add Items</h4>
        <button onClick={onToggle} className={styles.toggleButton}>
          Hide Presets
        </button>
      </div>

      {/* 類別篩選 */}
      <div className={styles.categoryFilter}>
        {categories.map(category => {
          const count = category === 'All' 
            ? presetItems.length 
            : presetItems.filter(item => item.category === category).length;
          
          return (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`${styles.categoryButton} ${selectedCategory === category ? styles.active : ''}`}
            >
              {category} ({count})
            </button>
          );
        })}
      </div>

      {/* 項目列表 */}
      <div className={styles.itemsList}>
        {filteredItems.map((item) => {
          const discountPercentage = item.originalPrice && item.originalPrice > item.price 
            ? Math.round(((item.originalPrice - item.price) / item.originalPrice) * 100)
            : 0;

          return (
            <div
              key={item.id}
              className={`${styles.itemRow} ${draggedItem?.id === item.id ? styles.dragging : ''}`}
              draggable
              onDragStart={(e) => handleDragStart(e, item)}
              onDragEnd={handleDragEnd}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, item)}
            >
              <div className={styles.dragHandle}>⋮⋮</div>
              
              <div className={styles.itemInfo}>
                <div className={styles.itemHeader}>
                  <span className={styles.itemName}>{item.name}</span>
                  <span className={styles.itemCategory}>
                    {getCategoryIcon(item.category)} {item.category}
                  </span>
                </div>
                {item.description && (
                  <div className={styles.itemDescription}>{item.description}</div>
                )}
              </div>

              <div className={styles.priceInfo}>
                <div className={styles.currentPrice}>${item.price.toFixed(2)}</div>
                {item.originalPrice && item.originalPrice > item.price && (
                  <div className={styles.originalPrice}>
                    <span className={styles.strikethrough}>${item.originalPrice.toFixed(2)}</span>
                    <span className={styles.discount}>-{discountPercentage}%</span>
                  </div>
                )}
              </div>

              <div className={styles.actions}>
                <button
                  onClick={() => handleEditItem(item)}
                  className={styles.editButton}
                >
                  Edit
                </button>
                <button
                  onClick={() => handleDeleteItem(item.id)}
                  className={styles.deleteButton}
                >
                  Delete
                </button>
                <button
                  onClick={() => onSelectItem(item)}
                  className={styles.addButton}
                >
                  Add Item
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* 編輯彈窗 */}
      {editingItem && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <div className={styles.modalHeader}>
              <h3>Edit Preset Item</h3>
              <button 
                onClick={() => setEditingItem(null)}
                className={styles.closeButton}
              >
                ×
              </button>
            </div>

            <div className={styles.modalBody}>
              <div className={styles.formGroup}>
                <label>Item Name</label>
                <input
                  type="text"
                  value={editingItem.name}
                  onChange={(e) => setEditingItem({ ...editingItem, name: e.target.value })}
                />
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Price</label>
                  <input
                    type="number"
                    step="0.01"
                    value={editingItem.price}
                    onChange={(e) => setEditingItem({ ...editingItem, price: parseFloat(e.target.value) || 0 })}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Original Price (Optional)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={editingItem.originalPrice || ''}
                    onChange={(e) => setEditingItem({ 
                      ...editingItem, 
                      originalPrice: e.target.value ? parseFloat(e.target.value) : undefined 
                    })}
                  />
                </div>
              </div>

              <div className={styles.formGroup}>
                <label>Category</label>
                <select
                  value={editingItem.category}
                  onChange={(e) => setEditingItem({ 
                    ...editingItem, 
                    category: e.target.value as PresetItem['category']
                  })}
                >
                  {Object.keys(presetItemsByCategory).map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              <div className={styles.formGroup}>
                <label>Description</label>
                <textarea
                  value={editingItem.description || ''}
                  onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })}
                />
              </div>

              {editingItem.originalPrice && editingItem.originalPrice > editingItem.price && (
                <div className={styles.discountInfo}>
                  <strong>Discount Information:</strong>
                  <div>Save ${(editingItem.originalPrice - editingItem.price).toFixed(2)}</div>
                  <div>({Math.round(((editingItem.originalPrice - editingItem.price) / editingItem.originalPrice) * 100)}% OFF)</div>
                </div>
              )}
            </div>

            <div className={styles.modalFooter}>
              <button onClick={() => setEditingItem(null)} className={styles.cancelButton}>
                Cancel
              </button>
              <button onClick={handleSaveEdit} className={styles.saveButton}>
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PresetItemSelector;
