<!DOCTYPE html>
<html lang="zh-TW" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="KMS PC Receipt Maker - Professional computer hardware receipt generator / KMS電腦收據生成器 - 專業的電腦硬件收據生成工具">
    <meta name="keywords" content="receipt, computer, hardware, generator, 收據, 電腦, 硬件, 生成器">
    <meta name="author" content="KMS PC Store">

    <title>KMS PC Receipt Maker | 電腦收據生成器</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/receipt.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/print.css" media="print">

    <!-- Preload critical resources -->
    <link rel="preload" href="js/app.js" as="script">
    <link rel="preload" href="js/receipt.js" as="script">
</head>
<body class="kms-body">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="kms-skip-link" data-i18n="skip-to-content">跳到主要內容</a>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="kms-loading-overlay" aria-hidden="true">
        <div class="kms-loading-spinner">
            <div class="kms-spinner"></div>
            <p class="kms-loading-text" data-i18n="loading">載入中...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="kms-header" role="banner">
        <div class="kms-layout-container">
            <div class="kms-header-content">
                <!-- Logo and title -->
                <div class="kms-header-brand">
                    <img src="assets/images/logo.png" alt="KMS PC Store Logo" class="kms-logo" width="40" height="40">
                    <h1 class="kms-title">
                        <span class="kms-title-main" data-i18n="app-title">KMS PC Receipt Maker</span>
                        <span class="kms-title-sub" data-i18n="app-subtitle">電腦收據生成器</span>
                    </h1>
                </div>

                <!-- Header controls -->
                <div class="kms-header-controls">
                    <!-- Language switcher -->
                    <div class="kms-language-switcher">
                        <button type="button" class="kms-btn kms-btn-secondary kms-btn-sm"
                                id="language-toggle" aria-label="Switch language">
                            <span class="kms-lang-icon">🌐</span>
                            <span id="current-language">中文</span>
                        </button>
                    </div>

                    <!-- Theme switcher -->
                    <div class="kms-theme-switcher">
                        <button type="button" class="kms-btn kms-btn-secondary kms-btn-sm"
                                id="theme-toggle" aria-label="Switch theme">
                            <span class="kms-theme-icon">🌙</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main content -->
    <main id="main-content" class="kms-main" role="main">
        <div class="kms-layout-container">
            <!-- Alert messages -->
            <div id="alert-container" class="kms-alert-container" role="alert" aria-live="polite"></div>

            <!-- Main layout -->
            <div class="kms-layout-grid">
                <!-- Form section -->
                <section class="kms-form-section" aria-labelledby="form-section-title">
                    <div class="kms-section-header">
                        <h2 id="form-section-title" class="kms-section-title" data-i18n="receipt-generator">收據生成器</h2>
                        <div class="kms-section-actions">
                            <button type="button" class="kms-btn kms-btn-secondary kms-btn-sm"
                                    id="clear-form-btn" data-i18n="clear-form">清空表單</button>
                            <button type="button" class="kms-btn kms-btn-secondary kms-btn-sm"
                                    id="load-template-btn" data-i18n="load-template">載入模板</button>
                        </div>
                    </div>

                    <!-- Receipt form -->
                    <form id="receipt-form" class="kms-receipt-form" novalidate>
                        <!-- Store information -->
                        <fieldset class="form-fieldset">
                            <legend class="form-legend" data-i18n="store-info">商店資訊</legend>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="store-select" class="form-label" data-i18n="store-name">商店名稱</label>
                                    <select id="store-select" name="store_id" class="form-input form-select" required>
                                        <option value="" data-i18n="select-store">請選擇商店</option>
                                    </select>
                                    <div class="form-validation-message" role="alert"></div>
                                </div>

                                <div class="form-group">
                                    <label for="receipt-number" class="form-label" data-i18n="receipt-number">收據編號</label>
                                    <input type="text" id="receipt-number" name="receipt_number"
                                           class="form-input" placeholder="自動生成" readonly>
                                    <button type="button" class="kms-btn kms-btn-secondary kms-btn-sm"
                                            id="generate-receipt-number" data-i18n="generate">生成</button>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="receipt-date" class="form-label" data-i18n="receipt-date">收據日期</label>
                                    <input type="date" id="receipt-date" name="receipt_date"
                                           class="form-input" required>
                                    <div class="form-validation-message" role="alert"></div>
                                </div>

                                <div class="form-group">
                                    <label for="tax-rate" class="form-label" data-i18n="tax-rate">稅率 (%)</label>
                                    <input type="number" id="tax-rate" name="tax_rate"
                                           class="form-input" min="0" max="100" step="0.01" value="5">
                                    <div class="form-validation-message" role="alert"></div>
                                </div>
                            </div>
                        </fieldset>

                        <!-- Customer information -->
                        <fieldset class="form-fieldset">
                            <legend class="form-legend" data-i18n="customer-info">客戶資訊</legend>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="customer-search" class="form-label" data-i18n="customer-search">搜尋客戶</label>
                                    <div class="form-input-group">
                                        <input type="text" id="customer-search" name="customer_search"
                                               class="form-input" placeholder="輸入姓名或電話搜尋"
                                               data-i18n-placeholder="search-customer-placeholder">
                                        <button type="button" class="kms-btn kms-btn-secondary"
                                                id="new-customer-btn" data-i18n="new-customer">新客戶</button>
                                    </div>
                                    <div id="customer-suggestions" class="form-suggestions" role="listbox" aria-label="Customer suggestions"></div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="customer-name" class="form-label" data-i18n="customer-name">客戶姓名</label>
                                    <input type="text" id="customer-name" name="customer_name"
                                           class="form-input" required>
                                    <div class="form-validation-message" role="alert"></div>
                                </div>

                                <div class="form-group">
                                    <label for="customer-phone" class="form-label" data-i18n="customer-phone">客戶電話</label>
                                    <input type="tel" id="customer-phone" name="customer_phone"
                                           class="form-input" required>
                                    <div class="form-validation-message" role="alert"></div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="customer-email" class="form-label" data-i18n="customer-email">客戶郵箱</label>
                                    <input type="email" id="customer-email" name="customer_email"
                                           class="form-input">
                                    <div class="form-validation-message" role="alert"></div>
                                </div>

                                <div class="form-group">
                                    <label for="customer-address" class="form-label" data-i18n="customer-address">客戶地址</label>
                                    <input type="text" id="customer-address" name="customer_address"
                                           class="form-input">
                                </div>
                            </div>
                        </fieldset>

                        <!-- Receipt items -->
                        <fieldset class="form-fieldset">
                            <legend class="form-legend" data-i18n="receipt-items">收據項目</legend>

                            <!-- Add item form -->
                            <div class="form-add-item">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="item-category" class="form-label" data-i18n="item-category">項目分類</label>
                                        <select id="item-category" name="item_category" class="form-input form-select">
                                            <option value="" data-i18n="select-category">請選擇分類</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="item-name" class="form-label" data-i18n="item-name">項目名稱</label>
                                        <input type="text" id="item-name" name="item_name"
                                               class="form-input" required>
                                        <div class="form-validation-message" role="alert"></div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="item-price" class="form-label" data-i18n="item-price">單價</label>
                                        <input type="number" id="item-price" name="item_price"
                                               class="form-input" min="0" step="0.01" required>
                                        <div class="form-validation-message" role="alert"></div>
                                    </div>

                                    <div class="form-group">
                                        <label for="item-quantity" class="form-label" data-i18n="item-quantity">數量</label>
                                        <input type="number" id="item-quantity" name="item_quantity"
                                               class="form-input" min="1" value="1" required>
                                        <div class="form-validation-message" role="alert"></div>
                                    </div>

                                    <div class="form-group">
                                        <button type="button" class="kms-btn kms-btn-primary"
                                                id="add-item-btn" data-i18n="add-item">新增項目</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Items list -->
                            <div id="items-list" class="form-items-list">
                                <div class="form-items-empty" data-i18n="no-items">尚未新增任何項目</div>
                            </div>
                        </fieldset>

                        <!-- Form actions -->
                        <div class="form-actions">
                            <button type="button" class="kms-btn kms-btn-secondary"
                                    id="save-draft-btn" data-i18n="save-draft">儲存草稿</button>
                            <button type="submit" class="kms-btn kms-btn-primary"
                                    data-i18n="generate-receipt">生成收據</button>
                        </div>
                    </form>
                </section>

                <!-- Preview section -->
                <section class="kms-preview-section" aria-labelledby="preview-section-title">
                    <div class="kms-section-header">
                        <h2 id="preview-section-title" class="kms-section-title" data-i18n="receipt-preview">收據預覽</h2>
                        <div class="kms-section-actions">
                            <button type="button" class="kms-btn kms-btn-secondary kms-btn-sm"
                                    id="print-receipt-btn" disabled data-i18n="print-receipt">列印收據</button>
                            <button type="button" class="kms-btn kms-btn-secondary kms-btn-sm"
                                    id="save-pdf-btn" disabled data-i18n="save-pdf">儲存PDF</button>
                        </div>
                    </div>

                    <!-- Receipt preview -->
                    <div id="receipt-preview" class="receipt-preview">
                        <div class="receipt-preview-empty" data-i18n="preview-empty">
                            請填寫表單以查看收據預覽
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="kms-footer" role="contentinfo">
        <div class="kms-layout-container">
            <div class="kms-footer-content">
                <p class="kms-footer-text">
                    <span data-i18n="footer-copyright">© 2025 KMS PC Store. All rights reserved.</span>
                    <span class="kms-footer-separator">|</span>
                    <span data-i18n="footer-version">Version 1.0.0</span>
                </p>
            </div>
        </div>
    </footer>

    <!-- Modals and dialogs will be inserted here by JavaScript -->
    <div id="modal-container" class="kms-modal-container"></div>

    <!-- JavaScript Files -->
    <script src="js/utils.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/api.js"></script>
    <script src="js/forms.js"></script>
    <script src="js/receipt.js"></script>
    <script src="js/print.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
