.container {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
}

.tableContainer {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background-color: #ffffff;
}

.headerRow {
  background-color: #f9fafb;
  border-bottom: 2px solid #e5e7eb;
}

.nameHeader,
.priceHeader,
.quantityHeader,
.subtotalHeader,
.actionsHeader {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.priceHeader,
.quantityHeader,
.subtotalHeader {
  text-align: right;
}

.actionsHeader {
  text-align: center;
  width: 140px;
}

.emptyState {
  padding: 3rem 1.5rem;
  text-align: center;
  color: #6b7280;
}

.emptyIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
}

.emptyState p {
  margin: 0 0 1.5rem 0;
  color: #6b7280;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .tableContainer {
    display: none;
  }

  .table {
    display: block;
  }

  .headerRow {
    display: none;
  }

  .emptyState {
    padding: 2rem 1rem;
  }
}