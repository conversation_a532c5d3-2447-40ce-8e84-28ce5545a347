# KMS PC Receipt Maker - 部署指南 | Deployment Guide

## 快速部署 | Quick Deployment

### 1. 環境準備 | Environment Setup

#### XAMPP 部署 (推薦用於開發和測試)
1. 下載並安裝 [XAMPP](https://www.apachefriends.org/)
2. 啟動 Apache 和 MySQL 服務
3. 將項目文件複製到 `C:\xampp\htdocs\kms-receipt-maker\`

#### 生產環境部署
- **Web服務器**: Apache 2.4+ 或 Nginx 1.18+
- **PHP**: 8.0 或更高版本
- **MySQL**: 8.0 或更高版本
- **SSL證書**: 推薦用於生產環境

### 2. 數據庫設置 | Database Setup

#### 創建數據庫
```sql
CREATE DATABASE kms_receipt_maker CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 創建用戶（可選）
```sql
CREATE USER 'kms_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON kms_receipt_maker.* TO 'kms_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 導入數據庫結構
```bash
mysql -u root -p kms_receipt_maker < database/schema.sql
```

### 3. 配置文件設置 | Configuration

#### 數據庫配置 (config/database.php)
```php
<?php
define('DB_HOST', 'localhost');
define('DB_NAME', 'kms_receipt_maker');
define('DB_USER', 'kms_user');
define('DB_PASS', 'your_secure_password');
define('DB_CHARSET', 'utf8mb4');
?>
```

#### Web服務器配置

**Apache (.htaccess)**
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/$1.php [QSA,L]

# 安全設置
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

**Nginx**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/kms-receipt-maker;
    index index.html;

    location /api/ {
        try_files $uri $uri.php =404;
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location / {
        try_files $uri $uri/ =404;
    }
}
```

### 4. 權限設置 | Permissions

#### Linux/Unix 系統
```bash
# 設置文件權限
chmod 644 *.html *.md
chmod 644 css/* js/* api/*
chmod 755 config/ includes/ models/

# 設置目錄權限
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;

# 確保Web服務器可以讀取
chown -R www-data:www-data /var/www/kms-receipt-maker
```

#### Windows 系統
- 確保 IIS_IUSRS 或 IUSR 有讀取權限
- 對於 XAMPP，通常不需要特殊權限設置

### 5. 測試部署 | Test Deployment

#### 基本功能測試
1. 訪問 `http://your-domain/kms-receipt-maker/test.html`
2. 運行所有測試項目
3. 確保所有測試通過

#### API測試
```bash
# 測試商店API
curl -X GET http://your-domain/api/stores

# 測試數據庫連接
curl -X GET http://your-domain/api/test-db
```

#### 瀏覽器測試
- Chrome/Edge 最新版本
- Firefox 最新版本
- Safari (macOS/iOS)
- 移動設備瀏覽器

### 6. 性能優化 | Performance Optimization

#### PHP 優化
```ini
; php.ini 設置
memory_limit = 256M
max_execution_time = 30
upload_max_filesize = 10M
post_max_size = 10M

; OPcache 設置
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
```

#### MySQL 優化
```sql
-- 創建索引
CREATE INDEX idx_receipts_date ON receipts(receipt_date);
CREATE INDEX idx_receipts_store ON receipts(store_id);
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_receipt_items_receipt ON receipt_items(receipt_id);
```

#### 前端優化
- 啟用 Gzip 壓縮
- 設置瀏覽器緩存
- 壓縮 CSS 和 JavaScript 文件

### 7. 安全設置 | Security Configuration

#### SSL/HTTPS 設置
```apache
# 強制 HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

#### 安全頭部
```apache
Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
```

#### 文件保護
```apache
# 保護敏感文件
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

<Files "config/*">
    Order deny,allow
    Deny from all
</Files>
```

### 8. 備份策略 | Backup Strategy

#### 數據庫備份
```bash
#!/bin/bash
# 每日備份腳本
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u kms_user -p kms_receipt_maker > backup/db_backup_$DATE.sql
```

#### 文件備份
```bash
#!/bin/bash
# 文件備份腳本
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf backup/files_backup_$DATE.tar.gz --exclude='backup' .
```

### 9. 監控和日誌 | Monitoring and Logging

#### 錯誤日誌
```php
// 在 config/database.php 中添加
ini_set('log_errors', 1);
ini_set('error_log', '/var/log/kms-receipt-maker/error.log');
```

#### 訪問日誌分析
```bash
# 分析 Apache 訪問日誌
tail -f /var/log/apache2/access.log | grep kms-receipt-maker
```

### 10. 故障排除 | Troubleshooting

#### 常見問題解決

**問題**: 數據庫連接失敗
```bash
# 檢查 MySQL 服務
systemctl status mysql
# 檢查連接
mysql -u kms_user -p -e "SELECT 1"
```

**問題**: API 返回 404
```bash
# 檢查 URL 重寫
apache2ctl -M | grep rewrite
# 檢查 .htaccess 文件
```

**問題**: 權限錯誤
```bash
# 檢查文件權限
ls -la /var/www/kms-receipt-maker/
# 修復權限
chown -R www-data:www-data /var/www/kms-receipt-maker/
```

### 11. 維護計劃 | Maintenance Plan

#### 定期維護任務
- **每日**: 檢查錯誤日誌
- **每週**: 數據庫備份驗證
- **每月**: 安全更新檢查
- **每季**: 性能評估和優化

#### 更新流程
1. 備份當前版本
2. 在測試環境部署新版本
3. 運行測試套件
4. 部署到生產環境
5. 驗證功能正常

### 12. 擴展部署 | Scaling Deployment

#### 負載均衡
```nginx
upstream kms_backend {
    server ************:80;
    server ************:80;
}

server {
    location / {
        proxy_pass http://kms_backend;
    }
}
```

#### 數據庫集群
- 主從複製設置
- 讀寫分離配置
- 連接池優化

---

## 支持聯繫 | Support Contact

如需部署支持，請聯繫：
- 技術支持：<EMAIL>
- 緊急聯繫：+886-2-1234-5678

For deployment support, please contact:
- Technical Support: <EMAIL>
- Emergency Contact: +886-2-1234-5678
