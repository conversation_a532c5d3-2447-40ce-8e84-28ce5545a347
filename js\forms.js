/**
 * Form Handling
 * 表單處理模塊
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

'use strict';

// 表單驗證器類
class FormValidator {
    constructor() {
        this.rules = {};
        this.messages = {};
    }

    /**
     * 添加驗證規則
     * Add validation rule
     */
    addRule(field, rule, message) {
        if (!this.rules[field]) {
            this.rules[field] = [];
        }
        this.rules[field].push(rule);
        
        if (!this.messages[field]) {
            this.messages[field] = [];
        }
        this.messages[field].push(message);
    }

    /**
     * 驗證表單
     * Validate form
     */
    validate(data) {
        const errors = {};

        Object.keys(this.rules).forEach(field => {
            const fieldRules = this.rules[field];
            const fieldMessages = this.messages[field];
            const value = data[field];

            for (let i = 0; i < fieldRules.length; i++) {
                const rule = fieldRules[i];
                const message = fieldMessages[i];

                if (!rule(value)) {
                    errors[field] = message;
                    break; // 只顯示第一個錯誤
                }
            }
        });

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    /**
     * 清除規則
     * Clear rules
     */
    clear() {
        this.rules = {};
        this.messages = {};
    }
}

// 表單處理類
class FormHandler {
    constructor(formSelector) {
        this.form = Utils.dom.find(formSelector);
        this.validator = new FormValidator();
        this.data = {};
        this.items = [];
        this.categories = [];
        this.stores = [];

        if (this.form) {
            this.init();
        }
    }

    /**
     * 初始化
     * Initialize
     */
    init() {
        this.setupValidation();
        this.bindEvents();
        this.loadInitialData();
    }

    /**
     * 設置驗證規則
     * Setup validation rules
     */
    setupValidation() {
        // 商店驗證
        this.validator.addRule('store_id', 
            value => value && value.trim() !== '', 
            t('required-field'));

        // 收據日期驗證
        this.validator.addRule('receipt_date', 
            value => value && value.trim() !== '', 
            t('required-field'));

        // 客戶姓名驗證
        this.validator.addRule('customer_name', 
            value => value && value.trim() !== '', 
            t('required-field'));

        // 客戶電話驗證
        this.validator.addRule('customer_phone', 
            value => value && value.trim() !== '', 
            t('required-field'));
        
        this.validator.addRule('customer_phone', 
            value => !value || Utils.validatePhone(value), 
            t('invalid-phone'));

        // 客戶郵箱驗證（可選）
        this.validator.addRule('customer_email', 
            value => !value || Utils.validateEmail(value), 
            t('invalid-email'));

        // 稅率驗證
        this.validator.addRule('tax_rate', 
            value => !isNaN(value) && value >= 0 && value <= 100, 
            t('invalid-number'));
    }

    /**
     * 綁定事件
     * Bind events
     */
    bindEvents() {
        // 表單提交
        Utils.dom.on(this.form, 'submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        });

        // 實時驗證
        const inputs = Utils.dom.findAll('input, select, textarea', this.form);
        inputs.forEach(input => {
            Utils.dom.on(input, 'blur', () => {
                this.validateField(input);
            });

            Utils.dom.on(input, 'input', Utils.debounce(() => {
                this.clearFieldError(input);
            }, 300));
        });

        // 商店選擇變更
        const storeSelect = Utils.dom.find('#store-select');
        if (storeSelect) {
            Utils.dom.on(storeSelect, 'change', () => {
                this.handleStoreChange();
            });
        }

        // 客戶搜索
        const customerSearch = Utils.dom.find('#customer-search');
        if (customerSearch) {
            Utils.dom.on(customerSearch, 'input', Utils.debounce(() => {
                this.handleCustomerSearch();
            }, 300));
        }

        // 新增項目
        const addItemBtn = Utils.dom.find('#add-item-btn');
        if (addItemBtn) {
            Utils.dom.on(addItemBtn, 'click', () => {
                this.handleAddItem();
            });
        }

        // 生成收據編號
        const generateReceiptBtn = Utils.dom.find('#generate-receipt-number');
        if (generateReceiptBtn) {
            Utils.dom.on(generateReceiptBtn, 'click', () => {
                this.generateReceiptNumber();
            });
        }

        // 清空表單
        const clearFormBtn = Utils.dom.find('#clear-form-btn');
        if (clearFormBtn) {
            Utils.dom.on(clearFormBtn, 'click', () => {
                this.clearForm();
            });
        }

        // 稅率變更時重新計算
        const taxRateInput = Utils.dom.find('#tax-rate');
        if (taxRateInput) {
            Utils.dom.on(taxRateInput, 'input', Utils.debounce(() => {
                this.updateCalculations();
            }, 300));
        }
    }

    /**
     * 載入初始數據
     * Load initial data
     */
    async loadInitialData() {
        try {
            // 載入商店列表
            await this.loadStores();
            
            // 載入分類列表
            await this.loadCategories();
            
            // 設置默認日期
            this.setDefaultDate();
            
            // 生成收據編號
            this.generateReceiptNumber();
            
        } catch (error) {
            console.error('Failed to load initial data:', error);
        }
    }

    /**
     * 載入商店列表
     * Load stores list
     */
    async loadStores() {
        try {
            const response = await API.stores.getActiveList();
            if (response && response.success && response.data) {
                this.stores = response.data;
                this.populateStoreSelect(response.data);
            } else {
                console.warn('Failed to load stores:', response);
            }
        } catch (error) {
            console.error('Error loading stores:', error);
            API.handleError(error);
        }
    }

    /**
     * 填充商店選擇器
     * Populate store selector
     */
    populateStoreSelect(stores) {
        const storeSelect = Utils.dom.find('#store-select');
        if (!storeSelect) return;

        // 清空現有選項（保留第一個默認選項）
        const defaultOption = storeSelect.querySelector('option[value=""]');
        storeSelect.innerHTML = '';
        if (defaultOption) {
            storeSelect.appendChild(defaultOption);
        }

        // 添加商店選項
        stores.forEach(store => {
            const option = Utils.dom.create('option', {
                value: store.id
            }, store.name);
            storeSelect.appendChild(option);
        });
    }

    /**
     * 載入分類列表
     * Load categories list
     */
    async loadCategories() {
        try {
            const response = await API.categories.getAll();
            if (response && response.success && response.data) {
                this.populateCategorySelect(response.data);
            } else {
                console.warn('Failed to load categories:', response);
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            API.handleError(error);
        }
    }

    /**
     * 填充分類選擇器
     * Populate category selector
     */
    populateCategorySelect(categories) {
        // 保存分類數據
        this.categories = categories;

        const categorySelect = Utils.dom.find('#item-category');
        if (!categorySelect) return;

        // 清空現有選項（保留第一個默認選項）
        const defaultOption = categorySelect.querySelector('option[value=""]');
        categorySelect.innerHTML = '';
        if (defaultOption) {
            categorySelect.appendChild(defaultOption);
        }

        // 按類型分組
        const hardware = categories.filter(cat => cat.type === 'hardware');
        const service = categories.filter(cat => cat.type === 'service');

        // 添加硬件分類
        if (hardware.length > 0) {
            const hardwareGroup = Utils.dom.create('optgroup', {
                label: t('hardware') || 'Hardware'
            });
            hardware.forEach(cat => {
                const option = Utils.dom.create('option', {
                    value: cat.id
                }, i18n.getCurrentLanguage() === 'zh-TW' ? cat.name_zh : cat.name_en);
                hardwareGroup.appendChild(option);
            });
            categorySelect.appendChild(hardwareGroup);
        }

        // 添加服務分類
        if (service.length > 0) {
            const serviceGroup = Utils.dom.create('optgroup', {
                label: t('service') || 'Service'
            });
            service.forEach(cat => {
                const option = Utils.dom.create('option', {
                    value: cat.id
                }, i18n.getCurrentLanguage() === 'zh-TW' ? cat.name_zh : cat.name_en);
                serviceGroup.appendChild(option);
            });
            categorySelect.appendChild(serviceGroup);
        }
    }

    /**
     * 設置默認日期
     * Set default date
     */
    setDefaultDate() {
        const dateInput = Utils.dom.find('#receipt-date');
        if (dateInput && !dateInput.value) {
            dateInput.value = Utils.formatDate(new Date(), 'YYYY-MM-DD');
        }
    }

    /**
     * 生成收據編號
     * Generate receipt number
     */
    generateReceiptNumber() {
        const receiptNumberInput = Utils.dom.find('#receipt-number');
        if (receiptNumberInput) {
            receiptNumberInput.value = Utils.generateReceiptNumber();
        }
    }

    /**
     * 處理商店變更
     * Handle store change
     */
    async handleStoreChange() {
        const storeSelect = Utils.dom.find('#store-select');
        if (!storeSelect || !storeSelect.value) return;

        try {
            const response = await API.stores.getById(storeSelect.value);
            if (response.success) {
                // 可以在這裡更新商店相關信息
                console.log('Store selected:', response.data);
            }
        } catch (error) {
            API.handleError(error);
        }
    }

    /**
     * 處理客戶搜索
     * Handle customer search
     */
    async handleCustomerSearch() {
        const searchInput = Utils.dom.find('#customer-search');
        if (!searchInput || !searchInput.value.trim()) {
            this.hideSuggestions();
            return;
        }

        try {
            const response = await API.customers.search(searchInput.value.trim());
            if (response.success) {
                this.showSuggestions(response.data);
            }
        } catch (error) {
            console.error('Customer search failed:', error);
        }
    }

    /**
     * 顯示客戶建議
     * Show customer suggestions
     */
    showSuggestions(customers) {
        const suggestionsContainer = Utils.dom.find('#customer-suggestions');
        if (!suggestionsContainer) return;

        suggestionsContainer.innerHTML = '';

        if (customers.length === 0) {
            suggestionsContainer.style.display = 'none';
            return;
        }

        customers.forEach(customer => {
            const item = Utils.dom.create('div', {
                className: 'form-suggestion-item',
                dataset: { customerId: customer.id }
            });

            const name = Utils.dom.create('div', {
                className: 'form-suggestion-name'
            }, customer.name);

            const details = Utils.dom.create('div', {
                className: 'form-suggestion-details'
            }, `${customer.phone} ${customer.email || ''}`);

            item.appendChild(name);
            item.appendChild(details);

            Utils.dom.on(item, 'click', () => {
                this.selectCustomer(customer);
            });

            suggestionsContainer.appendChild(item);
        });

        suggestionsContainer.style.display = 'block';
    }

    /**
     * 隱藏建議
     * Hide suggestions
     */
    hideSuggestions() {
        const suggestionsContainer = Utils.dom.find('#customer-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }

    /**
     * 選擇客戶
     * Select customer
     */
    selectCustomer(customer) {
        // 填充客戶信息
        const fields = {
            'customer-name': customer.name,
            'customer-phone': customer.phone,
            'customer-email': customer.email || '',
            'customer-address': customer.address || ''
        };

        Object.keys(fields).forEach(fieldId => {
            const field = Utils.dom.find(`#${fieldId}`);
            if (field) {
                field.value = fields[fieldId];
            }
        });

        // 清空搜索框並隱藏建議
        const searchInput = Utils.dom.find('#customer-search');
        if (searchInput) {
            searchInput.value = '';
        }
        this.hideSuggestions();
    }

    /**
     * 處理新增項目
     * Handle add item
     */
    handleAddItem() {
        const itemData = this.getItemFormData();
        
        // 驗證項目數據
        const validation = this.validateItemData(itemData);
        if (!validation.isValid) {
            this.showItemErrors(validation.errors);
            return;
        }

        // 添加項目
        const item = {
            id: Utils.generateId('item'),
            ...itemData,
            price: parseFloat(itemData.price),
            quantity: parseInt(itemData.quantity)
        };

        this.items.push(item);
        this.renderItems();
        this.clearItemForm();
        this.updateCalculations();
    }

    /**
     * 獲取項目表單數據
     * Get item form data
     */
    getItemFormData() {
        return {
            category_id: Utils.dom.find('#item-category')?.value || '',
            name: Utils.dom.find('#item-name')?.value || '',
            price: Utils.dom.find('#item-price')?.value || '0',
            quantity: Utils.dom.find('#item-quantity')?.value || '1'
        };
    }

    /**
     * 驗證項目數據
     * Validate item data
     */
    validateItemData(data) {
        const errors = {};

        if (!data.name.trim()) {
            errors.name = t('required-field');
        }

        if (!data.price || isNaN(data.price) || parseFloat(data.price) <= 0) {
            errors.price = t('invalid-number');
        }

        if (!data.quantity || isNaN(data.quantity) || parseInt(data.quantity) <= 0) {
            errors.quantity = t('invalid-number');
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    /**
     * 顯示項目錯誤
     * Show item errors
     */
    showItemErrors(errors) {
        Object.keys(errors).forEach(field => {
            const input = Utils.dom.find(`#item-${field}`);
            if (input) {
                this.showFieldError(input, errors[field]);
            }
        });
    }

    /**
     * 渲染項目列表
     * Render items list
     */
    renderItems() {
        const itemsList = Utils.dom.find('#items-list');
        if (!itemsList) return;

        if (this.items.length === 0) {
            itemsList.innerHTML = `<div class="form-items-empty" data-i18n="no-items">${t('no-items')}</div>`;
            return;
        }

        itemsList.innerHTML = '';

        this.items.forEach((item, index) => {
            const itemElement = this.createItemElement(item, index);
            itemsList.appendChild(itemElement);
        });
    }

    /**
     * 創建項目元素
     * Create item element
     */
    createItemElement(item, index) {
        const itemDiv = Utils.dom.create('div', {
            className: 'form-item',
            dataset: { itemId: item.id }
        });

        const total = Utils.calc.calculateItemSubtotal(item.price, item.quantity);

        itemDiv.innerHTML = `
            <div class="form-item-info">
                <div class="form-item-name">${Utils.sanitizeHtml(item.name)}</div>
                <div class="form-item-category">${this.getCategoryName(item.category_id)}</div>
            </div>
            <div class="form-item-price">${Utils.formatCurrency(item.price)}</div>
            <div class="form-item-quantity">${item.quantity}</div>
            <div class="form-item-total">${Utils.formatCurrency(total)}</div>
            <div class="form-item-actions">
                <button type="button" class="kms-btn kms-btn-secondary kms-btn-sm" onclick="formHandler.editItem('${item.id}')" data-i18n="edit">${t('edit')}</button>
                <button type="button" class="kms-btn kms-btn-error kms-btn-sm" onclick="formHandler.deleteItem('${item.id}')" data-i18n="delete">${t('delete')}</button>
            </div>
        `;

        return itemDiv;
    }

    /**
     * 獲取分類名稱
     * Get category name
     */
    getCategoryName(categoryId) {
        if (!categoryId || !this.categories) {
            return '';
        }

        const category = this.categories.find(cat => cat.id == categoryId);
        if (category) {
            return i18n.getCurrentLanguage() === 'zh-TW' ? category.name_zh : category.name_en;
        }

        return '';
    }

    /**
     * 編輯項目
     * Edit item
     */
    editItem(itemId) {
        const item = this.items.find(item => item.id === itemId);
        if (!item) return;

        // 填充表單
        Utils.dom.find('#item-category').value = item.category_id;
        Utils.dom.find('#item-name').value = item.name;
        Utils.dom.find('#item-price').value = item.price;
        Utils.dom.find('#item-quantity').value = item.quantity;

        // 刪除原項目
        this.deleteItem(itemId);
    }

    /**
     * 刪除項目
     * Delete item
     */
    deleteItem(itemId) {
        this.items = this.items.filter(item => item.id !== itemId);
        this.renderItems();
        this.updateCalculations();
    }

    /**
     * 清空項目表單
     * Clear item form
     */
    clearItemForm() {
        const fields = ['#item-category', '#item-name', '#item-price', '#item-quantity'];
        fields.forEach(selector => {
            const field = Utils.dom.find(selector);
            if (field) {
                if (selector === '#item-quantity') {
                    field.value = '1';
                } else {
                    field.value = '';
                }
            }
        });
    }

    /**
     * 更新計算
     * Update calculations
     */
    updateCalculations() {
        const subtotal = this.items.reduce((sum, item) => {
            return sum + Utils.calc.calculateItemSubtotal(item.price, item.quantity);
        }, 0);

        const taxRate = parseFloat(Utils.dom.find('#tax-rate')?.value || 0);
        const taxAmount = Utils.calc.calculateTax(subtotal, taxRate);
        const total = Utils.calc.calculateTotal(subtotal, taxAmount);

        // 觸發計算更新事件
        window.dispatchEvent(new CustomEvent('calculationsUpdated', {
            detail: { subtotal, taxAmount, total, items: this.items }
        }));
    }

    /**
     * 驗證字段
     * Validate field
     */
    validateField(input) {
        const fieldName = input.name;
        const value = input.value;

        if (this.validator.rules[fieldName]) {
            const validation = this.validator.validate({ [fieldName]: value });
            
            if (!validation.isValid) {
                this.showFieldError(input, validation.errors[fieldName]);
            } else {
                this.clearFieldError(input);
            }
        }
    }

    /**
     * 顯示字段錯誤
     * Show field error
     */
    showFieldError(input, message) {
        Utils.dom.addClass(input, 'form-input-error');
        
        const errorElement = input.parentNode.querySelector('.form-validation-message');
        if (errorElement) {
            errorElement.textContent = message;
        }
    }

    /**
     * 清除字段錯誤
     * Clear field error
     */
    clearFieldError(input) {
        Utils.dom.removeClass(input, 'form-input-error');
        
        const errorElement = input.parentNode.querySelector('.form-validation-message');
        if (errorElement) {
            errorElement.textContent = '';
        }
    }

    /**
     * 處理表單提交
     * Handle form submit
     */
    async handleSubmit() {
        const formData = this.getFormData();
        const validation = this.validator.validate(formData);

        if (!validation.isValid) {
            this.showValidationErrors(validation.errors);
            return;
        }

        if (this.items.length === 0) {
            alert(t('no-items'));
            return;
        }

        try {
            // 準備收據數據
            const receiptData = {
                ...formData,
                items: this.items
            };

            // 創建收據
            const response = await API.receipts.create(receiptData);
            
            if (response.success) {
                API.showSuccess(t('receipt-created'));
                
                // 觸發收據創建事件
                window.dispatchEvent(new CustomEvent('receiptCreated', {
                    detail: { receipt: response.data }
                }));
            }
        } catch (error) {
            API.handleError(error);
        }
    }

    /**
     * 獲取表單數據
     * Get form data
     */
    getFormData() {
        const formData = new FormData(this.form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        return data;
    }

    /**
     * 顯示驗證錯誤
     * Show validation errors
     */
    showValidationErrors(errors) {
        Object.keys(errors).forEach(fieldName => {
            const input = this.form.querySelector(`[name="${fieldName}"]`);
            if (input) {
                this.showFieldError(input, errors[fieldName]);
            }
        });
    }

    /**
     * 清空表單
     * Clear form
     */
    clearForm() {
        if (confirm(t('confirm-clear'))) {
            this.form.reset();
            this.items = [];
            this.renderItems();
            this.setDefaultDate();
            this.generateReceiptNumber();
            this.updateCalculations();
        }
    }
}

// 創建全局表單處理器實例
window.formHandler = null;

// 在DOM載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    window.formHandler = new FormHandler('#receipt-form');
});
