import React, { useState, useEffect } from 'react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { useReceipt } from '../../hooks/useReceipt';
import type { ReceiptItem } from '../../types';
import styles from './ReceiptTemplateManager.module.css';

interface ReceiptTemplate {
  id: string;
  name: string;
  items: ReceiptItem[];
  createdAt: Date;
  updatedAt: Date;
}

interface ReceiptTemplateManagerProps {
  className?: string;
}

export const ReceiptTemplateManager: React.FC<ReceiptTemplateManagerProps> = ({
  className = '',
}) => {
  const { receiptData, addItem, clearItems } = useReceipt();
  const [templates, setTemplates] = useState<ReceiptTemplate[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [templateName, setTemplateName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(null);
  const [editingTemplateName, setEditingTemplateName] = useState('');

  // 從 localStorage 載入模板
  useEffect(() => {
    const savedTemplates = localStorage.getItem('receiptTemplates');
    if (savedTemplates) {
      try {
        const parsed = JSON.parse(savedTemplates);
        setTemplates(parsed.map((t: any) => ({
          ...t,
          createdAt: new Date(t.createdAt),
          updatedAt: new Date(t.updatedAt)
        })));
      } catch (error) {
        console.error('Failed to load templates:', error);
      }
    }
  }, []);

  // 保存模板到 localStorage
  const saveTemplates = (newTemplates: ReceiptTemplate[]) => {
    localStorage.setItem('receiptTemplates', JSON.stringify(newTemplates));
    setTemplates(newTemplates);
  };

  // 保存當前收據為模板
  const handleSaveTemplate = async () => {
    if (!templateName.trim()) {
      alert('Please enter a template name');
      return;
    }

    if (receiptData.items.length === 0) {
      alert('Please add at least one item before saving as template');
      return;
    }

    setIsSaving(true);

    try {
      const newTemplate: ReceiptTemplate = {
        id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: templateName.trim(),
        items: receiptData.items.map(item => ({ ...item })), // 深拷貝
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const updatedTemplates = [...templates, newTemplate];
      saveTemplates(updatedTemplates);
      setTemplateName('');
      alert(`Template "${newTemplate.name}" saved successfully!`);
    } catch (error) {
      console.error('Failed to save template:', error);
      alert('Failed to save template. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // 載入模板
  const handleLoadTemplate = (template: ReceiptTemplate) => {
    if (receiptData.items.length > 0) {
      const confirmed = window.confirm(
        'Loading this template will replace all current items. Continue?'
      );
      if (!confirmed) return;
    }

    // 清除現有項目
    clearItems();

    // 添加模板項目
    template.items.forEach(item => {
      addItem({
        name: item.name,
        category: item.category,
        price: item.price,
        originalPrice: item.originalPrice,
        quantity: item.quantity,
        hidePriceOnReceipt: item.hidePriceOnReceipt
      });
    });

    alert(`Template "${template.name}" loaded successfully!`);
  };

  // 刪除模板
  const handleDeleteTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete template "${template.name}"?`
    );
    if (!confirmed) return;

    const updatedTemplates = templates.filter(t => t.id !== templateId);
    saveTemplates(updatedTemplates);
    alert(`Template "${template.name}" deleted successfully!`);
  };

  // 開始編輯模板名稱
  const handleStartEditTemplateName = (templateId: string, currentName: string) => {
    setEditingTemplateId(templateId);
    setEditingTemplateName(currentName);
  };

  // 保存模板名稱編輯
  const handleSaveTemplateName = () => {
    if (!editingTemplateId || !editingTemplateName.trim()) return;

    const updatedTemplates = templates.map(template =>
      template.id === editingTemplateId
        ? { ...template, name: editingTemplateName.trim(), updatedAt: new Date() }
        : template
    );

    saveTemplates(updatedTemplates);
    setEditingTemplateId(null);
    setEditingTemplateName('');
  };

  // 取消編輯模板名稱
  const handleCancelEditTemplateName = () => {
    setEditingTemplateId(null);
    setEditingTemplateName('');
  };

  // 格式化日期
  const formatDate = (date: Date) => {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className={`${styles.container} ${className}`}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h4 className={styles.title}>Receipt Templates</h4>
          <p className={styles.description}>
            Save and reuse receipt item lists for faster creation
          </p>
        </div>
        <Button
          variant="outline"
          size="small"
          onClick={() => setIsExpanded(!isExpanded)}
          className={styles.toggleButton}
        >
          {isExpanded ? 'Hide Templates' : 'Show Templates'}
        </Button>
      </div>

      {isExpanded && (
        <div className={styles.content}>
          {/* 保存新模板 */}
          <div className={styles.saveSection}>
            <h5 className={styles.sectionTitle}>Save Current Items as Template</h5>
            <div className={styles.saveForm}>
              <Input
                type="text"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                placeholder="Enter template name..."
                className={styles.templateNameInput}
              />
              <Button
                variant="primary"
                size="small"
                onClick={handleSaveTemplate}
                disabled={isSaving || !templateName.trim() || receiptData.items.length === 0}
                className={styles.saveButton}
              >
                {isSaving ? 'Saving...' : 'Save Template'}
              </Button>
            </div>
            <p className={styles.saveHint}>
              Current items: {receiptData.items.length} 
              {receiptData.items.length === 0 && ' (Add items first)'}
            </p>
          </div>

          {/* 模板列表 */}
          <div className={styles.templatesSection}>
            <h5 className={styles.sectionTitle}>Saved Templates ({templates.length})</h5>
            
            {templates.length === 0 ? (
              <div className={styles.emptyState}>
                <div className={styles.emptyIcon}>📋</div>
                <div className={styles.emptyText}>No templates saved yet</div>
                <div className={styles.emptyHint}>
                  Add some items and save your first template above
                </div>
              </div>
            ) : (
              <div className={styles.templatesList}>
                {templates.map((template) => (
                  <div key={template.id} className={styles.templateCard}>
                    <div className={styles.templateHeader}>
                      <div className={styles.templateInfo}>
                        {editingTemplateId === template.id ? (
                          <div className={styles.templateNameEdit}>
                            <Input
                              type="text"
                              value={editingTemplateName}
                              onChange={(e) => setEditingTemplateName(e.target.value)}
                              className={styles.templateNameInput}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') handleSaveTemplateName();
                                if (e.key === 'Escape') handleCancelEditTemplateName();
                              }}
                              autoFocus
                            />
                            <div className={styles.templateNameActions}>
                              <Button
                                variant="primary"
                                size="small"
                                onClick={handleSaveTemplateName}
                                disabled={!editingTemplateName.trim()}
                              >
                                Save
                              </Button>
                              <Button
                                variant="outline"
                                size="small"
                                onClick={handleCancelEditTemplateName}
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div
                            className={styles.templateName}
                            onClick={() => handleStartEditTemplateName(template.id, template.name)}
                            title="Click to edit template name"
                          >
                            {template.name}
                          </div>
                        )}
                        <div className={styles.templateMeta}>
                          {template.items.length} items • Created {formatDate(template.createdAt)}
                        </div>
                      </div>
                      <div className={styles.templateActions}>
                        <Button
                          variant="primary"
                          size="small"
                          onClick={() => handleLoadTemplate(template)}
                          className={styles.loadButton}
                        >
                          Load
                        </Button>
                        <Button
                          variant="danger"
                          size="small"
                          onClick={() => handleDeleteTemplate(template.id)}
                          className={styles.deleteButton}
                        >
                          Delete
                        </Button>
                      </div>
                    </div>
                    
                    <div className={styles.templateItems}>
                      {template.items.slice(0, 3).map((item, index) => (
                        <div key={index} className={styles.templateItem}>
                          <span className={styles.itemName}>{item.name}</span>
                          <span className={styles.itemPrice}>
                            ${item.price.toFixed(2)}
                            {item.originalPrice && item.originalPrice > item.price && (
                              <span className={styles.originalPrice}>
                                ${item.originalPrice.toFixed(2)}
                              </span>
                            )}
                          </span>
                        </div>
                      ))}
                      {template.items.length > 3 && (
                        <div className={styles.moreItems}>
                          +{template.items.length - 3} more items
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
