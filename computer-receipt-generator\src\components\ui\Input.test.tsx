import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Input } from './Input';

describe('Input', () => {
  it('應該正確渲染基本輸入框', () => {
    render(<Input placeholder="Enter text" />);
    
    const input = screen.getByPlaceholderText('Enter text');
    expect(input).toBeInTheDocument();
  });

  it('應該顯示標籤', () => {
    render(<Input label="Username" />);
    
    const label = screen.getByText('Username');
    const input = screen.getByLabelText('Username');
    
    expect(label).toBeInTheDocument();
    expect(input).toBeInTheDocument();
  });

  it('應該顯示錯誤訊息', () => {
    render(<Input label="Email" error="Invalid email format" />);
    
    const input = screen.getByLabelText('Email');
    const errorMessage = screen.getByText('Invalid email format');
    
    expect(input.className).toContain('error');
    expect(errorMessage).toBeInTheDocument();
    expect(errorMessage.className).toContain('errorText');
  });

  it('應該顯示輔助文字', () => {
    render(<Input label="Password" helperText="At least 8 characters" />);
    
    const helperText = screen.getByText('At least 8 characters');
    expect(helperText).toBeInTheDocument();
    expect(helperText.className).toContain('helperText');
  });

  it('錯誤訊息應該優先於輔助文字', () => {
    render(
      <Input 
        label="Email" 
        error="Invalid email" 
        helperText="Enter your email address" 
      />
    );
    
    expect(screen.getByText('Invalid email')).toBeInTheDocument();
    expect(screen.queryByText('Enter your email address')).not.toBeInTheDocument();
  });

  it('應該支援全寬度', () => {
    render(<Input fullWidth data-testid="full-width-input" />);
    
    const input = screen.getByTestId('full-width-input');
    const container = input.parentElement?.parentElement; // inputWrapper -> container
    expect(container?.className).toContain('fullWidth');
  });

  it('應該支援開始圖示', () => {
    const StartIcon = () => <span data-testid="start-icon">🔍</span>;
    render(<Input startIcon={<StartIcon />} />);
    
    const input = screen.getByRole('textbox');
    const icon = screen.getByTestId('start-icon');
    
    expect(input.className).toContain('hasStartIcon');
    expect(icon).toBeInTheDocument();
  });

  it('應該支援結束圖示', () => {
    const EndIcon = () => <span data-testid="end-icon">✓</span>;
    render(<Input endIcon={<EndIcon />} />);
    
    const input = screen.getByRole('textbox');
    const icon = screen.getByTestId('end-icon');
    
    expect(input.className).toContain('hasEndIcon');
    expect(icon).toBeInTheDocument();
  });

  it('應該正確處理輸入事件', () => {
    const handleChange = vi.fn();
    render(<Input onChange={handleChange} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'test input' } });
    
    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(input).toHaveValue('test input');
  });

  it('應該支援禁用狀態', () => {
    render(<Input disabled />);
    
    const input = screen.getByRole('textbox');
    expect(input).toBeDisabled();
  });

  it('應該支援不同的輸入類型', () => {
    const { rerender } = render(<Input type="email" />);
    expect(screen.getByRole('textbox')).toHaveAttribute('type', 'email');

    rerender(<Input type="password" />);
    expect(screen.getByDisplayValue('')).toHaveAttribute('type', 'password');

    rerender(<Input type="number" />);
    expect(screen.getByRole('spinbutton')).toHaveAttribute('type', 'number');
  });

  it('應該支援自訂 className', () => {
    render(<Input className="custom-input" data-testid="custom-input" />);
    
    const input = screen.getByTestId('custom-input');
    const container = input.parentElement?.parentElement; // inputWrapper -> container
    expect(container?.className).toContain('custom-input');
  });

  it('應該傳遞其他 HTML 屬性', () => {
    render(<Input data-testid="custom-input" maxLength={10} />);
    
    const input = screen.getByTestId('custom-input');
    expect(input).toHaveAttribute('maxLength', '10');
  });

  it('應該生成唯一的 ID', () => {
    render(
      <div>
        <Input label="First" />
        <Input label="Second" />
      </div>
    );
    
    const firstInput = screen.getByLabelText('First');
    const secondInput = screen.getByLabelText('Second');
    
    expect(firstInput.id).toBeTruthy();
    expect(secondInput.id).toBeTruthy();
    expect(firstInput.id).not.toBe(secondInput.id);
  });

  it('應該使用提供的 ID', () => {
    render(<Input id="custom-id" label="Custom ID" />);
    
    const input = screen.getByLabelText('Custom ID');
    expect(input).toHaveAttribute('id', 'custom-id');
  });
});