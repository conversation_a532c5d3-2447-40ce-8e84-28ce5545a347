.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  box-sizing: border-box;
  animation: fadeIn 0.2s ease-out;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

.small {
  width: 100%;
  max-width: 400px;
}

.medium {
  width: 100%;
  max-width: 600px;
}

.large {
  width: 100%;
  max-width: 800px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 0 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  flex-shrink: 0;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background: #f3f4f6;
  color: #374151;
}

.closeButton:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

.content {
  padding: 0 1.5rem;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  margin-top: 1.5rem;
  flex-shrink: 0;
}

.cancelButton {
  /* Inherits from Button component */
}

.confirmButton {
  /* Inherits from Button component */
}

/* 動畫效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .overlay {
    padding: 0.5rem;
  }
  
  .modal {
    max-height: 95vh;
  }
  
  .header {
    padding: 1rem 1rem 0 1rem;
    margin-bottom: 1rem;
  }
  
  .content {
    padding: 0 1rem;
  }
  
  .footer {
    padding: 1rem;
    margin-top: 1rem;
    flex-direction: column-reverse;
  }
  
  .footer button {
    width: 100%;
  }
}

/* 高對比模式支援 */
@media (prefers-contrast: high) {
  .overlay {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .modal {
    border: 2px solid #000;
  }
  
  .header {
    border-bottom-width: 2px;
  }
  
  .footer {
    border-top-width: 2px;
  }
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {
  .overlay,
  .modal {
    animation: none;
  }
  
  .closeButton {
    transition: none;
  }
}
