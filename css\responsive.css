/**
 * Responsive Styles
 * 響應式樣式文件
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

/* Breakpoints */
/* 
 * Mobile: 0-767px
 * Tablet: 768-1023px  
 * Desktop: 1024px+
 */

/* Mobile Styles (0-767px) */
@media (max-width: 767px) {
    /* Layout */
    .kms-layout-container {
        padding: 0 var(--kms-spacing-3);
    }
    
    .kms-layout-grid {
        grid-template-columns: 1fr;
        gap: var(--kms-spacing-4);
        margin-top: var(--kms-spacing-4);
    }
    
    /* Header */
    .kms-header {
        padding: var(--kms-spacing-3) 0;
    }
    
    .kms-header-content {
        flex-direction: column;
        gap: var(--kms-spacing-3);
        align-items: flex-start;
    }
    
    .kms-header-brand {
        width: 100%;
    }
    
    .kms-title-main {
        font-size: var(--kms-font-size-lg);
    }
    
    .kms-title-sub {
        font-size: var(--kms-font-size-xs);
    }
    
    .kms-header-controls {
        width: 100%;
        justify-content: flex-end;
    }
    
    /* Sections */
    .kms-form-section,
    .kms-preview-section {
        padding: var(--kms-spacing-4);
    }
    
    .kms-section-header {
        flex-direction: column;
        gap: var(--kms-spacing-3);
        align-items: flex-start;
    }
    
    .kms-section-title {
        font-size: var(--kms-font-size-xl);
    }
    
    .kms-section-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    
    /* Main content */
    .kms-main {
        padding: var(--kms-spacing-4) 0;
    }
    
    /* Buttons */
    .kms-btn {
        font-size: var(--kms-font-size-xs);
        padding: var(--kms-spacing-2) var(--kms-spacing-3);
    }
    
    .kms-btn-sm {
        padding: var(--kms-spacing-1) var(--kms-spacing-2);
        font-size: 10px;
    }
    
    /* Forms */
    .form-fieldset {
        padding: var(--kms-spacing-4);
    }
    
    .form-legend {
        font-size: var(--kms-font-size-base);
    }
    
    .form-input,
    .form-select {
        font-size: var(--kms-font-size-xs);
        padding: var(--kms-spacing-2) var(--kms-spacing-3);
    }
    
    .form-label {
        font-size: var(--kms-font-size-xs);
    }
    
    /* Receipt preview */
    .receipt-document {
        font-size: 11px;
    }
    
    .receipt-header,
    .receipt-customer,
    .receipt-items,
    .receipt-summary,
    .receipt-footer {
        padding: var(--kms-spacing-3);
    }
    
    .receipt-store-name {
        font-size: 18px;
    }
    
    .receipt-number {
        font-size: 14px;
    }
    
    /* Hide non-essential elements on mobile */
    .mobile-hide {
        display: none !important;
    }
}

/* Tablet Styles (768-1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    /* Layout */
    .kms-layout-container {
        padding: 0 var(--kms-spacing-4);
    }
    
    .kms-layout-grid {
        grid-template-columns: 1fr;
        gap: var(--kms-spacing-6);
    }
    
    /* Sections */
    .kms-form-section,
    .kms-preview-section {
        padding: var(--kms-spacing-5);
    }
    
    /* Forms - Keep two columns for most form rows */
    .form-row.tablet-single {
        grid-template-columns: 1fr;
    }
    
    /* Receipt preview adjustments */
    .receipt-document {
        font-size: 13px;
    }
    
    /* Show tablet-specific elements */
    .tablet-show {
        display: block !important;
    }
    
    .tablet-hide {
        display: none !important;
    }
}

/* Desktop Styles (1024px+) */
@media (min-width: 1024px) {
    /* Layout */
    .kms-layout-container {
        max-width: 1400px;
        padding: 0 var(--kms-spacing-6);
    }
    
    .kms-layout-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--kms-spacing-10);
    }
    
    /* Sections */
    .kms-form-section,
    .kms-preview-section {
        padding: var(--kms-spacing-8);
    }
    
    /* Forms - Allow three columns for some form rows */
    .form-row.desktop-triple {
        grid-template-columns: 1fr 1fr 1fr;
    }
    
    /* Receipt preview */
    .receipt-document {
        font-size: 14px;
    }
    
    /* Show desktop-specific elements */
    .desktop-show {
        display: block !important;
    }
    
    .desktop-hide {
        display: none !important;
    }
}

/* Large Desktop Styles (1400px+) */
@media (min-width: 1400px) {
    .kms-layout-container {
        max-width: 1600px;
    }
    
    .kms-layout-grid {
        gap: var(--kms-spacing-12);
    }
    
    .kms-form-section,
    .kms-preview-section {
        padding: var(--kms-spacing-10);
    }
}

/* Orientation-specific styles */
@media (orientation: landscape) and (max-height: 600px) {
    /* Reduce vertical spacing on landscape mobile devices */
    .kms-header {
        padding: var(--kms-spacing-2) 0;
    }
    
    .kms-main {
        padding: var(--kms-spacing-3) 0;
    }
    
    .kms-form-section,
    .kms-preview-section {
        padding: var(--kms-spacing-3);
    }
    
    .form-fieldset {
        padding: var(--kms-spacing-3);
    }
}

/* High DPI / Retina displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Ensure crisp rendering on high DPI displays */
    .kms-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
    
    /* Adjust border widths for better appearance */
    .form-input,
    .form-select,
    .kms-btn {
        border-width: 0.5px;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .kms-spinner {
        animation: none;
    }
}

/* Dark mode preferences */
@media (prefers-color-scheme: dark) {
    :root:not([data-theme="light"]) {
        --kms-color-primary: #3b82f6;
        --kms-color-primary-hover: #2563eb;
        --kms-color-primary-light: #1e3a8a;
        
        --kms-bg-primary: var(--kms-color-gray-900);
        --kms-bg-secondary: var(--kms-color-gray-800);
        --kms-bg-tertiary: var(--kms-color-gray-700);
        
        --kms-text-primary: var(--kms-color-gray-100);
        --kms-text-secondary: var(--kms-color-gray-300);
        --kms-text-muted: var(--kms-color-gray-400);
        
        --kms-border-color: var(--kms-color-gray-700);
        --kms-border-color-hover: var(--kms-color-gray-600);
    }
}

/* Print-specific responsive adjustments */
@media print {
    /* Hide responsive utility classes that shouldn't print */
    .mobile-hide,
    .tablet-hide,
    .desktop-hide {
        display: block !important;
    }
    
    .mobile-show,
    .tablet-show,
    .desktop-show {
        display: block !important;
    }
}

/* Container queries (future-proofing) */
@supports (container-type: inline-size) {
    .responsive-container {
        container-type: inline-size;
    }
    
    @container (max-width: 400px) {
        .responsive-stack {
            flex-direction: column;
        }
    }
}

/* Utility classes for responsive design */
.responsive-flex {
    display: flex;
    flex-wrap: wrap;
    gap: var(--kms-spacing-2);
}

.responsive-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--kms-spacing-4);
}

.responsive-text-center {
    text-align: center;
}

@media (min-width: 768px) {
    .responsive-text-center {
        text-align: left;
    }
}

/* Aspect ratio utilities */
.responsive-aspect-16-9 {
    aspect-ratio: 16 / 9;
}

.responsive-aspect-4-3 {
    aspect-ratio: 4 / 3;
}

.responsive-aspect-1-1 {
    aspect-ratio: 1 / 1;
}

/* Fallback for browsers that don't support aspect-ratio */
@supports not (aspect-ratio: 1 / 1) {
    .responsive-aspect-16-9 {
        position: relative;
        padding-bottom: 56.25%; /* 9/16 * 100% */
    }
    
    .responsive-aspect-4-3 {
        position: relative;
        padding-bottom: 75%; /* 3/4 * 100% */
    }
    
    .responsive-aspect-1-1 {
        position: relative;
        padding-bottom: 100%;
    }
    
    .responsive-aspect-16-9 > *,
    .responsive-aspect-4-3 > *,
    .responsive-aspect-1-1 > * {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}
