/**
 * Receipt Styles
 * 收據樣式文件
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

/* Receipt Preview Container */
.receipt-preview {
    background-color: var(--kms-bg-primary);
    border: 1px solid var(--kms-border-color);
    border-radius: var(--kms-radius-lg);
    overflow: hidden;
    box-shadow: var(--kms-shadow-md);
}

.receipt-preview-empty {
    padding: var(--kms-spacing-12);
    text-align: center;
    color: var(--kms-text-muted);
    font-style: italic;
    background-color: var(--kms-bg-secondary);
}

/* Receipt Document */
.receipt-document {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--kms-color-white);
    color: var(--kms-color-gray-900);
    font-family: var(--kms-font-family-sans);
    font-size: 14px;
    line-height: 1.4;
    box-shadow: var(--kms-shadow-lg);
}

/* Receipt Header */
.receipt-header {
    padding: var(--kms-spacing-6);
    border-bottom: 2px solid var(--kms-color-gray-200);
    background-color: var(--kms-color-gray-50);
}

.receipt-header-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--kms-spacing-6);
    align-items: start;
}

.receipt-store-info {
    display: flex;
    flex-direction: column;
    gap: var(--kms-spacing-2);
}

.receipt-store-name {
    font-size: 24px;
    font-weight: var(--kms-font-weight-bold);
    color: var(--kms-color-primary);
    margin-bottom: var(--kms-spacing-2);
}

.receipt-store-details {
    font-size: 13px;
    color: var(--kms-color-gray-600);
    line-height: 1.5;
}

.receipt-meta {
    text-align: right;
    font-size: 13px;
}

.receipt-number {
    font-size: 18px;
    font-weight: var(--kms-font-weight-bold);
    color: var(--kms-color-primary);
    margin-bottom: var(--kms-spacing-2);
}

.receipt-date {
    color: var(--kms-color-gray-600);
}

/* Receipt Customer Info */
.receipt-customer {
    padding: var(--kms-spacing-4) var(--kms-spacing-6);
    background-color: var(--kms-color-white);
    border-bottom: 1px solid var(--kms-color-gray-200);
}

.receipt-customer-title {
    font-size: 16px;
    font-weight: var(--kms-font-weight-semibold);
    color: var(--kms-color-gray-800);
    margin-bottom: var(--kms-spacing-3);
}

.receipt-customer-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--kms-spacing-4);
    font-size: 13px;
    color: var(--kms-color-gray-600);
}

.receipt-customer-field {
    display: flex;
    flex-direction: column;
    gap: var(--kms-spacing-1);
}

.receipt-customer-label {
    font-weight: var(--kms-font-weight-medium);
    color: var(--kms-color-gray-700);
}

.receipt-customer-value {
    color: var(--kms-color-gray-900);
}

/* Receipt Items Table */
.receipt-items {
    padding: var(--kms-spacing-6);
}

.receipt-items-title {
    font-size: 16px;
    font-weight: var(--kms-font-weight-semibold);
    color: var(--kms-color-gray-800);
    margin-bottom: var(--kms-spacing-4);
}

.receipt-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
}

.receipt-table th {
    background-color: var(--kms-color-gray-100);
    color: var(--kms-color-gray-700);
    font-weight: var(--kms-font-weight-semibold);
    padding: var(--kms-spacing-3) var(--kms-spacing-2);
    text-align: left;
    border-bottom: 2px solid var(--kms-color-gray-300);
}

.receipt-table th:first-child {
    padding-left: var(--kms-spacing-4);
}

.receipt-table th:last-child {
    padding-right: var(--kms-spacing-4);
    text-align: right;
}

.receipt-table td {
    padding: var(--kms-spacing-3) var(--kms-spacing-2);
    border-bottom: 1px solid var(--kms-color-gray-200);
    vertical-align: top;
}

.receipt-table td:first-child {
    padding-left: var(--kms-spacing-4);
}

.receipt-table td:last-child {
    padding-right: var(--kms-spacing-4);
    text-align: right;
}

.receipt-table tbody tr:hover {
    background-color: var(--kms-color-gray-50);
}

.receipt-item-name {
    font-weight: var(--kms-font-weight-medium);
    color: var(--kms-color-gray-900);
    margin-bottom: var(--kms-spacing-1);
}

.receipt-item-category {
    font-size: 11px;
    color: var(--kms-color-gray-500);
    background-color: var(--kms-color-gray-100);
    padding: 2px 6px;
    border-radius: var(--kms-radius-sm);
    display: inline-block;
}

.receipt-item-price,
.receipt-item-quantity,
.receipt-item-total {
    font-weight: var(--kms-font-weight-medium);
    color: var(--kms-color-gray-900);
}

/* Receipt Summary */
.receipt-summary {
    padding: var(--kms-spacing-6);
    background-color: var(--kms-color-gray-50);
    border-top: 2px solid var(--kms-color-gray-200);
}

.receipt-summary-table {
    width: 100%;
    max-width: 300px;
    margin-left: auto;
    font-size: 14px;
}

.receipt-summary-table td {
    padding: var(--kms-spacing-2) var(--kms-spacing-4);
    border: none;
}

.receipt-summary-table .receipt-summary-label {
    color: var(--kms-color-gray-600);
    text-align: left;
}

.receipt-summary-table .receipt-summary-value {
    color: var(--kms-color-gray-900);
    font-weight: var(--kms-font-weight-medium);
    text-align: right;
}

.receipt-summary-subtotal {
    border-bottom: 1px solid var(--kms-color-gray-300);
}

.receipt-summary-total {
    border-top: 2px solid var(--kms-color-primary);
    background-color: var(--kms-color-primary-light);
}

.receipt-summary-total .receipt-summary-label,
.receipt-summary-total .receipt-summary-value {
    font-size: 16px;
    font-weight: var(--kms-font-weight-bold);
    color: var(--kms-color-primary);
}

/* Receipt Footer */
.receipt-footer {
    padding: var(--kms-spacing-6);
    text-align: center;
    border-top: 1px solid var(--kms-color-gray-200);
    background-color: var(--kms-color-white);
}

.receipt-footer-text {
    font-size: 12px;
    color: var(--kms-color-gray-500);
    line-height: 1.6;
}

.receipt-footer-signature {
    margin-top: var(--kms-spacing-8);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--kms-spacing-8);
}

.receipt-signature-section {
    text-align: center;
}

.receipt-signature-line {
    border-bottom: 1px solid var(--kms-color-gray-400);
    height: 40px;
    margin-bottom: var(--kms-spacing-2);
}

.receipt-signature-label {
    font-size: 12px;
    color: var(--kms-color-gray-600);
}

/* Receipt Logo */
.receipt-logo {
    max-width: 80px;
    max-height: 60px;
    object-fit: contain;
}

/* Receipt Status Badge */
.receipt-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: var(--kms-radius-sm);
    font-size: 11px;
    font-weight: var(--kms-font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.receipt-status-draft {
    background-color: #fef3c7;
    color: #92400e;
}

.receipt-status-completed {
    background-color: #dcfce7;
    color: #166534;
}

.receipt-status-cancelled {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Receipt Notes */
.receipt-notes {
    padding: var(--kms-spacing-4) var(--kms-spacing-6);
    background-color: var(--kms-color-gray-50);
    border-top: 1px solid var(--kms-color-gray-200);
}

.receipt-notes-title {
    font-size: 14px;
    font-weight: var(--kms-font-weight-semibold);
    color: var(--kms-color-gray-800);
    margin-bottom: var(--kms-spacing-2);
}

.receipt-notes-content {
    font-size: 13px;
    color: var(--kms-color-gray-600);
    line-height: 1.5;
}

/* Receipt Watermark */
.receipt-watermark {
    position: relative;
    overflow: hidden;
}

.receipt-watermark::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-size: 48px;
    font-weight: var(--kms-font-weight-bold);
    color: rgba(0, 0, 0, 0.05);
    z-index: 1;
    pointer-events: none;
}

.receipt-watermark.receipt-watermark-draft::before {
    content: 'DRAFT';
}

.receipt-watermark.receipt-watermark-cancelled::before {
    content: 'CANCELLED';
    color: rgba(220, 38, 38, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .receipt-document {
        font-size: 12px;
    }
    
    .receipt-header-content {
        grid-template-columns: 1fr;
        gap: var(--kms-spacing-4);
    }
    
    .receipt-meta {
        text-align: left;
    }
    
    .receipt-customer-details {
        grid-template-columns: 1fr;
        gap: var(--kms-spacing-2);
    }
    
    .receipt-table {
        font-size: 11px;
    }
    
    .receipt-table th,
    .receipt-table td {
        padding: var(--kms-spacing-2) var(--kms-spacing-1);
    }
    
    .receipt-footer-signature {
        grid-template-columns: 1fr;
        gap: var(--kms-spacing-4);
    }
}
