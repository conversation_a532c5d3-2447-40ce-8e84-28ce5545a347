.section {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
}

.description {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .formGrid {
    grid-template-columns: 1fr;
  }
}

.preview {
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}

.preview h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.previewCard {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
}

.storeName {
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: #111827;
}

.storeDetails {
  color: #374151;
}

.storeDetails > div {
  margin-bottom: 0.25rem;
}

.storeDetails > div:last-child {
  margin-bottom: 0;
}