import React, { useState, useRef, useCallback } from 'react';
import type { LogoSettings } from './LogoManager';
import styles from './LogoDisplay.module.css';

interface LogoDisplayProps {
  logoSettings: LogoSettings;
  onLogoChange?: (settings: LogoSettings) => void;
  className?: string;
  isDraggable?: boolean;
}

export const LogoDisplay: React.FC<LogoDisplayProps> = ({
  logoSettings,
  onLogoChange,
  className = '',
  isDraggable = false,
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const logoRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  if (!logoSettings.imageUrl) {
    return null;
  }

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!isDraggable || !onLogoChange) return;

    e.preventDefault();
    setIsDragging(true);
    setDragStart({
      x: e.clientX - (logoSettings.position.x * (containerRef.current?.offsetWidth || 1) / 100),
      y: e.clientY - (logoSettings.position.y * (containerRef.current?.offsetHeight || 1) / 100)
    });
  }, [isDraggable, onLogoChange, logoSettings.position]);

  const handleResizeMouseDown = useCallback((e: React.MouseEvent) => {
    if (!isDraggable || !onLogoChange) return;

    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: logoSettings.size.width,
      height: logoSettings.size.height
    });
  }, [isDraggable, onLogoChange, logoSettings.size]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!containerRef.current || !onLogoChange) return;

    if (isDragging) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const newX = ((e.clientX - dragStart.x) / containerRect.width) * 100;
      const newY = ((e.clientY - dragStart.y) / containerRect.height) * 100;

      onLogoChange({
        ...logoSettings,
        position: {
          x: Math.max(0, Math.min(100 - logoSettings.size.width, newX)),
          y: Math.max(0, Math.min(100 - logoSettings.size.height, newY))
        }
      });
    } else if (isResizing) {
      const deltaX = e.clientX - resizeStart.x;
      const deltaY = e.clientY - resizeStart.y;
      const containerRect = containerRef.current.getBoundingClientRect();

      const newWidth = Math.max(5, Math.min(50, resizeStart.width + (deltaX / containerRect.width) * 100));
      const newHeight = Math.max(5, Math.min(50, resizeStart.height + (deltaY / containerRect.height) * 100));

      onLogoChange({
        ...logoSettings,
        size: {
          width: newWidth,
          height: newHeight
        }
      });
    }
  }, [isDragging, isResizing, dragStart, resizeStart, logoSettings, onLogoChange]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
  }, []);

  React.useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  const logoStyle: React.CSSProperties = {
    position: 'absolute',
    left: `${logoSettings.position.x}%`,
    top: `${logoSettings.position.y}%`,
    width: `${logoSettings.size.width}%`,
    height: `${logoSettings.size.height}%`,
    opacity: logoSettings.opacity,
    zIndex: 10,
    cursor: isDraggable ? (isDragging ? 'grabbing' : 'grab') : 'default',
    pointerEvents: isDraggable ? 'auto' : 'none',
  };

  return (
    <div ref={containerRef} className={styles.logoWrapper}>
      <div
        ref={logoRef}
        className={`${styles.logoContainer} ${className} ${isDraggable ? styles.draggable : ''}`}
        style={logoStyle}
        onMouseDown={handleMouseDown}
      >
        <img
          src={logoSettings.imageUrl}
          alt="Receipt Logo"
          className={styles.logoImage}
          draggable={false}
        />
        {isDraggable && (
          <div
            className={styles.resizeHandle}
            onMouseDown={handleResizeMouseDown}
          />
        )}
      </div>
    </div>
  );
};
