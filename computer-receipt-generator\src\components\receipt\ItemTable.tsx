import React from 'react';
import type { ReceiptItem } from '../../types';
import { formatCurrency, calculateItemSubtotal } from '../../utils/calculations';
import { getDiscountPercentage } from '../../data/presetItems';
import styles from './ItemTable.module.css';

interface ItemTableProps {
  items: ReceiptItem[];
}

export const ItemTable: React.FC<ItemTableProps> = ({ items }) => {
  const getCategoryDisplayName = (category: ReceiptItem['category']): string => {
    return category;
  };

  if (items.length === 0) {
    return (
      <div className={styles.emptyState}>
        <p className={styles.emptyMessage}>No items added yet</p>
      </div>
    );
  }

  return (
    <div className={styles.tableContainer}>
      <table className={styles.itemTable}>
        <thead>
          <tr className={styles.headerRow}>
            <th className={styles.nameHeader}>Item Name</th>
            <th className={styles.categoryHeader}>Category</th>
            <th className={styles.quantityHeader}>Quantity</th>
            <th className={styles.priceHeader}>Unit Price</th>
            <th className={styles.subtotalHeader}>Subtotal</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item, index) => {
            const discountPercentage = getDiscountPercentage(item.price, item.originalPrice);
            const hasDiscount = discountPercentage > 0;

            return (
              <tr key={item.id} className={`${styles.itemRow} ${index % 2 === 0 ? styles.evenRow : styles.oddRow}`}>
                <td className={styles.nameCell}>
                  <div className={styles.itemName}>
                    {item.name}
                    {hasDiscount && (
                      <span className={styles.discountBadge}>
                        -{discountPercentage}% OFF
                      </span>
                    )}
                  </div>
                </td>
                <td className={styles.categoryCell}>
                  <span className={`${styles.categoryBadge} ${styles[`category${item.category}`]}`}>
                    {getCategoryDisplayName(item.category)}
                  </span>
                </td>
                <td className={styles.quantityCell}>
                  <span className={styles.quantity}>{item.quantity}</span>
                </td>
                <td className={styles.priceCell}>
                  {item.hidePriceOnReceipt ? (
                    <span className={styles.price}>N/A</span>
                  ) : (
                    <div className={styles.priceContainer}>
                      <span className={styles.currentPrice}>
                        {formatCurrency(item.price)}
                      </span>
                      {hasDiscount && (
                        <span className={styles.originalPrice}>
                          {formatCurrency(item.originalPrice!)}
                        </span>
                      )}
                    </div>
                  )}
                </td>
                <td className={styles.subtotalCell}>
                  <span className={styles.subtotal}>
                    {item.hidePriceOnReceipt ? 'N/A' : formatCurrency(calculateItemSubtotal(item))}
                  </span>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};