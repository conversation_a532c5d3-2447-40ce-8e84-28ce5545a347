.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.titleSection {
  flex: 1;
  min-width: 300px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
}

.infoSection {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem 1rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.featureIcon {
  font-size: 1.2rem;
}

.featureText {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .header {
    padding: 1.5rem 0;
  }
  
  .container {
    flex-direction: column;
    text-align: center;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .infoSection {
    justify-content: center;
    gap: 1rem;
  }
  
  .feature {
    padding: 0.5rem 0.75rem;
  }
  
  .featureText {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.75rem;
  }
  
  .infoSection {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .feature {
    justify-content: center;
  }
}

/* 列印時隱藏 */
@media print {
  .header {
    display: none;
  }
}