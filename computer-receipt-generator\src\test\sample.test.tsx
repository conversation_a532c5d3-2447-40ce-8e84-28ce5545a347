import { describe, it, expect } from 'vitest';
import { render, screen } from './utils';
import { createMockReceiptItem, createMockStoreInfo, createMockCustomerInfo } from './utils';

describe('Testing Environment Setup', () => {
  it('should render a simple component', () => {
    const TestComponent = () => <div>Hello Test World</div>;
    render(<TestComponent />);
    expect(screen.getByText('Hello Test World')).toBeInTheDocument();
  });

  it('should create mock data correctly', () => {
    const mockItem = createMockReceiptItem();
    expect(mockItem).toHaveProperty('id');
    expect(mockItem).toHaveProperty('name');
    expect(mockItem).toHaveProperty('category');
    expect(mockItem).toHaveProperty('price');
    expect(mockItem).toHaveProperty('quantity');
    expect(typeof mockItem.price).toBe('number');
    expect(mockItem.price).toBeGreaterThan(0);
  });

  it('should create mock store info correctly', () => {
    const mockStore = createMockStoreInfo();
    expect(mockStore).toHaveProperty('name');
    expect(mockStore).toHaveProperty('address');
    expect(mockStore).toHaveProperty('phone');
    expect(mockStore).toHaveProperty('email');
    expect(mockStore.name).toBe('Test Computer Store');
  });

  it('should create mock customer info correctly', () => {
    const mockCustomer = createMockCustomerInfo();
    expect(mockCustomer).toHaveProperty('name');
    expect(mockCustomer).toHaveProperty('phone');
    expect(mockCustomer).toHaveProperty('email');
    expect(mockCustomer.name).toBe('John Doe');
  });

  it('should have access to testing utilities', () => {
    expect(screen).toBeDefined();
    expect(render).toBeDefined();
  });

  it('should have mocked window functions', () => {
    expect(window.print).toBeDefined();
    expect(typeof window.print).toBe('function');
  });
});