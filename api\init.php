<?php
/**
 * API Initialization
 * API初始化文件
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

// 定義應用程序常量
define('KMS_RECEIPT_MAKER', true);
define('DEBUG', true); // 生產環境中設置為 false

// 設置錯誤報告
if (DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 設置時區
date_default_timezone_set('Asia/Taipei');

// 設置字符編碼
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');

// 包含必要的文件
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/includes/functions.php';
require_once dirname(__DIR__) . '/includes/validation.php';

// 包含模型文件
require_once dirname(__DIR__) . '/models/BaseModel.php';
require_once dirname(__DIR__) . '/models/Store.php';
require_once dirname(__DIR__) . '/models/Customer.php';
require_once dirname(__DIR__) . '/models/Category.php';
require_once dirname(__DIR__) . '/models/Receipt.php';
require_once dirname(__DIR__) . '/models/ReceiptItem.php';

// 設置CORS頭部
setCorsHeaders();

// 檢查數據庫連接
if (!checkDatabaseConnection()) {
    sendError('Database connection failed', 500);
}

// 設置內容類型
header('Content-Type: application/json; charset=utf-8');

/**
 * API路由處理器
 * API Route Handler
 */
class ApiRouter {
    private $routes = [];
    private $method;
    private $path;

    public function __construct() {
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->path = $this->getPath();
    }

    /**
     * 獲取請求路徑
     * Get request path
     * 
     * @return string
     */
    private function getPath() {
        $path = $_SERVER['REQUEST_URI'];
        
        // 移除查詢字符串
        if (($pos = strpos($path, '?')) !== false) {
            $path = substr($path, 0, $pos);
        }
        
        // 移除API前綴
        $path = str_replace('/api', '', $path);
        
        // 確保以 / 開頭
        if (!str_starts_with($path, '/')) {
            $path = '/' . $path;
        }
        
        return $path;
    }

    /**
     * 註冊路由
     * Register route
     * 
     * @param string $method HTTP方法
     * @param string $pattern 路徑模式
     * @param callable $handler 處理函數
     */
    public function register($method, $pattern, $handler) {
        $this->routes[] = [
            'method' => strtoupper($method),
            'pattern' => $pattern,
            'handler' => $handler
        ];
    }

    /**
     * GET路由
     * GET route
     */
    public function get($pattern, $handler) {
        $this->register('GET', $pattern, $handler);
    }

    /**
     * POST路由
     * POST route
     */
    public function post($pattern, $handler) {
        $this->register('POST', $pattern, $handler);
    }

    /**
     * PUT路由
     * PUT route
     */
    public function put($pattern, $handler) {
        $this->register('PUT', $pattern, $handler);
    }

    /**
     * DELETE路由
     * DELETE route
     */
    public function delete($pattern, $handler) {
        $this->register('DELETE', $pattern, $handler);
    }

    /**
     * 執行路由
     * Execute routing
     */
    public function run() {
        foreach ($this->routes as $route) {
            if ($route['method'] !== $this->method) {
                continue;
            }

            $matches = [];
            $pattern = '#^' . str_replace(['/', '{id}', '{slug}'], ['\/', '(\d+)', '([a-zA-Z0-9_-]+)'], $route['pattern']) . '$#';
            
            if (preg_match($pattern, $this->path, $matches)) {
                // 移除完整匹配
                array_shift($matches);
                
                try {
                    call_user_func_array($route['handler'], $matches);
                    return;
                } catch (Exception $e) {
                    logMessage("Route handler error: " . $e->getMessage(), 'ERROR');
                    sendError('Internal server error', 500);
                }
            }
        }

        // 沒有找到匹配的路由
        sendError('Endpoint not found', 404);
    }
}

/**
 * 獲取請求數據並清理
 * Get and sanitize request data
 * 
 * @return array
 */
function getCleanRequestData() {
    $data = getRequestData();
    return sanitizeInput($data);
}

/**
 * 驗證必需參數
 * Validate required parameters
 * 
 * @param array $data 數據
 * @param array $required 必需參數
 * @return bool
 */
function validateRequiredParams($data, $required) {
    $missing = [];
    
    foreach ($required as $param) {
        if (!isset($data[$param]) || trim($data[$param]) === '') {
            $missing[] = $param;
        }
    }
    
    if (!empty($missing)) {
        sendError('Missing required parameters: ' . implode(', ', $missing), 400);
        return false;
    }
    
    return true;
}

/**
 * 驗證數字參數
 * Validate numeric parameter
 * 
 * @param mixed $value 值
 * @param string $name 參數名
 * @return int
 */
function validateNumericParam($value, $name) {
    if (!is_numeric($value) || $value <= 0) {
        sendError("Parameter '{$name}' must be a positive number", 400);
    }
    
    return (int)$value;
}

/**
 * 分頁參數處理
 * Handle pagination parameters
 * 
 * @param array $data 請求數據
 * @return array
 */
function handlePagination($data) {
    $page = isset($data['page']) ? max(1, (int)$data['page']) : 1;
    $perPage = isset($data['per_page']) ? max(1, min(100, (int)$data['per_page'])) : 10;
    
    return ['page' => $page, 'per_page' => $perPage];
}

/**
 * 記錄API請求
 * Log API request
 * 
 * @param string $endpoint 端點
 * @param string $method 方法
 * @param array $data 數據
 */
function logApiRequest($endpoint, $method, $data = []) {
    $ip = getClientIP();
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $dataStr = !empty($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : '';
    
    $message = "API Request - {$method} {$endpoint} - IP: {$ip} - UA: {$userAgent}";
    if ($dataStr) {
        $message .= " - Data: {$dataStr}";
    }
    
    logMessage($message, 'INFO', 'api.log');
}

/**
 * 處理異常
 * Handle exception
 * 
 * @param Exception $e 異常
 */
function handleException($e) {
    logMessage("Exception: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(), 'ERROR');
    
    if (DEBUG) {
        sendError($e->getMessage(), 500, [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
    } else {
        sendError('Internal server error', 500);
    }
}

// 設置異常處理器
set_exception_handler('handleException');

// 創建全局路由器實例
$router = new ApiRouter();
?>
