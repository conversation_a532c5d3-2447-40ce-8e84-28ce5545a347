import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { PrintButton } from './PrintButton';

// Mock window.print
const mockPrint = vi.fn();
Object.defineProperty(window, 'print', {
  writable: true,
  value: mockPrint,
});

describe('PrintButton', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with default text', () => {
    render(<PrintButton />);
    expect(screen.getByRole('button', { name: '列印收據' })).toBeInTheDocument();
  });

  it('calls window.print when clicked', () => {
    const onAfterPrint = vi.fn();
    render(<PrintButton onAfterPrint={onAfterPrint} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(mockPrint).toHaveBeenCalledTimes(1);
    expect(onAfterPrint).toHaveBeenCalledTimes(1);
  });

  it('calls onBeforePrint callback', () => {
    const onBeforePrint = vi.fn();
    render(<PrintButton onBeforePrint={onBeforePrint} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(onBeforePrint).toHaveBeenCalledTimes(1);
  });
});