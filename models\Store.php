<?php
/**
 * Store Model
 * 商店模型類
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

// 防止直接訪問
if (!defined('KMS_RECEIPT_MAKER')) {
    die('Direct access not permitted');
}

require_once 'BaseModel.php';

/**
 * 商店模型類
 * Store Model Class
 */
class Store extends BaseModel {
    protected $table = 'stores';
    protected $fillable = [
        'name',
        'address', 
        'phone',
        'email',
        'logo_url',
        'tax_id',
        'website',
        'is_active'
    ];

    /**
     * 獲取所有啟用的商店
     * Get all active stores
     * 
     * @return array
     */
    public function getActiveStores() {
        return $this->findAll(['is_active' => 1], 'name ASC');
    }

    /**
     * 根據郵箱查找商店
     * Find store by email
     * 
     * @param string $email 郵箱
     * @return array|false
     */
    public function findByEmail($email) {
        return $this->findOne(['email' => $email]);
    }

    /**
     * 檢查郵箱是否已存在
     * Check if email exists
     * 
     * @param string $email 郵箱
     * @param int $excludeId 排除的ID（用於更新時檢查）
     * @return bool
     */
    public function emailExists($email, $excludeId = null) {
        $conditions = ['email' => $email];
        
        if ($excludeId) {
            $sql = "SELECT COUNT(*) as count FROM `{$this->table}` WHERE `email` = :email AND `id` != :exclude_id";
            $result = $this->db->fetchRow($sql, ['email' => $email, 'exclude_id' => $excludeId]);
            return (int)$result['count'] > 0;
        }
        
        return $this->exists($conditions);
    }

    /**
     * 創建商店
     * Create store
     * 
     * @param array $data 商店數據
     * @return array 結果
     */
    public function createStore($data) {
        // 驗證數據
        $errors = validateStoreData($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        // 檢查郵箱是否已存在
        if ($this->emailExists($data['email'])) {
            return [
                'success' => false, 
                'errors' => ['email' => '此郵箱已被使用 / This email is already in use']
            ];
        }

        try {
            $storeId = $this->create($data);
            $store = $this->findById($storeId);
            
            return [
                'success' => true,
                'data' => $store,
                'message' => '商店創建成功 / Store created successfully'
            ];
        } catch (Exception $e) {
            logMessage("Store creation failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '創建商店失敗 / Failed to create store']
            ];
        }
    }

    /**
     * 更新商店
     * Update store
     * 
     * @param int $id 商店ID
     * @param array $data 商店數據
     * @return array 結果
     */
    public function updateStore($id, $data) {
        // 檢查商店是否存在
        $store = $this->findById($id);
        if (!$store) {
            return [
                'success' => false,
                'errors' => ['general' => '商店不存在 / Store not found']
            ];
        }

        // 驗證數據
        $errors = validateStoreData($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        // 檢查郵箱是否已被其他商店使用
        if ($this->emailExists($data['email'], $id)) {
            return [
                'success' => false,
                'errors' => ['email' => '此郵箱已被其他商店使用 / This email is already used by another store']
            ];
        }

        try {
            $this->update($id, $data);
            $updatedStore = $this->findById($id);
            
            return [
                'success' => true,
                'data' => $updatedStore,
                'message' => '商店更新成功 / Store updated successfully'
            ];
        } catch (Exception $e) {
            logMessage("Store update failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '更新商店失敗 / Failed to update store']
            ];
        }
    }

    /**
     * 刪除商店
     * Delete store
     * 
     * @param int $id 商店ID
     * @return array 結果
     */
    public function deleteStore($id) {
        // 檢查商店是否存在
        $store = $this->findById($id);
        if (!$store) {
            return [
                'success' => false,
                'errors' => ['general' => '商店不存在 / Store not found']
            ];
        }

        // 檢查是否有關聯的收據
        $receiptCount = $this->db->fetchRow(
            "SELECT COUNT(*) as count FROM receipts WHERE store_id = :store_id",
            ['store_id' => $id]
        );

        if ($receiptCount['count'] > 0) {
            // 如果有關聯收據，執行軟刪除
            try {
                $this->update($id, ['is_active' => 0]);
                return [
                    'success' => true,
                    'message' => '商店已停用（因為有關聯的收據） / Store deactivated (has associated receipts)'
                ];
            } catch (Exception $e) {
                logMessage("Store soft delete failed: " . $e->getMessage(), 'ERROR');
                return [
                    'success' => false,
                    'errors' => ['general' => '停用商店失敗 / Failed to deactivate store']
                ];
            }
        } else {
            // 如果沒有關聯收據，執行硬刪除
            try {
                $this->delete($id);
                return [
                    'success' => true,
                    'message' => '商店刪除成功 / Store deleted successfully'
                ];
            } catch (Exception $e) {
                logMessage("Store delete failed: " . $e->getMessage(), 'ERROR');
                return [
                    'success' => false,
                    'errors' => ['general' => '刪除商店失敗 / Failed to delete store']
                ];
            }
        }
    }

    /**
     * 獲取商店統計信息
     * Get store statistics
     * 
     * @param int $id 商店ID
     * @return array
     */
    public function getStoreStats($id) {
        $sql = "
            SELECT 
                COUNT(r.id) as total_receipts,
                COALESCE(SUM(r.total), 0) as total_revenue,
                COALESCE(AVG(r.total), 0) as avg_receipt_value,
                COUNT(CASE WHEN r.receipt_date >= CURDATE() - INTERVAL 30 DAY THEN 1 END) as receipts_last_30_days,
                COALESCE(SUM(CASE WHEN r.receipt_date >= CURDATE() - INTERVAL 30 DAY THEN r.total ELSE 0 END), 0) as revenue_last_30_days
            FROM stores s
            LEFT JOIN receipts r ON s.id = r.store_id AND r.status = 'completed'
            WHERE s.id = :store_id
            GROUP BY s.id
        ";

        $result = $this->db->fetchRow($sql, ['store_id' => $id]);
        
        if (!$result) {
            return [
                'total_receipts' => 0,
                'total_revenue' => 0,
                'avg_receipt_value' => 0,
                'receipts_last_30_days' => 0,
                'revenue_last_30_days' => 0
            ];
        }

        return $result;
    }

    /**
     * 搜索商店
     * Search stores
     * 
     * @param string $keyword 關鍵詞
     * @param int $limit 限制數量
     * @return array
     */
    public function searchStores($keyword, $limit = 10) {
        $sql = "
            SELECT * FROM `{$this->table}` 
            WHERE (`name` LIKE :keyword OR `email` LIKE :keyword OR `phone` LIKE :keyword)
            AND `is_active` = 1
            ORDER BY `name` ASC
            LIMIT {$limit}
        ";
        
        $keyword = '%' . $keyword . '%';
        return $this->db->fetchAll($sql, ['keyword' => $keyword]);
    }

    /**
     * 獲取商店的收據列表
     * Get store receipts
     * 
     * @param int $storeId 商店ID
     * @param int $page 頁碼
     * @param int $perPage 每頁數量
     * @return array
     */
    public function getStoreReceipts($storeId, $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        
        $sql = "
            SELECT r.*, 
                   COALESCE(c.name, r.customer_name) as customer_name,
                   COALESCE(c.phone, r.customer_phone) as customer_phone
            FROM receipts r
            LEFT JOIN customers c ON r.customer_id = c.id
            WHERE r.store_id = :store_id
            ORDER BY r.receipt_date DESC, r.created_at DESC
            LIMIT {$perPage} OFFSET {$offset}
        ";
        
        $countSql = "SELECT COUNT(*) as count FROM receipts WHERE store_id = :store_id";
        
        $data = $this->db->fetchAll($sql, ['store_id' => $storeId]);
        $total = $this->db->fetchRow($countSql, ['store_id' => $storeId])['count'];
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
    }
}
?>
