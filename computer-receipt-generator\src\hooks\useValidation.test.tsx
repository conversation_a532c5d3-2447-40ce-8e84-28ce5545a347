import { describe, it, expect } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useValidation } from './useValidation';

describe('useValidation', () => {
  it('應該提供初始狀態', () => {
    const { result } = renderHook(() => useValidation());

    expect(result.current.errors).toEqual([]);
    expect(result.current.hasErrors).toBe(false);
    expect(result.current.errorFields).toEqual([]);
  });

  it('應該能夠新增和清除錯誤', () => {
    const { result } = renderHook(() => useValidation());

    // 新增錯誤
    act(() => {
      result.current.addError('testField', 'Test error message');
    });

    expect(result.current.errors).toHaveLength(1);
    expect(result.current.hasErrors).toBe(true);
    expect(result.current.getFieldError('testField')).toBe('Test error message');
    expect(result.current.hasFieldError('testField')).toBe(true);

    // 清除特定欄位錯誤
    act(() => {
      result.current.clearFieldError('testField');
    });

    expect(result.current.errors).toHaveLength(0);
    expect(result.current.hasErrors).toBe(false);
  });

  it('應該能夠清除所有錯誤', () => {
    const { result } = renderHook(() => useValidation());

    // 新增多個錯誤
    act(() => {
      result.current.addError('field1', 'Error 1');
      result.current.addError('field2', 'Error 2');
    });

    expect(result.current.errors).toHaveLength(2);

    // 清除所有錯誤
    act(() => {
      result.current.clearErrors();
    });

    expect(result.current.errors).toHaveLength(0);
    expect(result.current.hasErrors).toBe(false);
  });

  it('應該正確驗證價格欄位', () => {
    const { result } = renderHook(() => useValidation());

    // 有效價格
    act(() => {
      const isValid = result.current.validatePriceField('price', 100.50);
      expect(isValid).toBe(true);
    });
    expect(result.current.hasFieldError('price')).toBe(false);

    // 無效價格
    act(() => {
      const isValid = result.current.validatePriceField('price', -10);
      expect(isValid).toBe(false);
    });
    expect(result.current.hasFieldError('price')).toBe(true);
    expect(result.current.getFieldError('price')).toBe('價格必須大於 0');
  });

  it('應該正確驗證必填欄位', () => {
    const { result } = renderHook(() => useValidation());

    // 有效值
    act(() => {
      const isValid = result.current.validateRequiredField('name', 'Test Name', '姓名');
      expect(isValid).toBe(true);
    });
    expect(result.current.hasFieldError('name')).toBe(false);

    // 空值
    act(() => {
      const isValid = result.current.validateRequiredField('name', '', '姓名');
      expect(isValid).toBe(false);
    });
    expect(result.current.hasFieldError('name')).toBe(true);
    expect(result.current.getFieldError('name')).toBe('姓名為必填欄位');
  });

  it('應該正確驗證電子郵件欄位', () => {
    const { result } = renderHook(() => useValidation());

    // 有效電子郵件
    act(() => {
      const isValid = result.current.validateEmailField('email', '<EMAIL>');
      expect(isValid).toBe(true);
    });
    expect(result.current.hasFieldError('email')).toBe(false);

    // 空值應該通過（選填）
    act(() => {
      const isValid = result.current.validateEmailField('email', '');
      expect(isValid).toBe(true);
    });
    expect(result.current.hasFieldError('email')).toBe(false);

    // 無效電子郵件
    act(() => {
      const isValid = result.current.validateEmailField('email', 'invalid-email');
      expect(isValid).toBe(false);
    });
    expect(result.current.hasFieldError('email')).toBe(true);
    expect(result.current.getFieldError('email')).toBe('請輸入有效的電子郵件格式');
  });

  it('應該正確驗證電話號碼欄位', () => {
    const { result } = renderHook(() => useValidation());

    // 有效電話號碼
    act(() => {
      const isValid = result.current.validatePhoneField('phone', '0912345678');
      expect(isValid).toBe(true);
    });
    expect(result.current.hasFieldError('phone')).toBe(false);

    // 空值應該通過（選填）
    act(() => {
      const isValid = result.current.validatePhoneField('phone', '');
      expect(isValid).toBe(true);
    });
    expect(result.current.hasFieldError('phone')).toBe(false);

    // 無效電話號碼
    act(() => {
      const isValid = result.current.validatePhoneField('phone', '123');
      expect(isValid).toBe(false);
    });
    expect(result.current.hasFieldError('phone')).toBe(true);
    expect(result.current.getFieldError('phone')).toBe('請輸入有效的電話號碼');
  });

  it('應該正確驗證數量欄位', () => {
    const { result } = renderHook(() => useValidation());

    // 有效數量
    act(() => {
      const isValid = result.current.validateQuantityField('quantity', 5);
      expect(isValid).toBe(true);
    });
    expect(result.current.hasFieldError('quantity')).toBe(false);

    // 無效數量
    act(() => {
      const isValid = result.current.validateQuantityField('quantity', 0);
      expect(isValid).toBe(false);
    });
    expect(result.current.hasFieldError('quantity')).toBe(true);
    expect(result.current.getFieldError('quantity')).toBe('數量必須大於 0');
  });

  it('應該正確驗證稅率欄位', () => {
    const { result } = renderHook(() => useValidation());

    // 有效稅率
    act(() => {
      const isValid = result.current.validateTaxRateField('taxRate', 5);
      expect(isValid).toBe(true);
    });
    expect(result.current.hasFieldError('taxRate')).toBe(false);

    // 無效稅率
    act(() => {
      const isValid = result.current.validateTaxRateField('taxRate', 150);
      expect(isValid).toBe(false);
    });
    expect(result.current.hasFieldError('taxRate')).toBe(true);
    expect(result.current.getFieldError('taxRate')).toBe('稅率必須在 0-100% 之間');
  });

  it('應該正確驗證項目表單', () => {
    const { result } = renderHook(() => useValidation());

    // 有效項目
    act(() => {
      const isValid = result.current.validateItemForm({
        name: 'Test CPU',
        price: 10000,
        quantity: 1,
      });
      expect(isValid).toBe(true);
    });
    expect(result.current.hasErrors).toBe(false);

    // 無效項目
    act(() => {
      const isValid = result.current.validateItemForm({
        name: '',
        price: -100,
        quantity: 0,
      });
      expect(isValid).toBe(false);
    });
    expect(result.current.hasErrors).toBe(true);
    expect(result.current.hasFieldError('itemName')).toBe(true);
    expect(result.current.hasFieldError('itemPrice')).toBe(true);
    expect(result.current.hasFieldError('itemQuantity')).toBe(true);
  });

  it('應該正確驗證商店資訊表單', () => {
    const { result } = renderHook(() => useValidation());

    // 有效商店資訊
    act(() => {
      const isValid = result.current.validateStoreInfoForm({
        name: 'Test Store',
        address: 'Test Address',
        phone: '0912345678',
        email: '<EMAIL>',
      });
      expect(isValid).toBe(true);
    });
    expect(result.current.hasErrors).toBe(false);

    // 無效商店資訊
    act(() => {
      const isValid = result.current.validateStoreInfoForm({
        name: '',
        address: '',
        phone: '123',
        email: 'invalid-email',
      });
      expect(isValid).toBe(false);
    });
    expect(result.current.hasErrors).toBe(true);
    expect(result.current.hasFieldError('storeName')).toBe(true);
    expect(result.current.hasFieldError('storeAddress')).toBe(true);
    expect(result.current.hasFieldError('storePhone')).toBe(true);
    expect(result.current.hasFieldError('storeEmail')).toBe(true);
  });

  it('應該正確驗證客戶資訊表單', () => {
    const { result } = renderHook(() => useValidation());

    // 有效客戶資訊
    act(() => {
      const isValid = result.current.validateCustomerInfoForm({
        name: 'Test Customer',
        phone: '0987654321',
        email: '<EMAIL>',
      });
      expect(isValid).toBe(true);
    });
    expect(result.current.hasErrors).toBe(false);

    // 無效客戶資訊
    act(() => {
      const isValid = result.current.validateCustomerInfoForm({
        name: '',
        phone: '123',
        email: 'invalid-email',
      });
      expect(isValid).toBe(false);
    });
    expect(result.current.hasErrors).toBe(true);
    expect(result.current.hasFieldError('customerName')).toBe(true);
    expect(result.current.hasFieldError('customerPhone')).toBe(true);
    expect(result.current.hasFieldError('customerEmail')).toBe(true);
  });

  it('應該正確處理欄位錯誤的替換', () => {
    const { result } = renderHook(() => useValidation());

    // 新增第一個錯誤
    act(() => {
      result.current.addError('testField', 'First error');
    });
    expect(result.current.getFieldError('testField')).toBe('First error');

    // 新增同一欄位的新錯誤，應該替換舊錯誤
    act(() => {
      result.current.addError('testField', 'Second error');
    });
    expect(result.current.getFieldError('testField')).toBe('Second error');
    expect(result.current.errors).toHaveLength(1);
  });
});