import React from 'react';
import { Receipt } from './Receipt';
import { PrintStyles, usePrintPreview } from './PrintStyles';
import { PrintButton, SavePDFButton } from '../ui';
import type { ReceiptData } from '../../types';

/**
 * ReceiptDemo 組件
 * 用於展示和測試收據的 letter 紙張尺寸樣式
 */
export const ReceiptDemo: React.FC = () => {
  const { isPrintPreview, togglePrintPreview } = usePrintPreview();

  // 示例收據資料
  const demoReceiptData: ReceiptData = {
    receiptNumber: 'RCP-DEMO-001',
    date: new Date(),
    storeInfo: {
      name: '科技電腦專賣店',
      address: '台北市信義區信義路五段7號101樓',
      phone: '02-2345-6789',
      email: '<EMAIL>',
    },
    customerInfo: {
      name: '張小華',
      phone: '0987-654-321',
      email: '<EMAIL>',
      address: '台北市大安區復興南路一段390號',
    },
    items: [
      {
        id: '1',
        name: 'Intel Core i7-13700K 處理器',
        category: 'CPU',
        price: 15000,
        quantity: 1,
      },
      {
        id: '2',
        name: 'ASUS ROG STRIX Z790-E 主機板',
        category: 'Motherboard',
        price: 12000,
        quantity: 1,
      },
      {
        id: '3',
        name: 'Corsair Vengeance DDR5-5600 32GB',
        category: 'RAM',
        price: 8000,
        quantity: 2,
      },
      {
        id: '4',
        name: 'NVIDIA GeForce RTX 4080 顯示卡',
        category: 'GPU',
        price: 35000,
        quantity: 1,
      },
      {
        id: '5',
        name: 'Samsung 980 PRO 2TB NVMe SSD',
        category: 'SSD',
        price: 6000,
        quantity: 1,
      },
      {
        id: '6',
        name: '系統組裝與測試服務',
        category: 'Labor Fee',
        price: 1500,
        quantity: 1,
      },
      {
        id: '7',
        name: 'Windows 11 Pro 安裝',
        category: 'OS',
        price: 800,
        quantity: 1,
      },
    ],
    taxRate: 5,
    subtotal: 0, // 會由 Receipt 組件重新計算
    taxAmount: 0, // 會由 Receipt 組件重新計算
    total: 0, // 會由 Receipt 組件重新計算
    logoSettings: {
      imageUrl: null,
      position: { x: 10, y: 10 },
      size: { width: 20, height: 15 },
      opacity: 1,
    },
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <PrintStyles />
      
      {/* 控制按鈕 */}
      <div className="no-print" style={{ 
        marginBottom: '20px', 
        textAlign: 'center',
        backgroundColor: 'white',
        padding: '15px',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <h2 style={{ margin: '0 0 15px 0', color: '#000000' }}>收據預覽與列印測試</h2>
        <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', flexWrap: 'wrap' }}>
          <PrintButton targetElementId="receipt-container">
            🖨️ 列印收據
          </PrintButton>
          <SavePDFButton 
            targetElementId="receipt-container"
            filename="computer-receipt.pdf"
            onError={(error) => alert(`PDF 生成失敗: ${error.message}`)}
          >
            💾 儲存 PDF
          </SavePDFButton>
          <button
            type="button"
            onClick={togglePrintPreview}
            style={{
              padding: '10px 20px',
              backgroundColor: isPrintPreview ? '#28a745' : '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            👁️ {isPrintPreview ? '關閉' : '開啟'}列印預覽
          </button>
        </div>
        <p style={{ 
          margin: '10px 0 0 0', 
          fontSize: '12px', 
          color: '#666',
          lineHeight: '1.4'
        }}>
          • 點擊「列印收據」測試 Letter 紙張尺寸列印<br/>
          • 點擊「儲存 PDF」生成高品質 PDF 檔案<br/>
          • 點擊「列印預覽」查看列印版面效果<br/>
          • 收據尺寸：8.5" × 11" (Letter 標準)
        </p>
      </div>

      {/* 收據組件 */}
      <div id="receipt-container">
        <Receipt 
          receiptData={demoReceiptData} 
          isPrintPreview={isPrintPreview}
        />
      </div>
      
      {/* 頁面資訊 */}
      <div className="no-print" style={{ 
        marginTop: '20px', 
        textAlign: 'center',
        backgroundColor: 'white',
        padding: '15px',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        fontSize: '12px',
        color: '#666'
      }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#000000' }}>技術規格</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
          <div>
            <strong>紙張尺寸:</strong> Letter (8.5" × 11")
          </div>
          <div>
            <strong>邊距:</strong> 0.5 英吋
          </div>
          <div>
            <strong>字體:</strong> Arial, Microsoft JhengHei
          </div>
          <div>
            <strong>響應式:</strong> 支援手機、平板、桌面
          </div>
        </div>
      </div>
    </div>
  );
};