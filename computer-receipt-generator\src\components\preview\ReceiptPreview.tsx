import React, { useMemo } from 'react';
import { Receipt } from '../receipt/Receipt';
import { useReceipt } from '../../hooks/useReceipt';
import styles from './ReceiptPreview.module.css';

interface ReceiptPreviewProps {
  className?: string;
  showPrintStyles?: boolean;
}

export const ReceiptPreview: React.FC<ReceiptPreviewProps> = ({ 
  className = '',
  showPrintStyles = false 
}) => {
  const { receiptData, totals, isReceiptValid } = useReceipt();

  // 建立完整的收據資料，包含計算後的總額
  const completeReceiptData = useMemo(() => ({
    ...receiptData,
    subtotal: totals.subtotal,
    taxAmount: totals.taxAmount,
    total: totals.total,
  }), [receiptData, totals]);

  // 檢查是否有任何項目
  const hasItems = receiptData.items.length > 0;
  
  // 檢查是否有基本資訊 - 現在都是選填

  const containerClasses = [
    styles.previewContainer,
    showPrintStyles ? styles.printMode : '',
    className
  ].filter(Boolean).join(' ');

  // 如果沒有任何項目，顯示空狀態
  if (!hasItems) {
    return (
      <div className={containerClasses}>
        <div className={styles.emptyState}>
          <div className={styles.emptyIcon}>📄</div>
          <h3 className={styles.emptyTitle}>Receipt Preview</h3>
          <p className={styles.emptyMessage}>
            Please add items first to view receipt preview
          </p>
          <div className={styles.emptyHints}>
            <div className={styles.hint}>
              <span className={styles.hintIcon}>1️⃣</span>
              <span>Fill in store and customer information</span>
            </div>
            <div className={styles.hint}>
              <span className={styles.hintIcon}>2️⃣</span>
              <span>Add receipt items</span>
            </div>
            <div className={styles.hint}>
              <span className={styles.hintIcon}>3️⃣</span>
              <span>Set appropriate tax rate</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={containerClasses}>
      {/* 預覽標題和狀態指示器 */}
      <div className={styles.previewHeader}>
        <div className={styles.headerContent}>
          <h2 className={styles.previewTitle}>Receipt Preview</h2>
          <div className={styles.statusIndicators}>
            <div className={`${styles.statusItem} ${styles.complete}`}>
              <span className={styles.statusIcon}>✅</span>
              <span className={styles.statusText}>Store Information (Optional)</span>
            </div>
            <div className={`${styles.statusItem} ${styles.complete}`}>
              <span className={styles.statusIcon}>✅</span>
              <span className={styles.statusText}>Customer Information (Optional)</span>
            </div>
            <div className={`${styles.statusItem} ${hasItems ? styles.complete : styles.incomplete}`}>
              <span className={styles.statusIcon}>
                {hasItems ? '✅' : '⚠️'}
              </span>
              <span className={styles.statusText}>Receipt Items (Required)</span>
            </div>
          </div>
        </div>
        
        {/* 即時更新指示器 */}
        <div className={styles.liveIndicator}>
          <span className={styles.liveIcon}>🔄</span>
          <span className={styles.liveText}>Live Update</span>
        </div>
      </div>

      {/* 收據內容 */}
      <div className={styles.receiptWrapper}>
        <div id="receipt-content-for-pdf">
          <Receipt
            receiptData={completeReceiptData}
            className={styles.previewReceipt}
            isPrintPreview={showPrintStyles}
          />
        </div>
      </div>

      {/* 預覽資訊和提示 */}
      <div className={styles.previewInfo}>
        <div className={styles.infoSection}>
          <h4 className={styles.infoTitle}>Receipt Summary</h4>
          <div className={styles.summaryGrid}>
            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Item Count:</span>
              <span className={styles.summaryValue}>{receiptData.items.length}</span>
            </div>
            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Subtotal:</span>
              <span className={styles.summaryValue}>$ {totals.subtotal.toLocaleString()}</span>
            </div>
            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Tax:</span>
              <span className={styles.summaryValue}>$ {totals.taxAmount.toLocaleString()}</span>
            </div>
            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Total:</span>
              <span className={`${styles.summaryValue} ${styles.totalAmount}`}>
                $ {totals.total.toLocaleString()}
              </span>
            </div>
          </div>
        </div>

        {/* 完整性檢查提示 */}
        {!isReceiptValid && (
          <div className={styles.validationWarning}>
            <div className={styles.warningIcon}>⚠️</div>
            <div className={styles.warningContent}>
              <h5 className={styles.warningTitle}>Receipt Information Incomplete</h5>
              <ul className={styles.warningList}>
                {!hasItems && <li>Please add at least one receipt item to generate the receipt</li>}
              </ul>
            </div>
          </div>
        )}

        {/* 操作提示 */}
        <div className={styles.actionHints}>
          <p className={styles.hintText}>
            💡 The receipt will update in real-time as you input. Once completed, you can print or save as PDF.
          </p>
        </div>
      </div>
    </div>
  );
};