/* 基本按鈕樣式 */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid transparent;
  border-radius: 6px;
  font-family: inherit;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.button:focus {
  outline: 2px solid #646cff;
  outline-offset: 2px;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 尺寸變體 */
.small {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.medium {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.large {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

/* 顏色變體 */
.primary {
  background-color: #646cff;
  color: white;
  border-color: #646cff;
}

.primary:hover:not(:disabled) {
  background-color: #535bf2;
  border-color: #535bf2;
}

.secondary {
  background-color: #f8f9fa;
  color: #495057;
  border-color: #dee2e6;
}

.secondary:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.danger {
  background-color: #dc3545;
  color: white;
  border-color: #dc3545;
}

.danger:hover:not(:disabled) {
  background-color: #c82333;
  border-color: #bd2130;
}

.outline {
  background-color: transparent;
  color: #646cff;
  border-color: #646cff;
}

.outline:hover:not(:disabled) {
  background-color: #646cff;
  color: white;
}

/* 全寬度 */
.fullWidth {
  width: 100%;
}

/* 載入狀態 */
.loading {
  pointer-events: none;
}

.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.hiddenText {
  opacity: 0;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}