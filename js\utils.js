/**
 * Utility Functions
 * 工具函數庫
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

'use strict';

// 全局工具對象
window.KMSUtils = {
    
    /**
     * 格式化貨幣
     * Format currency
     * 
     * @param {number} amount 金額
     * @param {string} currency 貨幣符號
     * @return {string} 格式化後的金額
     */
    formatCurrency(amount, currency = 'NT$') {
        if (typeof amount !== 'number' || isNaN(amount)) {
            return `${currency} 0`;
        }
        return `${currency} ${amount.toLocaleString('zh-TW', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        })}`;
    },

    /**
     * 格式化日期
     * Format date
     * 
     * @param {Date|string} date 日期
     * @param {string} format 格式
     * @return {string} 格式化後的日期
     */
    formatDate(date, format = 'YYYY-MM-DD') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    /**
     * 生成唯一ID
     * Generate unique ID
     * 
     * @param {string} prefix 前綴
     * @return {string} 唯一ID
     */
    generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    /**
     * 生成收據編號
     * Generate receipt number
     * 
     * @param {string} prefix 前綴
     * @return {string} 收據編號
     */
    generateReceiptNumber(prefix = 'RCP') {
        const date = this.formatDate(new Date(), 'YYYYMMDD');
        const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
        return `${prefix}-${date}-${random}`;
    },

    /**
     * 深度克隆對象
     * Deep clone object
     * 
     * @param {any} obj 要克隆的對象
     * @return {any} 克隆後的對象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const cloned = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }
        
        return obj;
    },

    /**
     * 防抖函數
     * Debounce function
     * 
     * @param {Function} func 要防抖的函數
     * @param {number} wait 等待時間（毫秒）
     * @return {Function} 防抖後的函數
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func.apply(this, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 節流函數
     * Throttle function
     * 
     * @param {Function} func 要節流的函數
     * @param {number} limit 限制時間（毫秒）
     * @return {Function} 節流後的函數
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 驗證電子郵件格式
     * Validate email format
     * 
     * @param {string} email 電子郵件
     * @return {boolean} 是否有效
     */
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * 驗證電話號碼格式（台灣）
     * Validate phone number format (Taiwan)
     * 
     * @param {string} phone 電話號碼
     * @return {boolean} 是否有效
     */
    validatePhone(phone) {
        const phoneRegex = /^(\+886|0)([2-9]\d{7,8}|[2-9]\d{2}-\d{3}-\d{3,4})$/;
        return phoneRegex.test(phone);
    },

    /**
     * 清理HTML字符串
     * Sanitize HTML string
     * 
     * @param {string} str 要清理的字符串
     * @return {string} 清理後的字符串
     */
    sanitizeHtml(str) {
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    },

    /**
     * 獲取URL參數
     * Get URL parameter
     * 
     * @param {string} name 參數名
     * @return {string|null} 參數值
     */
    getUrlParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    /**
     * 設置URL參數
     * Set URL parameter
     * 
     * @param {string} name 參數名
     * @param {string} value 參數值
     */
    setUrlParameter(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.replaceState({}, '', url);
    },

    /**
     * 本地存儲操作
     * Local storage operations
     */
    storage: {
        /**
         * 設置本地存儲
         * Set local storage
         * 
         * @param {string} key 鍵
         * @param {any} value 值
         */
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (error) {
                console.error('Failed to set localStorage:', error);
            }
        },

        /**
         * 獲取本地存儲
         * Get local storage
         * 
         * @param {string} key 鍵
         * @param {any} defaultValue 默認值
         * @return {any} 存儲的值
         */
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('Failed to get localStorage:', error);
                return defaultValue;
            }
        },

        /**
         * 移除本地存儲
         * Remove local storage
         * 
         * @param {string} key 鍵
         */
        remove(key) {
            try {
                localStorage.removeItem(key);
            } catch (error) {
                console.error('Failed to remove localStorage:', error);
            }
        },

        /**
         * 清空本地存儲
         * Clear local storage
         */
        clear() {
            try {
                localStorage.clear();
            } catch (error) {
                console.error('Failed to clear localStorage:', error);
            }
        }
    },

    /**
     * DOM操作工具
     * DOM manipulation utilities
     */
    dom: {
        /**
         * 查找元素
         * Find element
         * 
         * @param {string} selector 選擇器
         * @param {Element} parent 父元素
         * @return {Element|null} 找到的元素
         */
        find(selector, parent = document) {
            return parent.querySelector(selector);
        },

        /**
         * 查找所有元素
         * Find all elements
         * 
         * @param {string} selector 選擇器
         * @param {Element} parent 父元素
         * @return {NodeList} 找到的元素列表
         */
        findAll(selector, parent = document) {
            return parent.querySelectorAll(selector);
        },

        /**
         * 創建元素
         * Create element
         * 
         * @param {string} tag 標籤名
         * @param {Object} attributes 屬性
         * @param {string} content 內容
         * @return {Element} 創建的元素
         */
        create(tag, attributes = {}, content = '') {
            const element = document.createElement(tag);
            
            Object.keys(attributes).forEach(key => {
                if (key === 'className') {
                    element.className = attributes[key];
                } else if (key === 'dataset') {
                    Object.keys(attributes[key]).forEach(dataKey => {
                        element.dataset[dataKey] = attributes[key][dataKey];
                    });
                } else {
                    element.setAttribute(key, attributes[key]);
                }
            });
            
            if (content) {
                element.innerHTML = content;
            }
            
            return element;
        },

        /**
         * 添加事件監聽器
         * Add event listener
         * 
         * @param {Element|string} element 元素或選擇器
         * @param {string} event 事件名
         * @param {Function} handler 處理函數
         * @param {Object} options 選項
         */
        on(element, event, handler, options = {}) {
            const el = typeof element === 'string' ? this.find(element) : element;
            if (el) {
                el.addEventListener(event, handler, options);
            }
        },

        /**
         * 移除事件監聽器
         * Remove event listener
         * 
         * @param {Element|string} element 元素或選擇器
         * @param {string} event 事件名
         * @param {Function} handler 處理函數
         */
        off(element, event, handler) {
            const el = typeof element === 'string' ? this.find(element) : element;
            if (el) {
                el.removeEventListener(event, handler);
            }
        },

        /**
         * 切換類名
         * Toggle class
         * 
         * @param {Element|string} element 元素或選擇器
         * @param {string} className 類名
         */
        toggleClass(element, className) {
            const el = typeof element === 'string' ? this.find(element) : element;
            if (el) {
                el.classList.toggle(className);
            }
        },

        /**
         * 添加類名
         * Add class
         * 
         * @param {Element|string} element 元素或選擇器
         * @param {string} className 類名
         */
        addClass(element, className) {
            const el = typeof element === 'string' ? this.find(element) : element;
            if (el) {
                el.classList.add(className);
            }
        },

        /**
         * 移除類名
         * Remove class
         * 
         * @param {Element|string} element 元素或選擇器
         * @param {string} className 類名
         */
        removeClass(element, className) {
            const el = typeof element === 'string' ? this.find(element) : element;
            if (el) {
                el.classList.remove(className);
            }
        }
    },

    /**
     * 計算相關工具
     * Calculation utilities
     */
    calc: {
        /**
         * 計算稅額
         * Calculate tax amount
         * 
         * @param {number} subtotal 小計
         * @param {number} taxRate 稅率（百分比）
         * @return {number} 稅額
         */
        calculateTax(subtotal, taxRate) {
            return Math.round(subtotal * (taxRate / 100) * 100) / 100;
        },

        /**
         * 計算總計
         * Calculate total
         * 
         * @param {number} subtotal 小計
         * @param {number} taxAmount 稅額
         * @param {number} discount 折扣
         * @return {number} 總計
         */
        calculateTotal(subtotal, taxAmount, discount = 0) {
            return Math.round((subtotal + taxAmount - discount) * 100) / 100;
        },

        /**
         * 計算項目小計
         * Calculate item subtotal
         * 
         * @param {number} price 單價
         * @param {number} quantity 數量
         * @return {number} 小計
         */
        calculateItemSubtotal(price, quantity) {
            return Math.round(price * quantity * 100) / 100;
        }
    }
};

// 導出到全局作用域
window.Utils = window.KMSUtils;
