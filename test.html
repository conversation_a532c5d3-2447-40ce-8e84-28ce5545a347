<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS PC Receipt Maker - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>KMS PC Receipt Maker - 功能測試</h1>
    
    <div class="test-section">
        <h2>1. 基礎功能測試</h2>
        <button onclick="testBasicFunctions()">測試基礎功能</button>
        <div id="basic-test-results"></div>
    </div>

    <div class="test-section">
        <h2>2. 數據庫連接測試</h2>
        <button onclick="testDatabaseConnection()">測試數據庫連接</button>
        <div id="db-test-results"></div>
    </div>

    <div class="test-section">
        <h2>3. API接口測試</h2>
        <button onclick="testAPIEndpoints()">測試API接口</button>
        <div id="api-test-results"></div>
    </div>

    <div class="test-section">
        <h2>4. 前端功能測試</h2>
        <button onclick="testFrontendFunctions()">測試前端功能</button>
        <div id="frontend-test-results"></div>
    </div>

    <div class="test-section">
        <h2>5. 收據生成測試</h2>
        <button onclick="testReceiptGeneration()">測試收據生成</button>
        <div id="receipt-test-results"></div>
    </div>

    <div class="test-section">
        <h2>6. 系統信息</h2>
        <button onclick="showSystemInfo()">顯示系統信息</button>
        <div id="system-info"></div>
    </div>

    <script>
        // 測試結果顯示函數
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }

        function clearResults(containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
        }

        // 1. 基礎功能測試
        function testBasicFunctions() {
            clearResults('basic-test-results');
            
            try {
                // 測試HTML結構
                const mainElements = [
                    'receipt-form',
                    'receipt-preview',
                    'store-select',
                    'customer-name',
                    'items-list'
                ];
                
                let missingElements = [];
                mainElements.forEach(id => {
                    if (!document.getElementById(id)) {
                        missingElements.push(id);
                    }
                });

                if (missingElements.length === 0) {
                    showResult('basic-test-results', '✓ HTML結構完整', 'success');
                } else {
                    showResult('basic-test-results', `✗ 缺少HTML元素: ${missingElements.join(', ')}`, 'error');
                }

                // 測試CSS文件
                const cssFiles = [
                    'css/main.css',
                    'css/forms.css',
                    'css/receipt.css',
                    'css/responsive.css',
                    'css/print.css'
                ];

                let loadedCSS = 0;
                cssFiles.forEach(file => {
                    const link = document.querySelector(`link[href="${file}"]`);
                    if (link) loadedCSS++;
                });

                showResult('basic-test-results', `CSS文件載入: ${loadedCSS}/${cssFiles.length}`, 
                    loadedCSS === cssFiles.length ? 'success' : 'error');

                // 測試JavaScript文件
                const jsFiles = [
                    'js/utils.js',
                    'js/i18n.js',
                    'js/api.js',
                    'js/forms.js',
                    'js/receipt.js',
                    'js/print.js',
                    'js/app.js'
                ];

                let loadedJS = 0;
                jsFiles.forEach(file => {
                    const script = document.querySelector(`script[src="${file}"]`);
                    if (script) loadedJS++;
                });

                showResult('basic-test-results', `JavaScript文件載入: ${loadedJS}/${jsFiles.length}`, 
                    loadedJS === jsFiles.length ? 'success' : 'error');

            } catch (error) {
                showResult('basic-test-results', `✗ 基礎功能測試失敗: ${error.message}`, 'error');
            }
        }

        // 2. 數據庫連接測試
        async function testDatabaseConnection() {
            clearResults('db-test-results');

            try {
                showResult('db-test-results', '正在測試數據庫連接...', 'info');

                // 測試數據庫連接
                const response = await fetch('/api/test-db', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        showResult('db-test-results', '✓ 數據庫連接成功', 'success');
                        showResult('db-test-results', `數據庫: ${data.data.database}`, 'info');
                        showResult('db-test-results', `版本: ${data.data.version}`, 'info');
                        showResult('db-test-results', `字符集: ${data.data.charset}`, 'info');

                        // 檢查表狀態
                        const tables = data.data.tables;
                        Object.keys(tables).forEach(table => {
                            const status = tables[table] === 'exists' ? '✓' : '✗';
                            const type = tables[table] === 'exists' ? 'success' : 'error';
                            showResult('db-test-results', `${status} 表 ${table}: ${tables[table]}`, type);
                        });
                    } else {
                        showResult('db-test-results', `✗ 數據庫測試失敗: ${data.message}`, 'error');
                    }
                } else {
                    showResult('db-test-results', `✗ 數據庫連接失敗: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('db-test-results', `✗ 數據庫連接測試失敗: ${error.message}`, 'error');
                showResult('db-test-results', '請確保：<br>1. MySQL服務已啟動<br>2. 數據庫配置正確<br>3. 數據表已創建<br>4. 運行 database/schema.sql 創建表結構', 'info');
            }
        }

        // 3. API接口測試
        async function testAPIEndpoints() {
            clearResults('api-test-results');
            
            const endpoints = [
                { url: '/api/stores', method: 'GET', name: '商店列表' },
                { url: '/api/customers', method: 'GET', name: '客戶列表' },
                { url: '/api/categories', method: 'GET', name: '分類列表' },
                { url: '/api/receipts', method: 'GET', name: '收據列表' }
            ];

            for (const endpoint of endpoints) {
                try {
                    showResult('api-test-results', `正在測試 ${endpoint.name}...`, 'info');
                    
                    const response = await fetch(endpoint.url, {
                        method: endpoint.method,
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        showResult('api-test-results', `✓ ${endpoint.name} API 正常`, 'success');
                    } else {
                        showResult('api-test-results', `✗ ${endpoint.name} API 失敗: HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    showResult('api-test-results', `✗ ${endpoint.name} API 錯誤: ${error.message}`, 'error');
                }
            }
        }

        // 4. 前端功能測試
        function testFrontendFunctions() {
            clearResults('frontend-test-results');
            
            try {
                // 測試全局對象
                const globalObjects = [
                    'Utils',
                    'i18n',
                    'API',
                    'formHandler',
                    'receiptManager',
                    'app'
                ];

                globalObjects.forEach(obj => {
                    if (window[obj]) {
                        showResult('frontend-test-results', `✓ ${obj} 對象已載入`, 'success');
                    } else {
                        showResult('frontend-test-results', `✗ ${obj} 對象未載入`, 'error');
                    }
                });

                // 測試工具函數
                if (window.Utils) {
                    const testAmount = Utils.formatCurrency(1234.56);
                    showResult('frontend-test-results', `✓ 貨幣格式化測試: ${testAmount}`, 'success');
                    
                    const testDate = Utils.formatDate(new Date(), 'YYYY-MM-DD');
                    showResult('frontend-test-results', `✓ 日期格式化測試: ${testDate}`, 'success');
                }

                // 測試國際化
                if (window.i18n) {
                    const currentLang = i18n.getCurrentLanguage();
                    showResult('frontend-test-results', `✓ 當前語言: ${currentLang}`, 'success');
                    
                    const testTranslation = t('app-title');
                    showResult('frontend-test-results', `✓ 翻譯測試: ${testTranslation}`, 'success');
                }

            } catch (error) {
                showResult('frontend-test-results', `✗ 前端功能測試失敗: ${error.message}`, 'error');
            }
        }

        // 5. 收據生成測試
        function testReceiptGeneration() {
            clearResults('receipt-test-results');
            
            try {
                showResult('receipt-test-results', '正在測試收據生成功能...', 'info');
                
                // 模擬收據數據
                const mockReceiptData = {
                    receipt_number: 'TEST-001',
                    receipt_date: new Date().toISOString().split('T')[0],
                    store_name: 'KMS PC Store',
                    customer_name: '測試客戶',
                    customer_phone: '0912-345-678',
                    items: [
                        { name: 'CPU', price: 10000, quantity: 1 },
                        { name: 'RAM', price: 3000, quantity: 2 }
                    ],
                    subtotal: 16000,
                    tax_rate: 5,
                    taxAmount: 800,
                    total: 16800
                };

                // 測試收據HTML生成
                if (window.receiptManager) {
                    showResult('receipt-test-results', '✓ 收據管理器已載入', 'success');
                    showResult('receipt-test-results', '✓ 收據數據結構正確', 'success');
                } else {
                    showResult('receipt-test-results', '✗ 收據管理器未載入', 'error');
                }

                // 測試打印功能
                if (window.Print && window.Print.isSupported()) {
                    showResult('receipt-test-results', '✓ 打印功能支持', 'success');
                } else {
                    showResult('receipt-test-results', '✗ 打印功能不支持', 'error');
                }

            } catch (error) {
                showResult('receipt-test-results', `✗ 收據生成測試失敗: ${error.message}`, 'error');
            }
        }

        // 6. 顯示系統信息
        function showSystemInfo() {
            const container = document.getElementById('system-info');
            
            const info = {
                '瀏覽器': navigator.userAgent,
                '語言': navigator.language,
                '在線狀態': navigator.onLine ? '在線' : '離線',
                '屏幕分辨率': `${screen.width}x${screen.height}`,
                '視窗大小': `${window.innerWidth}x${window.innerHeight}`,
                '時區': Intl.DateTimeFormat().resolvedOptions().timeZone,
                '當前時間': new Date().toLocaleString('zh-TW')
            };

            let html = '<pre>';
            Object.keys(info).forEach(key => {
                html += `${key}: ${info[key]}\n`;
            });
            html += '</pre>';

            container.innerHTML = html;
        }

        // 頁面載入完成後自動運行基礎測試
        window.addEventListener('load', () => {
            setTimeout(() => {
                testBasicFunctions();
            }, 1000);
        });
    </script>
</body>
</html>
