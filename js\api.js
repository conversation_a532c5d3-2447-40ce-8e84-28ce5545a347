/**
 * API Communication
 * API通信模塊
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

'use strict';

// API配置
const API_CONFIG = {
    baseUrl: '/api',
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000
};

// API類
class API {
    constructor() {
        this.baseUrl = API_CONFIG.baseUrl;
        this.timeout = API_CONFIG.timeout;
        this.retryAttempts = API_CONFIG.retryAttempts;
        this.retryDelay = API_CONFIG.retryDelay;
    }

    /**
     * 發送HTTP請求
     * Send HTTP request
     * 
     * @param {string} url 請求URL
     * @param {Object} options 請求選項
     * @return {Promise} 請求結果
     */
    async request(url, options = {}) {
        const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;
        
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: this.timeout
        };

        const requestOptions = { ...defaultOptions, ...options };

        // 如果有數據且不是GET請求，序列化數據
        if (requestOptions.data && requestOptions.method !== 'GET') {
            requestOptions.body = JSON.stringify(requestOptions.data);
        }

        // 移除自定義屬性
        delete requestOptions.data;

        let lastError;
        
        // 重試邏輯
        for (let attempt = 0; attempt <= this.retryAttempts; attempt++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);

                const response = await fetch(fullUrl, {
                    ...requestOptions,
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return data;

            } catch (error) {
                lastError = error;
                
                // 如果是最後一次嘗試或者是非網絡錯誤，直接拋出
                if (attempt === this.retryAttempts || !this.isRetryableError(error)) {
                    break;
                }

                // 等待後重試
                await this.delay(this.retryDelay * Math.pow(2, attempt));
            }
        }

        throw lastError;
    }

    /**
     * 判斷是否為可重試的錯誤
     * Check if error is retryable
     */
    isRetryableError(error) {
        return error.name === 'TypeError' || // 網絡錯誤
               error.name === 'AbortError' || // 超時錯誤
               (error.message && error.message.includes('fetch'));
    }

    /**
     * 延遲函數
     * Delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * GET請求
     * GET request
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return this.request(fullUrl, { method: 'GET' });
    }

    /**
     * POST請求
     * POST request
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            data
        });
    }

    /**
     * PUT請求
     * PUT request
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            data
        });
    }

    /**
     * DELETE請求
     * DELETE request
     */
    async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }
}

// API服務類
class APIService {
    constructor() {
        this.api = new API();
    }

    // 商店相關API
    stores = {
        /**
         * 獲取所有商店
         * Get all stores
         */
        getAll: (params = {}) => this.api.get('/stores', params),

        /**
         * 獲取單個商店
         * Get single store
         */
        getById: (id) => this.api.get(`/stores/${id}`),

        /**
         * 創建商店
         * Create store
         */
        create: (data) => this.api.post('/stores', data),

        /**
         * 更新商店
         * Update store
         */
        update: (id, data) => this.api.put(`/stores/${id}`, data),

        /**
         * 刪除商店
         * Delete store
         */
        delete: (id) => this.api.delete(`/stores/${id}`),

        /**
         * 獲取啟用的商店列表
         * Get active stores list
         */
        getActiveList: () => this.api.get('/stores/active/list'),

        /**
         * 檢查郵箱可用性
         * Check email availability
         */
        checkEmail: (email, excludeId = null) => this.api.post('/stores/check-email', { email, exclude_id: excludeId }),

        /**
         * 獲取商店統計
         * Get store statistics
         */
        getStats: (id) => this.api.get(`/stores/${id}/stats`),

        /**
         * 獲取商店收據
         * Get store receipts
         */
        getReceipts: (id, params = {}) => this.api.get(`/stores/${id}/receipts`, params)
    };

    // 客戶相關API
    customers = {
        /**
         * 獲取所有客戶
         * Get all customers
         */
        getAll: (params = {}) => this.api.get('/customers', params),

        /**
         * 獲取單個客戶
         * Get single customer
         */
        getById: (id) => this.api.get(`/customers/${id}`),

        /**
         * 創建客戶
         * Create customer
         */
        create: (data) => this.api.post('/customers', data),

        /**
         * 更新客戶
         * Update customer
         */
        update: (id, data) => this.api.put(`/customers/${id}`, data),

        /**
         * 刪除客戶
         * Delete customer
         */
        delete: (id) => this.api.delete(`/customers/${id}`),

        /**
         * 搜索客戶
         * Search customers
         */
        search: (keyword) => this.api.get('/customers', { search: keyword, per_page: 10 })
    };

    // 分類相關API
    categories = {
        /**
         * 獲取所有分類
         * Get all categories
         */
        getAll: (params = {}) => this.api.get('/categories', params),

        /**
         * 獲取硬件分類
         * Get hardware categories
         */
        getHardware: () => this.api.get('/categories', { type: 'hardware' }),

        /**
         * 獲取服務分類
         * Get service categories
         */
        getService: () => this.api.get('/categories', { type: 'service' }),

        /**
         * 創建分類
         * Create category
         */
        create: (data) => this.api.post('/categories', data),

        /**
         * 更新分類
         * Update category
         */
        update: (id, data) => this.api.put(`/categories/${id}`, data),

        /**
         * 刪除分類
         * Delete category
         */
        delete: (id) => this.api.delete(`/categories/${id}`)
    };

    // 收據相關API
    receipts = {
        /**
         * 獲取所有收據
         * Get all receipts
         */
        getAll: (params = {}) => this.api.get('/receipts', params),

        /**
         * 獲取單個收據
         * Get single receipt
         */
        getById: (id) => this.api.get(`/receipts/${id}`),

        /**
         * 創建收據
         * Create receipt
         */
        create: (data) => this.api.post('/receipts', data),

        /**
         * 更新收據
         * Update receipt
         */
        update: (id, data) => this.api.put(`/receipts/${id}`, data),

        /**
         * 刪除收據
         * Delete receipt
         */
        delete: (id) => this.api.delete(`/receipts/${id}`),

        /**
         * 更新收據狀態
         * Update receipt status
         */
        updateStatus: (id, status) => this.api.put(`/receipts/${id}/status`, { status }),

        /**
         * 根據收據編號查找
         * Find by receipt number
         */
        findByNumber: (receiptNumber) => this.api.get('/receipts', { receipt_number: receiptNumber })
    };

    /**
     * 處理API錯誤
     * Handle API error
     */
    handleError(error) {
        console.error('API Error:', error);
        
        let message = t('error-occurred');
        
        if (error.name === 'TypeError' || error.message.includes('fetch')) {
            message = t('network-error');
        } else if (error.message.includes('HTTP 404')) {
            message = t('not-found');
        } else if (error.message.includes('HTTP 403')) {
            message = t('permission-denied');
        } else if (error.message.includes('HTTP 500')) {
            message = t('server-error');
        }

        // 顯示錯誤消息
        this.showError(message);
        
        return { success: false, error: message };
    }

    /**
     * 顯示錯誤消息
     * Show error message
     */
    showError(message) {
        // 這裡可以集成通知系統
        console.error(message);
        
        // 觸發錯誤事件
        window.dispatchEvent(new CustomEvent('apiError', {
            detail: { message }
        }));
    }

    /**
     * 顯示成功消息
     * Show success message
     */
    showSuccess(message) {
        // 這裡可以集成通知系統
        console.log(message);
        
        // 觸發成功事件
        window.dispatchEvent(new CustomEvent('apiSuccess', {
            detail: { message }
        }));
    }
}

// 創建全局實例
window.apiService = new APIService();

// 導出API服務
window.API = window.apiService;
