<?php
/**
 * Category Model
 * 分類模型類
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

// 防止直接訪問
if (!defined('KMS_RECEIPT_MAKER')) {
    die('Direct access not permitted');
}

require_once 'BaseModel.php';

/**
 * 分類模型類
 * Category Model Class
 */
class Category extends BaseModel {
    protected $table = 'categories';
    protected $fillable = [
        'name_en',
        'name_zh',
        'type',
        'description_en',
        'description_zh',
        'sort_order',
        'is_active'
    ];

    /**
     * 獲取所有啟用的分類
     * Get all active categories
     * 
     * @param string $type 類型過濾 (hardware/service)
     * @return array
     */
    public function getActiveCategories($type = null) {
        $conditions = ['is_active' => 1];
        
        if ($type && in_array($type, ['hardware', 'service'])) {
            $conditions['type'] = $type;
        }
        
        return $this->findAll($conditions, 'sort_order ASC, name_zh ASC');
    }

    /**
     * 獲取硬件分類
     * Get hardware categories
     * 
     * @return array
     */
    public function getHardwareCategories() {
        return $this->getActiveCategories('hardware');
    }

    /**
     * 獲取服務分類
     * Get service categories
     * 
     * @return array
     */
    public function getServiceCategories() {
        return $this->getActiveCategories('service');
    }

    /**
     * 創建分類
     * Create category
     * 
     * @param array $data 分類數據
     * @return array 結果
     */
    public function createCategory($data) {
        // 基本驗證
        $errors = $this->validateCategoryData($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        // 檢查名稱是否已存在
        if ($this->nameExists($data['name_en'], $data['name_zh'])) {
            return [
                'success' => false,
                'errors' => ['name' => '分類名稱已存在 / Category name already exists']
            ];
        }

        // 設置默認排序順序
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = $this->getNextSortOrder($data['type']);
        }

        try {
            $categoryId = $this->create($data);
            $category = $this->findById($categoryId);
            
            return [
                'success' => true,
                'data' => $category,
                'message' => '分類創建成功 / Category created successfully'
            ];
        } catch (Exception $e) {
            logMessage("Category creation failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '創建分類失敗 / Failed to create category']
            ];
        }
    }

    /**
     * 更新分類
     * Update category
     * 
     * @param int $id 分類ID
     * @param array $data 分類數據
     * @return array 結果
     */
    public function updateCategory($id, $data) {
        // 檢查分類是否存在
        $category = $this->findById($id);
        if (!$category) {
            return [
                'success' => false,
                'errors' => ['general' => '分類不存在 / Category not found']
            ];
        }

        // 驗證數據
        $errors = $this->validateCategoryData($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        // 檢查名稱是否已被其他分類使用
        if ($this->nameExists($data['name_en'], $data['name_zh'], $id)) {
            return [
                'success' => false,
                'errors' => ['name' => '分類名稱已被其他分類使用 / Category name already used by another category']
            ];
        }

        try {
            $this->update($id, $data);
            $updatedCategory = $this->findById($id);
            
            return [
                'success' => true,
                'data' => $updatedCategory,
                'message' => '分類更新成功 / Category updated successfully'
            ];
        } catch (Exception $e) {
            logMessage("Category update failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '更新分類失敗 / Failed to update category']
            ];
        }
    }

    /**
     * 刪除分類
     * Delete category
     * 
     * @param int $id 分類ID
     * @return array 結果
     */
    public function deleteCategory($id) {
        // 檢查分類是否存在
        $category = $this->findById($id);
        if (!$category) {
            return [
                'success' => false,
                'errors' => ['general' => '分類不存在 / Category not found']
            ];
        }

        // 檢查是否有關聯的收據項目
        $itemCount = $this->db->fetchRow(
            "SELECT COUNT(*) as count FROM receipt_items WHERE category_id = :category_id",
            ['category_id' => $id]
        );

        if ($itemCount['count'] > 0) {
            // 如果有關聯項目，執行軟刪除
            try {
                $this->update($id, ['is_active' => 0]);
                return [
                    'success' => true,
                    'message' => '分類已停用（因為有關聯的項目） / Category deactivated (has associated items)'
                ];
            } catch (Exception $e) {
                logMessage("Category soft delete failed: " . $e->getMessage(), 'ERROR');
                return [
                    'success' => false,
                    'errors' => ['general' => '停用分類失敗 / Failed to deactivate category']
                ];
            }
        } else {
            // 如果沒有關聯項目，執行硬刪除
            try {
                $this->delete($id);
                return [
                    'success' => true,
                    'message' => '分類刪除成功 / Category deleted successfully'
                ];
            } catch (Exception $e) {
                logMessage("Category delete failed: " . $e->getMessage(), 'ERROR');
                return [
                    'success' => false,
                    'errors' => ['general' => '刪除分類失敗 / Failed to delete category']
                ];
            }
        }
    }

    /**
     * 更新分類排序
     * Update categories sort order
     * 
     * @param array $categoryIds 分類ID數組（按新順序排列）
     * @return array 結果
     */
    public function updateSortOrder($categoryIds) {
        if (empty($categoryIds)) {
            return [
                'success' => false,
                'errors' => ['categories' => '分類ID數組不能為空 / Category IDs array cannot be empty']
            ];
        }

        $this->beginTransaction();

        try {
            foreach ($categoryIds as $index => $categoryId) {
                $this->update($categoryId, ['sort_order' => $index + 1]);
            }

            $this->commit();

            return [
                'success' => true,
                'message' => '分類排序更新成功 / Categories sort order updated successfully'
            ];

        } catch (Exception $e) {
            $this->rollback();
            logMessage("Update categories sort order failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '更新排序失敗 / Failed to update sort order']
            ];
        }
    }

    /**
     * 獲取分類統計信息
     * Get category statistics
     * 
     * @param int $id 分類ID
     * @return array
     */
    public function getCategoryStats($id) {
        $sql = "
            SELECT 
                COUNT(ri.id) as total_items,
                SUM(ri.quantity) as total_quantity,
                SUM(ri.price * ri.quantity) as total_revenue,
                AVG(ri.price) as avg_price,
                COUNT(DISTINCT ri.receipt_id) as receipt_count
            FROM categories c
            LEFT JOIN receipt_items ri ON c.id = ri.category_id
            LEFT JOIN receipts r ON ri.receipt_id = r.id AND r.status = 'completed'
            WHERE c.id = :category_id
            GROUP BY c.id
        ";

        $result = $this->db->fetchRow($sql, ['category_id' => $id]);
        
        if (!$result) {
            return [
                'total_items' => 0,
                'total_quantity' => 0,
                'total_revenue' => 0,
                'avg_price' => 0,
                'receipt_count' => 0
            ];
        }

        return $result;
    }

    /**
     * 驗證分類數據
     * Validate category data
     * 
     * @param array $data 分類數據
     * @return array 錯誤數組
     */
    private function validateCategoryData($data) {
        $errors = [];

        // 驗證英文名稱
        if (empty($data['name_en'])) {
            $errors['name_en'] = '英文名稱為必填項 / English name is required';
        } elseif (strlen($data['name_en']) > 100) {
            $errors['name_en'] = '英文名稱不能超過100個字符 / English name cannot exceed 100 characters';
        }

        // 驗證中文名稱
        if (empty($data['name_zh'])) {
            $errors['name_zh'] = '中文名稱為必填項 / Chinese name is required';
        } elseif (strlen($data['name_zh']) > 100) {
            $errors['name_zh'] = '中文名稱不能超過100個字符 / Chinese name cannot exceed 100 characters';
        }

        // 驗證類型
        if (empty($data['type'])) {
            $errors['type'] = '類型為必填項 / Type is required';
        } elseif (!in_array($data['type'], ['hardware', 'service'])) {
            $errors['type'] = '類型必須是 hardware 或 service / Type must be hardware or service';
        }

        // 驗證排序順序
        if (isset($data['sort_order']) && (!is_numeric($data['sort_order']) || $data['sort_order'] < 0)) {
            $errors['sort_order'] = '排序順序必須是非負數 / Sort order must be a non-negative number';
        }

        return $errors;
    }

    /**
     * 檢查名稱是否已存在
     * Check if name exists
     * 
     * @param string $nameEn 英文名稱
     * @param string $nameZh 中文名稱
     * @param int $excludeId 排除的ID（用於更新時檢查）
     * @return bool
     */
    private function nameExists($nameEn, $nameZh, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM `{$this->table}` WHERE (`name_en` = :name_en OR `name_zh` = :name_zh)";
        $params = ['name_en' => $nameEn, 'name_zh' => $nameZh];

        if ($excludeId) {
            $sql .= " AND `id` != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $result = $this->db->fetchRow($sql, $params);
        return (int)$result['count'] > 0;
    }

    /**
     * 獲取下一個排序順序
     * Get next sort order
     * 
     * @param string $type 類型
     * @return int
     */
    private function getNextSortOrder($type) {
        $sql = "SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order FROM `{$this->table}` WHERE `type` = :type";
        $result = $this->db->fetchRow($sql, ['type' => $type]);
        return (int)$result['next_order'];
    }

    /**
     * 搜索分類
     * Search categories
     * 
     * @param string $keyword 關鍵詞
     * @param string $type 類型過濾
     * @param int $limit 限制數量
     * @return array
     */
    public function searchCategories($keyword, $type = null, $limit = 10) {
        $whereConditions = ["(`name_en` LIKE :keyword OR `name_zh` LIKE :keyword)", "`is_active` = 1"];
        $params = ['keyword' => '%' . $keyword . '%'];

        if ($type && in_array($type, ['hardware', 'service'])) {
            $whereConditions[] = "`type` = :type";
            $params['type'] = $type;
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "
            SELECT * FROM `{$this->table}` 
            WHERE {$whereClause}
            ORDER BY `sort_order` ASC, `name_zh` ASC
            LIMIT {$limit}
        ";

        return $this->db->fetchAll($sql, $params);
    }
}
?>
