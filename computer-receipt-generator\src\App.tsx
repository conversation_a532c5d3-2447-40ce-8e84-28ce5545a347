// import React from 'react';
import { Header, ActionButtons } from './components/layout';
import { ReceiptForm } from './components/form/ReceiptForm';
import { ReceiptPreview } from './components/preview/ReceiptPreview';
import { AppProvider } from './context/AppContext';
import './App.css';

function App() {
  // 收據內容元素的 ID，用於列印和 PDF 生成（只包含收據內容，不包含預覽界面）
  const receiptContentId = 'receipt-content-for-pdf';

  return (
    <AppProvider>
      <div className="app">
        {/* 應用程式標題 */}
        <Header className="app-header" />
        
        {/* 主要內容區域 */}
        <main className="app-main">
          {/* 表單區域 */}
          <div className="form-section">
            <ReceiptForm />
          </div>
          
          {/* 預覽區域 */}
          <div className="preview-section">
            <ReceiptPreview />
          </div>
        </main>
        
        {/* 操作按鈕區域 */}
        <footer className="app-footer">
          <ActionButtons
            receiptElementId={receiptContentId}
            className="action-buttons"
          />
        </footer>
      </div>
    </AppProvider>
  );
}

export default App;
