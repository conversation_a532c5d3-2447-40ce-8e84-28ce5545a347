import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TEST_IDS } from './test-config';
import type { ReceiptItem, StoreInfo, CustomerInfo } from '../types';

export class TestHelper {
  static user = userEvent.setup();

  // Store info helpers
  static async fillStoreInfo(storeInfo: Partial<StoreInfo> = {}) {
    const defaultStore = {
      name: 'Test Computer Store',
      address: '123 Test Street, Test City, TC 12345',
      phone: '(*************',
      email: '<EMAIL>',
      ...storeInfo,
    };

    if (defaultStore.name) {
      const nameInput = screen.getByTestId(TEST_IDS.STORE_NAME_INPUT);
      await this.user.clear(nameInput);
      await this.user.type(nameInput, defaultStore.name);
    }

    if (defaultStore.address) {
      const addressInput = screen.getByTestId(TEST_IDS.STORE_ADDRESS_INPUT);
      await this.user.clear(addressInput);
      await this.user.type(addressInput, defaultStore.address);
    }

    if (defaultStore.phone) {
      const phoneInput = screen.getByTestId(TEST_IDS.STORE_PHONE_INPUT);
      await this.user.clear(phoneInput);
      await this.user.type(phoneInput, defaultStore.phone);
    }

    if (defaultStore.email) {
      const emailInput = screen.getByTestId(TEST_IDS.STORE_EMAIL_INPUT);
      await this.user.clear(emailInput);
      await this.user.type(emailInput, defaultStore.email);
    }
  }

  // Customer info helpers
  static async fillCustomerInfo(customerInfo: Partial<CustomerInfo> = {}) {
    const defaultCustomer = {
      name: 'John Doe',
      phone: '(*************',
      email: '<EMAIL>',
      ...customerInfo,
    };

    if (defaultCustomer.name) {
      const nameInput = screen.getByTestId(TEST_IDS.CUSTOMER_NAME_INPUT);
      await this.user.clear(nameInput);
      await this.user.type(nameInput, defaultCustomer.name);
    }

    if (defaultCustomer.phone) {
      const phoneInput = screen.getByTestId(TEST_IDS.CUSTOMER_PHONE_INPUT);
      await this.user.clear(phoneInput);
      await this.user.type(phoneInput, defaultCustomer.phone);
    }

    if (defaultCustomer.email) {
      const emailInput = screen.getByTestId(TEST_IDS.CUSTOMER_EMAIL_INPUT);
      await this.user.clear(emailInput);
      await this.user.type(emailInput, defaultCustomer.email);
    }
  }

  // Item management helpers
  static async addItem(item: Partial<ReceiptItem> = {}) {
    const defaultItem = {
      name: 'Test CPU',
      category: 'hardware' as const,
      price: 299.99,
      quantity: 1,
      ...item,
    };

    // Click add item button to open form
    const addButton = screen.getByTestId(TEST_IDS.ADD_ITEM_BUTTON);
    await this.user.click(addButton);

    // Fill item form
    const nameInput = screen.getByTestId(TEST_IDS.ITEM_NAME_INPUT);
    await this.user.clear(nameInput);
    await this.user.type(nameInput, defaultItem.name);

    const categorySelect = screen.getByTestId(TEST_IDS.ITEM_CATEGORY_SELECT);
    await this.user.selectOptions(categorySelect, defaultItem.category);

    const priceInput = screen.getByTestId(TEST_IDS.ITEM_PRICE_INPUT);
    await this.user.clear(priceInput);
    await this.user.type(priceInput, defaultItem.price.toString());

    const quantityInput = screen.getByTestId(TEST_IDS.ITEM_QUANTITY_INPUT);
    await this.user.clear(quantityInput);
    await this.user.type(quantityInput, defaultItem.quantity.toString());

    // Save item
    const saveButton = screen.getByTestId(TEST_IDS.SAVE_ITEM_BUTTON);
    await this.user.click(saveButton);

    // Wait for item to be added
    await waitFor(() => {
      expect(screen.getByText(defaultItem.name)).toBeInTheDocument();
    });
  }

  static async editItem(itemName: string, updates: Partial<ReceiptItem>) {
    // Find the item row and click edit
    const itemRow = screen.getByText(itemName).closest('[data-testid="item-row"]');
    expect(itemRow).toBeInTheDocument();
    
    const editButton = itemRow?.querySelector('[data-testid="edit-item-button"]');
    expect(editButton).toBeInTheDocument();
    
    await this.user.click(editButton!);

    // Update fields
    if (updates.name) {
      const nameInput = screen.getByTestId(TEST_IDS.ITEM_NAME_INPUT);
      await this.user.clear(nameInput);
      await this.user.type(nameInput, updates.name);
    }

    if (updates.category) {
      const categorySelect = screen.getByTestId(TEST_IDS.ITEM_CATEGORY_SELECT);
      await this.user.selectOptions(categorySelect, updates.category);
    }

    if (updates.price !== undefined) {
      const priceInput = screen.getByTestId(TEST_IDS.ITEM_PRICE_INPUT);
      await this.user.clear(priceInput);
      await this.user.type(priceInput, updates.price.toString());
    }

    if (updates.quantity !== undefined) {
      const quantityInput = screen.getByTestId(TEST_IDS.ITEM_QUANTITY_INPUT);
      await this.user.clear(quantityInput);
      await this.user.type(quantityInput, updates.quantity.toString());
    }

    // Save changes
    const saveButton = screen.getByTestId(TEST_IDS.SAVE_ITEM_BUTTON);
    await this.user.click(saveButton);
  }

  static async deleteItem(itemName: string) {
    // Find the item row and click delete
    const itemRow = screen.getByText(itemName).closest('[data-testid="item-row"]');
    expect(itemRow).toBeInTheDocument();
    
    const deleteButton = itemRow?.querySelector('[data-testid="delete-item-button"]');
    expect(deleteButton).toBeInTheDocument();
    
    await this.user.click(deleteButton!);

    // Wait for item to be removed
    await waitFor(() => {
      expect(screen.queryByText(itemName)).not.toBeInTheDocument();
    });
  }

  // Tax rate helper
  static async setTaxRate(rate: number) {
    const taxRateInput = screen.getByTestId(TEST_IDS.TAX_RATE_INPUT);
    await this.user.clear(taxRateInput);
    await this.user.type(taxRateInput, (rate * 100).toString()); // Convert to percentage
  }

  // Action helpers
  static async clickPrint() {
    const printButton = screen.getByTestId(TEST_IDS.PRINT_BUTTON);
    await this.user.click(printButton);
  }

  static async clickSavePDF() {
    const savePDFButton = screen.getByTestId(TEST_IDS.SAVE_PDF_BUTTON);
    await this.user.click(savePDFButton);
  }

  // Assertion helpers
  static expectReceiptToShow(expectedData: {
    storeName?: string;
    customerName?: string;
    itemCount?: number;
    subtotal?: string;
    total?: string;
  }) {
    const receiptPreview = screen.getByTestId(TEST_IDS.RECEIPT_PREVIEW);
    expect(receiptPreview).toBeInTheDocument();

    if (expectedData.storeName) {
      expect(screen.getByText(expectedData.storeName)).toBeInTheDocument();
    }

    if (expectedData.customerName) {
      expect(screen.getByText(expectedData.customerName)).toBeInTheDocument();
    }

    if (expectedData.itemCount !== undefined) {
      const itemRows = screen.getAllByTestId(TEST_IDS.ITEM_ROW);
      expect(itemRows).toHaveLength(expectedData.itemCount);
    }

    if (expectedData.subtotal) {
      expect(screen.getByText(expectedData.subtotal)).toBeInTheDocument();
    }

    if (expectedData.total) {
      expect(screen.getByText(expectedData.total)).toBeInTheDocument();
    }
  }

  static expectValidationError(message: string) {
    expect(screen.getByText(message)).toBeInTheDocument();
  }

  static expectNoValidationErrors() {
    const errorElements = screen.queryAllByText(/此欄位為必填|請輸入有效的|價格必須為正數|數量必須為正整數/);
    expect(errorElements).toHaveLength(0);
  }
}