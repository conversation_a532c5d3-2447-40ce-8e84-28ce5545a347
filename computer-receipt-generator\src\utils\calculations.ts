import type { ReceiptItem } from '../types';

/**
 * 計算項目的小計（價格 × 數量）
 */
export const calculateItemSubtotal = (item: ReceiptItem): number => {
  return item.price * item.quantity;
};

/**
 * 計算所有項目的總小計
 */
export const calculateSubtotal = (items: ReceiptItem[]): number => {
  return items.reduce((total, item) => {
    return total + calculateItemSubtotal(item);
  }, 0);
};

/**
 * 計算稅額
 * @param subtotal 小計金額
 * @param taxRate 稅率（百分比，例如 5 代表 5%）
 */
export const calculateTaxAmount = (subtotal: number, taxRate: number): number => {
  return subtotal * (taxRate / 100);
};

/**
 * 計算總計（小計 + 稅額）
 */
export const calculateTotal = (subtotal: number, taxAmount: number): number => {
  return subtotal + taxAmount;
};

/**
 * 格式化貨幣顯示
 * @param amount 金額
 * @param currency 貨幣符號，預設為 '$'
 * @param locale 地區設定，預設為 'en-US'
 */
export const formatCurrency = (
  amount: number, 
  currency: string = '$', 
  locale: string = 'en-US'
): string => {
  // 確保金額為有效數字
  if (isNaN(amount) || !isFinite(amount)) {
    return `${currency}0.00`;
  }

  // 使用 Intl.NumberFormat 進行格式化
  const formatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  return `${currency}${formatter.format(amount)}`;
};

/**
 * 格式化美金顯示
 */
export const formatUSD = (amount: number): string => {
  return formatCurrency(amount, '$', 'en-US');
};

/**
 * 生成唯一的收據編號
 */
export const generateReceiptNumber = (): string => {
  // 生成6位數字，從000001開始
  const randomNum = Math.floor(Math.random() * 999999) + 1;
  const paddedNum = randomNum.toString().padStart(6, '0');
  return `KMS-UltraVIP-${paddedNum}`;
};

/**
 * 格式化日期時間顯示
 */
export const formatDateTime = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}/${month}/${day} ${hours}:${minutes}`;
};

/**
 * 格式化日期顯示（僅日期）
 */
export const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}/${month}/${day}`;
};

/**
 * 四捨五入到指定小數位數
 */
export const roundToDecimalPlaces = (number: number, decimalPlaces: number = 2): number => {
  const factor = Math.pow(10, decimalPlaces);
  return Math.round(number * factor) / factor;
};

/**
 * 計算完整的收據金額資訊
 */
export const calculateReceiptTotals = (items: ReceiptItem[], taxRate: number) => {
  const subtotal = calculateSubtotal(items);
  const taxAmount = calculateTaxAmount(subtotal, taxRate);
  const total = calculateTotal(subtotal, taxAmount);

  return {
    subtotal: roundToDecimalPlaces(subtotal),
    taxAmount: roundToDecimalPlaces(taxAmount),
    total: roundToDecimalPlaces(total),
  };
};

/**
 * 驗證並解析數字輸入
 */
export const parseNumber = (value: string | number): number => {
  if (typeof value === 'number') {
    return value;
  }
  
  const parsed = parseFloat(value);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * 驗證並解析整數輸入
 */
export const parseInteger = (value: string | number): number => {
  if (typeof value === 'number') {
    return Math.floor(value);
  }
  
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? 0 : parsed;
};