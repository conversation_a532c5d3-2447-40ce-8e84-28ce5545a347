// 核心資料類型定義

export interface ReceiptItem {
  id: string;
  name: string;
  category: 'PC Case' | 'CPU' | 'CPU Cooler' | 'GPU' | 'RAM' | 'SSD' | 'Motherboard' | 'PSU' | 'RGB' | 'Other' | 'Labor Fee' | 'OS' | 'OC' | 'Package' | 'Other Services';
  price: number;
  originalPrice?: number; // 原價，用於顯示折扣比較
  quantity: number;
  hidePriceOnReceipt?: boolean;
}

export interface StoreInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
}

export interface CustomerInfo {
  name: string;
  phone: string;
  email: string;
  address: string;
}

export interface LogoSettings {
  imageUrl: string | null;
  position: {
    x: number; // percentage from left
    y: number; // percentage from top
  };
  size: {
    width: number; // percentage of container width
    height: number; // percentage of container height
  };
  opacity: number; // 0-1
}

export interface ReceiptData {
  receiptNumber: string;
  date: Date | null;
  storeInfo: StoreInfo;
  customerInfo: CustomerInfo;
  items: ReceiptItem[];
  taxRate: number;
  subtotal: number;
  taxAmount: number;
  total: number;
  logoSettings: LogoSettings;
}

// 狀態管理相關類型
export interface AppState {
  receiptData: ReceiptData;
  isEditing: boolean;
  editingItemId: string | null;
}

export type AppAction =
  | { type: 'ADD_ITEM'; payload: ReceiptItem }
  | { type: 'UPDATE_ITEM'; payload: { id: string; item: Partial<ReceiptItem> } }
  | { type: 'DELETE_ITEM'; payload: string }
  | { type: 'MOVE_ITEM_UP'; payload: string }
  | { type: 'MOVE_ITEM_DOWN'; payload: string }
  | { type: 'UPDATE_STORE_INFO'; payload: Partial<StoreInfo> }
  | { type: 'UPDATE_CUSTOMER_INFO'; payload: Partial<CustomerInfo> }
  | { type: 'SET_TAX_RATE'; payload: number }
  | { type: 'SET_RECEIPT_DATE'; payload: Date | null }
  | { type: 'SET_RECEIPT_NUMBER'; payload: string }
  | { type: 'UPDATE_LOGO_SETTINGS'; payload: LogoSettings }
  | { type: 'SET_EDITING'; payload: { isEditing: boolean; itemId?: string | null } };

// 驗證相關類型
export interface ValidationError {
  field: string;
  message: string;
}

export interface ErrorState {
  itemErrors: ValidationError[];
  storeInfoErrors: ValidationError[];
  customerInfoErrors: ValidationError[];
}