import { describe, it, expect } from 'vitest';
import {
  calculateItemSubtotal,
  calculateSubtotal,
  calculateTaxAmount,
  calculateTotal,
  formatCurrency,
  formatUSD,
  generateReceiptNumber,
  formatDateTime,
  formatDate,
  roundToDecimalPlaces,
  calculateReceiptTotals,
  parseNumber,
  parseInteger,
} from './calculations';
import { ReceiptItem } from '../types';

// 測試用的收據項目
const mockItems: ReceiptItem[] = [
  {
    id: '1',
    name: 'CPU',
    category: 'hardware',
    price: 10000,
    quantity: 1,
  },
  {
    id: '2',
    name: '記憶體',
    category: 'hardware',
    price: 2500,
    quantity: 2,
  },
  {
    id: '3',
    name: '組裝服務',
    category: 'service',
    price: 1000,
    quantity: 1,
  },
];

describe('calculateItemSubtotal', () => {
  it('應該正確計算項目小計', () => {
    const item: ReceiptItem = {
      id: '1',
      name: 'Test Item',
      category: 'hardware',
      price: 100,
      quantity: 3,
    };
    
    expect(calculateItemSubtotal(item)).toBe(300);
  });

  it('應該處理小數價格', () => {
    const item: ReceiptItem = {
      id: '1',
      name: 'Test Item',
      category: 'hardware',
      price: 99.99,
      quantity: 2,
    };
    
    expect(calculateItemSubtotal(item)).toBe(199.98);
  });
});

describe('calculateSubtotal', () => {
  it('應該正確計算所有項目的總小計', () => {
    // 10000 * 1 + 2500 * 2 + 1000 * 1 = 16000
    expect(calculateSubtotal(mockItems)).toBe(16000);
  });

  it('應該處理空項目列表', () => {
    expect(calculateSubtotal([])).toBe(0);
  });
});

describe('calculateTaxAmount', () => {
  it('應該正確計算稅額', () => {
    expect(calculateTaxAmount(1000, 5)).toBe(50); // 5% of 1000
    expect(calculateTaxAmount(1000, 10)).toBe(100); // 10% of 1000
  });

  it('應該處理零稅率', () => {
    expect(calculateTaxAmount(1000, 0)).toBe(0);
  });

  it('應該處理小數稅率', () => {
    expect(calculateTaxAmount(1000, 5.5)).toBe(55); // 5.5% of 1000
  });
});

describe('calculateTotal', () => {
  it('應該正確計算總計', () => {
    expect(calculateTotal(1000, 50)).toBe(1050);
    expect(calculateTotal(1000, 0)).toBe(1000);
  });
});

describe('formatCurrency', () => {
  it('應該正確格式化貨幣', () => {
    expect(formatCurrency(1234.56)).toBe('$1,234.56');
    expect(formatCurrency(1000)).toBe('$1,000.00');
    expect(formatCurrency(0)).toBe('$0.00');
  });

  it('應該支援自訂貨幣符號', () => {
    expect(formatCurrency(1234.56, '€')).toBe('€1,234.56');
  });

  it('應該處理無效數字', () => {
    expect(formatCurrency(NaN)).toBe('$0.00');
    expect(formatCurrency(Infinity)).toBe('$0.00');
  });
});

describe('formatUSD', () => {
  it('should correctly format USD currency', () => {
    expect(formatUSD(1234.56)).toBe('$1,234.56');
    expect(formatUSD(1000)).toBe('$1,000.00');
  });
});

describe('generateReceiptNumber', () => {
  it('應該生成唯一的收據編號', () => {
    const receipt1 = generateReceiptNumber();
    const receipt2 = generateReceiptNumber();
    
    expect(receipt1).toMatch(/^RCP-[A-Z0-9]+-[A-Z0-9]+$/);
    expect(receipt2).toMatch(/^RCP-[A-Z0-9]+-[A-Z0-9]+$/);
    expect(receipt1).not.toBe(receipt2);
  });
});

describe('formatDateTime', () => {
  it('應該正確格式化日期時間', () => {
    const date = new Date('2024-01-15T14:30:00');
    expect(formatDateTime(date)).toBe('2024/01/15 14:30');
  });
});

describe('formatDate', () => {
  it('應該正確格式化日期', () => {
    const date = new Date('2024-01-15T14:30:00');
    expect(formatDate(date)).toBe('2024/01/15');
  });
});

describe('roundToDecimalPlaces', () => {
  it('應該正確四捨五入到指定小數位數', () => {
    expect(roundToDecimalPlaces(1.2345, 2)).toBe(1.23);
    expect(roundToDecimalPlaces(1.2355, 2)).toBe(1.24);
    expect(roundToDecimalPlaces(1.2345, 0)).toBe(1);
    expect(roundToDecimalPlaces(1.2345, 3)).toBe(1.235);
  });

  it('應該預設四捨五入到兩位小數', () => {
    expect(roundToDecimalPlaces(1.2345)).toBe(1.23);
  });
});

describe('calculateReceiptTotals', () => {
  it('應該正確計算完整的收據金額資訊', () => {
    const result = calculateReceiptTotals(mockItems, 5);
    
    expect(result.subtotal).toBe(16000);
    expect(result.taxAmount).toBe(800); // 5% of 16000
    expect(result.total).toBe(16800);
  });

  it('應該處理零稅率', () => {
    const result = calculateReceiptTotals(mockItems, 0);
    
    expect(result.subtotal).toBe(16000);
    expect(result.taxAmount).toBe(0);
    expect(result.total).toBe(16000);
  });

  it('應該正確四捨五入結果', () => {
    const items: ReceiptItem[] = [
      {
        id: '1',
        name: 'Test',
        category: 'hardware',
        price: 10.33,
        quantity: 3,
      },
    ];
    
    const result = calculateReceiptTotals(items, 5.5);
    
    expect(result.subtotal).toBe(30.99);
    expect(result.taxAmount).toBe(1.70); // 5.5% of 30.99, rounded
    expect(result.total).toBe(32.69);
  });
});

describe('parseNumber', () => {
  it('應該正確解析數字', () => {
    expect(parseNumber(123)).toBe(123);
    expect(parseNumber('123')).toBe(123);
    expect(parseNumber('123.45')).toBe(123.45);
    expect(parseNumber('abc')).toBe(0);
    expect(parseNumber('')).toBe(0);
  });
});

describe('parseInteger', () => {
  it('應該正確解析整數', () => {
    expect(parseInteger(123)).toBe(123);
    expect(parseInteger(123.99)).toBe(123);
    expect(parseInteger('123')).toBe(123);
    expect(parseInteger('123.99')).toBe(123);
    expect(parseInteger('abc')).toBe(0);
    expect(parseInteger('')).toBe(0);
  });
});