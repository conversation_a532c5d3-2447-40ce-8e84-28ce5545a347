import React from 'react';
import styles from './Header.module.css';

interface HeaderProps {
  className?: string;
}

export const Header: React.FC<HeaderProps> = ({ className = '' }) => {
  return (
    <header className={`${styles.header} ${className}`}>
      <div className={styles.container}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>PC Receipt Generator</h1>
          <p className={styles.subtitle}>
            Professional computer parts sales receipt generator
          </p>
        </div>
        
        <div className={styles.infoSection}>
          <div className={styles.feature}>
            <span className={styles.featureIcon}>📄</span>
            <span className={styles.featureText}>Letter Paper Format</span>
          </div>
          <div className={styles.feature}>
            <span className={styles.featureIcon}>🖨️</span>
            <span className={styles.featureText}>Print Friendly</span>
          </div>
          <div className={styles.feature}>
            <span className={styles.featureIcon}>💾</span>
            <span className={styles.featureText}>PDF Export</span>
          </div>
        </div>
      </div>
    </header>
  );
};