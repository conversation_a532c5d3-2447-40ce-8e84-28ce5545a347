import React from 'react';
import { Button } from '../ui';
import { useReceipt } from '../../hooks/useReceipt';
import type { ReceiptItem } from '../../types';
import { formatUSD, calculateItemSubtotal } from '../../utils/calculations';
import { getDiscountPercentage } from '../../data/presetItems';
import styles from './ItemRow.module.css';

interface ItemRowProps {
  item: ReceiptItem;
  index: number;
  totalItems: number;
  onEdit?: (item: ReceiptItem) => void;
}

export const ItemRow: React.FC<ItemRowProps> = ({ item, index, totalItems, onEdit }) => {
  const { deleteItem, startEditingItem, moveItemUp, moveItemDown } = useReceipt();

  const handleEdit = () => {
    if (onEdit) {
      onEdit(item);
    } else {
      startEditingItem(item.id);
    }
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      deleteItem(item.id);
    }
  };

  const handleMoveUp = () => {
    moveItemUp(item.id);
  };

  const handleMoveDown = () => {
    moveItemDown(item.id);
  };

  const subtotal = calculateItemSubtotal(item);
  const categoryLabel = item.category;
  const discountPercentage = getDiscountPercentage(item.price, item.originalPrice);
  const hasDiscount = discountPercentage > 0;

  return (
    <tr className={styles.row}>
      <td className={styles.nameCell}>
        <div className={styles.itemName}>{item.name}</div>
        <div className={styles.itemCategory}>
          {categoryLabel}
          {hasDiscount && (
            <span className={styles.discountBadge}>
              -{discountPercentage}% OFF
            </span>
          )}
        </div>
      </td>
      <td className={styles.priceCell}>
        <div className={styles.priceContainer}>
          <div className={styles.currentPrice}>{formatUSD(item.price)}</div>
          {hasDiscount && (
            <div className={styles.originalPrice}>
              {formatUSD(item.originalPrice!)}
            </div>
          )}
        </div>
      </td>
      <td className={styles.quantityCell}>
        {item.quantity}
      </td>
      <td className={styles.subtotalCell}>
        {formatUSD(subtotal)}
      </td>
      <td className={styles.actionsCell}>
        <div className={styles.actions}>
          <div className={styles.moveButtons}>
            <Button
              size="small"
              variant="outline"
              onClick={handleMoveUp}
              disabled={index === 0}
              title="Move Up"
            >
              ↑
            </Button>
            <Button
              size="small"
              variant="outline"
              onClick={handleMoveDown}
              disabled={index === totalItems - 1}
              title="Move Down"
            >
              ↓
            </Button>
          </div>
          <div className={styles.editButtons}>
            <Button
              size="small"
              variant="outline"
              onClick={handleEdit}
              title="Edit Item"
            >
              Edit
            </Button>
            <Button
              size="small"
              variant="danger"
              onClick={handleDelete}
              title="Delete Item"
            >
              Delete
            </Button>
          </div>
        </div>
      </td>
    </tr>
  );
};