import React, { useState } from 'react';
import { Button } from '../ui';
import { useReceipt } from '../../hooks/useReceipt';
import { ItemForm } from './ItemForm';
import { ItemRow } from './ItemRow';
import { ItemEditModal } from './ItemEditModal';
import PresetItemSelector from './PresetItemSelectorNew';
import type { ReceiptItem } from '../../types';
import type { PresetItem } from '../../data/presetItems';
import styles from './ItemList.module.css';

export const ItemList: React.FC = () => {
  const { receiptData, isEditing, editingItem, addItem, updateItem } = useReceipt();
  const [showForm, setShowForm] = useState(false);
  const [editModalItem, setEditModalItem] = useState<ReceiptItem | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [showPresets, setShowPresets] = useState(false);

  const handleAddNew = () => {
    setShowForm(true);
  };

  const handleCancelAdd = () => {
    setShowForm(false);
  };

  const handleEditItem = (item: ReceiptItem) => {
    setEditModalItem(item);
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = (updatedItem: ReceiptItem) => {
    updateItem(updatedItem.id, updatedItem);
    setEditModalItem(null);
    setIsEditModalOpen(false);
  };

  const handleCancelEdit = () => {
    setEditModalItem(null);
    setIsEditModalOpen(false);
  };

  const handleSelectPresetItem = (presetItem: PresetItem) => {
    addItem({
      name: presetItem.name,
      category: presetItem.category,
      price: presetItem.price,
      originalPrice: presetItem.originalPrice,
      quantity: 1,
      hidePriceOnReceipt: false,
    });
  };

  const hasItems = receiptData.items.length > 0;

  return (
    <div className={styles.container}>
      {/* 預設項目選擇器 */}
      <PresetItemSelector
        onSelectItem={handleSelectPresetItem}
        isVisible={showPresets}
        onToggle={() => setShowPresets(!showPresets)}
      />

      <div className={styles.header}>
        <h2>Receipt Items</h2>
        <div className={styles.headerButtons}>
          {!showPresets && (
            <Button onClick={() => setShowPresets(true)} variant="secondary">
              Show Presets
            </Button>
          )}
          {!showForm && !isEditing && (
            <Button onClick={handleAddNew} variant="primary">
              Add Item
            </Button>
          )}
        </div>
      </div>

      {(showForm || isEditing) && (
        <ItemForm
          editingItem={editingItem}
          onCancel={handleCancelAdd}
        />
      )}

      {hasItems ? (
        <div className={styles.tableContainer}>
          <table className={styles.table}>
            <thead>
              <tr className={styles.headerRow}>
                <th className={styles.nameHeader}>Item Name</th>
                <th className={styles.priceHeader}>Unit Price</th>
                <th className={styles.quantityHeader}>Quantity</th>
                <th className={styles.subtotalHeader}>Subtotal</th>
                <th className={styles.actionsHeader}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {receiptData.items.map((item, index) => (
                <ItemRow
                  key={item.id}
                  item={item}
                  index={index}
                  totalItems={receiptData.items.length}
                  onEdit={handleEditItem}
                />
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className={styles.emptyState}>
          <div className={styles.emptyIcon}>📝</div>
          <h3>No items added yet</h3>
          <p>Click the "Add Item" button to add your first receipt item</p>
          {!showForm && (
            <Button onClick={handleAddNew} variant="primary">
              Add Item
            </Button>
          )}
        </div>
      )}

      {/* 編輯彈窗 */}
      <ItemEditModal
        isOpen={isEditModalOpen}
        onClose={handleCancelEdit}
        item={editModalItem}
        onSave={handleSaveEdit}
      />
    </div>
  );
};