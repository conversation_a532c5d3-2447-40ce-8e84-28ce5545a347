import { describe, it, expect } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useReceipt } from './useReceipt';
import { AppProvider } from '../context/AppContext';
import { ReactNode } from 'react';

// 測試包裝器
const wrapper = ({ children }: { children: ReactNode }) => (
  <AppProvider>{children}</AppProvider>
);

describe('useReceipt', () => {
  it('應該提供初始狀態', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    expect(result.current.receiptData.items).toEqual([]);
    expect(result.current.receiptData.taxRate).toBe(5);
    expect(result.current.isEditing).toBe(false);
    expect(result.current.editingItemId).toBe(null);
    expect(result.current.totals.subtotal).toBe(0);
    expect(result.current.totals.total).toBe(0);
    expect(result.current.isReceiptValid).toBe(false);
  });

  it('應該能夠新增項目', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    act(() => {
      result.current.addItem({
        name: 'Test CPU',
        category: 'hardware',
        price: 10000,
        quantity: 1,
      });
    });

    expect(result.current.receiptData.items).toHaveLength(1);
    expect(result.current.receiptData.items[0].name).toBe('Test CPU');
    expect(result.current.receiptData.items[0].price).toBe(10000);
    expect(result.current.receiptData.items[0].id).toBeDefined();
  });

  it('應該能夠更新項目', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    // 先新增一個項目
    act(() => {
      result.current.addItem({
        name: 'Test CPU',
        category: 'hardware',
        price: 10000,
        quantity: 1,
      });
    });

    const itemId = result.current.receiptData.items[0].id;

    // 更新項目
    act(() => {
      result.current.updateItem(itemId, {
        name: 'Updated CPU',
        price: 12000,
      });
    });

    expect(result.current.receiptData.items[0].name).toBe('Updated CPU');
    expect(result.current.receiptData.items[0].price).toBe(12000);
    expect(result.current.receiptData.items[0].quantity).toBe(1); // 未更新的欄位應保持不變
  });

  it('應該能夠刪除項目', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    // 先新增一個項目
    act(() => {
      result.current.addItem({
        name: 'Test CPU',
        category: 'hardware',
        price: 10000,
        quantity: 1,
      });
    });

    const itemId = result.current.receiptData.items[0].id;

    // 刪除項目
    act(() => {
      result.current.deleteItem(itemId);
    });

    expect(result.current.receiptData.items).toHaveLength(0);
  });

  it('應該能夠更新商店資訊', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    act(() => {
      result.current.updateStoreInfo({
        name: 'Test Store',
        address: 'Test Address',
      });
    });

    expect(result.current.receiptData.storeInfo.name).toBe('Test Store');
    expect(result.current.receiptData.storeInfo.address).toBe('Test Address');
  });

  it('應該能夠更新客戶資訊', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    act(() => {
      result.current.updateCustomerInfo({
        name: 'Test Customer',
        phone: '0912345678',
      });
    });

    expect(result.current.receiptData.customerInfo.name).toBe('Test Customer');
    expect(result.current.receiptData.customerInfo.phone).toBe('0912345678');
  });

  it('應該能夠設定稅率', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    act(() => {
      result.current.setTaxRate(10);
    });

    expect(result.current.receiptData.taxRate).toBe(10);
  });

  it('應該正確計算總額', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    act(() => {
      result.current.addItem({
        name: 'CPU',
        category: 'hardware',
        price: 10000,
        quantity: 1,
      });
      result.current.addItem({
        name: 'RAM',
        category: 'hardware',
        price: 2500,
        quantity: 2,
      });
      result.current.setTaxRate(5);
    });

    // 小計: 10000 + (2500 * 2) = 15000
    // 稅額: 15000 * 0.05 = 750
    // 總計: 15000 + 750 = 15750
    expect(result.current.totals.subtotal).toBe(15000);
    expect(result.current.totals.taxAmount).toBe(750);
    expect(result.current.totals.total).toBe(15750);
  });

  it('應該能夠管理編輯狀態', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    // 先新增一個項目
    act(() => {
      result.current.addItem({
        name: 'Test CPU',
        category: 'hardware',
        price: 10000,
        quantity: 1,
      });
    });

    const itemId = result.current.receiptData.items[0].id;

    // 開始編輯
    act(() => {
      result.current.startEditingItem(itemId);
    });

    expect(result.current.isEditing).toBe(true);
    expect(result.current.editingItemId).toBe(itemId);
    expect(result.current.editingItem).toEqual(result.current.receiptData.items[0]);

    // 停止編輯
    act(() => {
      result.current.stopEditing();
    });

    expect(result.current.isEditing).toBe(false);
    expect(result.current.editingItemId).toBe(null);
    expect(result.current.editingItem).toBe(null);
  });

  it('應該正確驗證收據有效性', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    // 初始狀態應該無效
    expect(result.current.isReceiptValid).toBe(false);

    // 只有項目，仍然無效
    act(() => {
      result.current.addItem({
        name: 'Test CPU',
        category: 'hardware',
        price: 10000,
        quantity: 1,
      });
    });
    expect(result.current.isReceiptValid).toBe(false);

    // 加上商店資訊，仍然無效
    act(() => {
      result.current.updateStoreInfo({
        name: 'Test Store',
      });
    });
    expect(result.current.isReceiptValid).toBe(false);

    // 加上客戶資訊，應該有效
    act(() => {
      result.current.updateCustomerInfo({
        name: 'Test Customer',
      });
    });
    expect(result.current.isReceiptValid).toBe(true);
  });

  it('應該能夠清空收據', () => {
    const { result } = renderHook(() => useReceipt(), { wrapper });

    let itemId: string;

    // 先設定一些資料
    act(() => {
      result.current.addItem({
        name: 'Test CPU',
        category: 'hardware',
        price: 10000,
        quantity: 1,
      });
    });

    act(() => {
      itemId = result.current.receiptData.items[0].id;
      result.current.updateStoreInfo({
        name: 'Test Store',
        address: 'Test Address',
      });
      result.current.updateCustomerInfo({
        name: 'Test Customer',
        phone: '0912345678',
      });
      result.current.setTaxRate(10);
      result.current.startEditingItem(itemId);
    });

    // 清空收據
    act(() => {
      result.current.clearReceipt();
    });

    expect(result.current.receiptData.items).toHaveLength(0);
    expect(result.current.receiptData.storeInfo.name).toBe('');
    expect(result.current.receiptData.customerInfo.name).toBe('');
    expect(result.current.receiptData.taxRate).toBe(5);
    expect(result.current.isEditing).toBe(false);
  });
});