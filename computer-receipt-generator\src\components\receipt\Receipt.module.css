/* Letter 紙張尺寸: 8.5" x 11" = 816px x 1056px at 96 DPI */

.receiptContainer {
  /* 基本容器設定 */
  max-width: 8.5in;
  min-height: 11in;
  margin: 0 auto;
  padding: 0.5in;
  background-color: #ffffff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  font-family: 'Arial', 'Microsoft JhengHei', sans-serif;
  font-size: 11pt;
  line-height: 1.3;
  color: #000000;

  /* 確保內容不會超出邊界 */
  box-sizing: border-box;
  overflow: hidden;

  /* Logo 定位支援 */
  position: relative;

  /* PDF 生成優化 */
  page-break-inside: avoid;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

.receiptContent {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.receiptHeader {
  flex-shrink: 0;
  margin-bottom: 1rem;
}

.receiptBody {
  flex: 1;
  margin-bottom: 1rem;
}

.receiptFooter {
  flex-shrink: 0;
  margin-top: auto;
}

/* 響應式設計 - 螢幕顯示 */
@media screen {
  .receiptContainer {
    /* 在螢幕上顯示時的調整 */
    margin: 1rem auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}

/* 平板設備 */
@media screen and (max-width: 1024px) {
  .receiptContainer {
    max-width: 95%;
    margin: 1rem auto;
    padding: 0.6in;
    font-size: 11pt;
  }
}

/* 手機設備 */
@media screen and (max-width: 768px) {
  .receiptContainer {
    max-width: 100%;
    margin: 0.5rem auto;
    padding: 0.5in;
    font-size: 10pt;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .receiptHeader {
    margin-bottom: 1rem;
  }
  
  .receiptBody {
    margin-bottom: 1rem;
  }
}

/* 小螢幕手機 */
@media screen and (max-width: 480px) {
  .receiptContainer {
    margin: 0.25rem;
    padding: 0.4in;
    font-size: 9pt;
  }
  
  .receiptHeader {
    margin-bottom: 0.75rem;
  }
  
  .receiptBody {
    margin-bottom: 0.75rem;
  }
}

/* 列印樣式 - Letter 紙張尺寸最佳化 */
@media print {
  /* 重置所有邊距和背景 */
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
  
  body {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .receiptContainer {
    /* Letter 紙張尺寸設定 */
    width: 8.5in !important;
    max-width: 8.5in !important;
    min-height: 11in !important;
    margin: 0 !important;
    padding: 0.5in !important;
    
    /* 移除螢幕樣式 */
    box-shadow: none !important;
    border: none !important;
    border-radius: 0 !important;
    background-color: transparent !important;
    
    /* 字體設定 */
    font-size: 11pt !important;
    line-height: 1.3 !important;
    color: #000 !important;
    
    /* 分頁控制 */
    page-break-inside: avoid;
    page-break-after: auto;
  }
  
  .receiptContent {
    height: auto !important;
  }
  
  .receiptHeader {
    margin-bottom: 12pt !important;
    page-break-inside: avoid;
    page-break-after: avoid;
  }
  
  .receiptBody {
    margin-bottom: 12pt !important;
    page-break-inside: auto;
  }
  
  .receiptFooter {
    margin-top: 12pt !important;
    page-break-inside: avoid;
    page-break-before: avoid;
  }
  
  /* 隱藏不需要列印的元素 */
  .noPrint {
    display: none !important;
  }
  
  /* 確保表格在列印時正確顯示 */
  table {
    page-break-inside: auto;
  }
  
  tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }
  
  thead {
    display: table-header-group;
  }
  
  tfoot {
    display: table-footer-group;
  }
}

/* 列印頁面設定 */
@page {
  size: letter;
  margin: 0.5in;
  
  /* 頁首和頁尾 */
  @top-center {
    content: "";
  }
  
  @bottom-center {
    content: "";
  }
}

/* 分頁控制類別 */
.pageBreakBefore {
  page-break-before: always;
}

.pageBreakAfter {
  page-break-after: always;
}

.pageBreakInside {
  page-break-inside: avoid;
}

/* 列印時的顏色和背景 */
@media print {
  .receiptContainer {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  
  /* 確保邊框和背景色在列印時顯示 */
  .printBorder {
    border: 1px solid #000 !important;
  }
  
  .printBackground {
    background-color: #f5f5f5 !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

/* 高解析度列印支援 */
@media print and (min-resolution: 150dpi) {
  .receiptContainer {
    font-size: 10pt !important;
  }
}

@media print and (min-resolution: 300dpi) {
  .receiptContainer {
    font-size: 9pt !important;
  }
}

/* 列印預覽模式 */
.printPreview {
  background-color: #f8f9fa;
  border: 2px dashed #6c757d;
  position: relative;
}

.printPreview::before {
  content: "列印預覽模式";
  position: absolute;
  top: -30px;
  left: 0;
  background-color: #6c757d;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px 4px 0 0;
}

/* 無障礙支援 */
@media (prefers-reduced-motion: reduce) {
  .receiptContainer {
    transition: none;
  }
}

@media (prefers-contrast: high) {
  .receiptContainer {
    border: 2px solid #000;
    background-color: #fff;
    color: #000;
  }
}

/* PDF 生成專用樣式 */
.pdfOptimized {
  /* 移除螢幕顯示的陰影和邊框 */
  box-shadow: none !important;
  border: none !important;
  border-radius: 0 !important;

  /* 確保背景色在 PDF 中顯示 */
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;

  /* 頁面分割優化 */
  page-break-inside: auto;
  orphans: 3;
  widows: 3;
}

/* 頁面分割控制 */
.pageBreakBefore {
  page-break-before: always;
}

.pageBreakAfter {
  page-break-after: always;
}

.pageBreakAvoid {
  page-break-inside: avoid;
}

/* 深色模式支援（螢幕顯示） */
@media (prefers-color-scheme: dark) and (not print) {
  .receiptContainer {
    background-color: #1a1a1a;
    color: #e0e0e0;
    border-color: #444;
    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);
  }
}