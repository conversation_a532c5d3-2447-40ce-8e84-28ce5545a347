import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../../App';

// Mock html2pdf to avoid issues in tests
vi.mock('html2pdf.js', () => ({
  default: () => ({
    set: () => ({
      from: () => ({
        save: vi.fn().mockResolvedValue(undefined)
      })
    })
  })
}));

describe('Basic Receipt Flow Integration Tests', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
  });

  const fillBasicInfo = async () => {
    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');
  };

  const addTestItem = async (name = '測試項目', price = '1000', quantity = '1') => {
    // 使用第一個新增項目按鈕
    const addButtons = screen.getAllByText('新增項目');
    await user.click(addButtons[0]);
    
    await user.type(screen.getByLabelText(/項目名稱/i), name);
    await user.type(screen.getByLabelText(/價格/i), price);
    await user.type(screen.getByLabelText(/數量/i), quantity);
    await user.click(screen.getByRole('button', { name: /新增/i }));
  };

  it('renders the main application components', () => {
    render(<App />);
    
    // 檢查主要組件是否存在
    expect(screen.getByText('電腦收據生成器')).toBeInTheDocument();
    expect(screen.getByText('收據生成器')).toBeInTheDocument();
    expect(screen.getByText('收據預覽')).toBeInTheDocument();
    expect(screen.getByText('列印收據')).toBeInTheDocument();
    expect(screen.getByText('儲存 PDF')).toBeInTheDocument();
  });

  it('shows empty state initially', () => {
    render(<App />);
    
    expect(screen.getByText('請先新增項目來查看收據預覽')).toBeInTheDocument();
    
    // 按鈕應該是禁用的
    const printButton = screen.getByRole('button', { name: /列印收據/i });
    const pdfButton = screen.getByRole('button', { name: /儲存 PDF/i });
    expect(printButton).toBeDisabled();
    expect(pdfButton).toBeDisabled();
  });

  it('allows filling store and customer information', async () => {
    render(<App />);
    
    await fillBasicInfo();
    
    // 驗證資訊已填入
    expect(screen.getByDisplayValue('測試商店')).toBeInTheDocument();
    expect(screen.getByDisplayValue('測試客戶')).toBeInTheDocument();
  });

  it('allows adding items and calculates totals', async () => {
    render(<App />);
    
    await fillBasicInfo();
    await addTestItem('測試項目', '1000', '1');
    
    // 驗證項目已新增
    await waitFor(() => {
      expect(screen.getByText('測試項目')).toBeInTheDocument();
    });
    
    // 驗證計算結果
    await waitFor(() => {
      expect(screen.getByText('NT$ 1,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 50')).toBeInTheDocument();    // 稅額 (5%)
      expect(screen.getByText('NT$ 1,050')).toBeInTheDocument(); // 總計
    });
  });

  it('enables action buttons when receipt is complete', async () => {
    render(<App />);
    
    await fillBasicInfo();
    await addTestItem();
    
    // 按鈕應該變為啟用
    await waitFor(() => {
      const printButton = screen.getByRole('button', { name: /列印收據/i });
      const pdfButton = screen.getByRole('button', { name: /儲存 PDF/i });
      expect(printButton).not.toBeDisabled();
      expect(pdfButton).not.toBeDisabled();
    });
  });

  it('calculates multiple items correctly', async () => {
    render(<App />);
    
    await fillBasicInfo();
    await addTestItem('項目 1', '1000', '1');
    await addTestItem('項目 2', '2000', '1');
    
    // 驗證計算結果
    await waitFor(() => {
      expect(screen.getByText('NT$ 3,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 150')).toBeInTheDocument();   // 稅額
      expect(screen.getByText('NT$ 3,150')).toBeInTheDocument(); // 總計
    });
  });

  it('handles tax rate changes', async () => {
    render(<App />);
    
    await fillBasicInfo();
    await addTestItem('測試項目', '1000', '1');
    
    // 修改稅率為 10%
    const taxRateInput = screen.getByLabelText(/稅率/i);
    await user.clear(taxRateInput);
    await user.type(taxRateInput, '10');
    
    // 驗證重新計算
    await waitFor(() => {
      expect(screen.getByText('NT$ 1,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 100')).toBeInTheDocument();   // 稅額 (10%)
      expect(screen.getByText('NT$ 1,100')).toBeInTheDocument(); // 總計
    });
  });

  it('validates required fields', async () => {
    render(<App />);
    
    // 嘗試新增空項目
    const addButtons = screen.getAllByText('新增項目');
    await user.click(addButtons[0]);
    
    // 嘗試儲存空表單
    await user.click(screen.getByRole('button', { name: /新增/i }));
    
    // 應該顯示驗證錯誤
    await waitFor(() => {
      expect(screen.getByText(/項目名稱為必填欄位/i)).toBeInTheDocument();
      expect(screen.getByText(/單價必須大於 0/i)).toBeInTheDocument();
    });
  });

  it('shows validation warnings for incomplete receipt', () => {
    render(<App />);
    
    expect(screen.getByText('收據資訊不完整')).toBeInTheDocument();
    expect(screen.getByText(/請確保已填寫商店資訊、客戶資訊/)).toBeInTheDocument();
  });

  it('handles decimal calculations correctly', async () => {
    render(<App />);
    
    await fillBasicInfo();
    await addTestItem('小數項目', '99.99', '1');
    
    await waitFor(() => {
      expect(screen.getByText('NT$ 99.99')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 5')).toBeInTheDocument();     // 稅額 (四捨五入)
      expect(screen.getByText('NT$ 104.99')).toBeInTheDocument(); // 總計
    });
  });
});