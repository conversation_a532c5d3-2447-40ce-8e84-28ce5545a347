import React from 'react';
import { formatCurrency } from '../../utils/calculations';
import styles from './ReceiptFooter.module.css';

interface ReceiptFooterProps {
  subtotal: number;
  taxAmount: number;
  total: number;
  taxRate: number;
  date?: Date | null;
}

export const ReceiptFooter: React.FC<ReceiptFooterProps> = ({
  subtotal,
  taxAmount,
  total,
  taxRate,
  date,
}) => {
  return (
    <div className={styles.footer}>
      {/* 計算摘要 */}
      <div className={styles.calculationSummary}>
        <div className={styles.summaryRow}>
          <span className={styles.summaryLabel}>Subtotal:</span>
          <span className={styles.summaryValue}>{formatCurrency(subtotal)}</span>
        </div>
        
        <div className={styles.summaryRow}>
          <span className={styles.summaryLabel}>
            Tax ({taxRate}%):
          </span>
          <span className={styles.summaryValue}>{formatCurrency(taxAmount)}</span>
        </div>
        
        <div className={styles.totalRow}>
          <span className={styles.totalLabel}>Total:</span>
          <span className={styles.totalValue}>{formatCurrency(total)}</span>
        </div>
      </div>

      {/* 保固訊息 */}
      <div className={styles.footerMessage}>
        <div className={styles.warrantyMessage}>
          Products come with 3-year warranty
        </div>
      </div>

      {/* 簽名欄位 */}
      <div className={styles.signatureSection}>
        <div className={styles.signatureField}>
          <div className={styles.signatureLine}></div>
          <div className={styles.signatureLabel}>Seller Signature</div>
        </div>
        <div className={styles.signatureField}>
          <div className={styles.signatureLine}></div>
          <div className={styles.signatureLabel}>Buyer Signature</div>
        </div>
      </div>

      {/* 日期顯示 */}
      {date && (
        <div className={styles.dateSection}>
          <div className={styles.dateLabel}>Date: {date.toLocaleDateString()}</div>
        </div>
      )}
    </div>
  );
};