import React, { useState, useEffect } from 'react';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import type { ReceiptItem } from '../../types';
import styles from './ItemEditModal.module.css';

interface ItemEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: ReceiptItem | null;
  onSave: (updatedItem: ReceiptItem) => void;
}

export const ItemEditModal: React.FC<ItemEditModalProps> = ({
  isOpen,
  onClose,
  item,
  onSave,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    category: 'PC Case' as ReceiptItem['category'],
    price: 0,
    originalPrice: undefined as number | undefined,
    quantity: 1,
    hidePriceOnReceipt: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 當 item 改變時更新表單數據
  useEffect(() => {
    if (item) {
      setFormData({
        name: item.name,
        category: item.category,
        price: item.price,
        originalPrice: item.originalPrice,
        quantity: item.quantity,
        hidePriceOnReceipt: item.hidePriceOnReceipt || false,
      });
      setErrors({});
    }
  }, [item]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Item name is required';
    }

    if (formData.price < 0) {
      newErrors.price = 'Price must be non-negative';
    }

    if (formData.originalPrice !== undefined && formData.originalPrice < formData.price) {
      newErrors.originalPrice = 'Original price must be greater than or equal to current price';
    }

    if (formData.quantity < 1) {
      newErrors.quantity = 'Quantity must be at least 1';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!item || !validateForm()) return;

    const updatedItem: ReceiptItem = {
      ...item,
      name: formData.name.trim(),
      category: formData.category,
      price: formData.price,
      originalPrice: formData.originalPrice,
      quantity: formData.quantity,
      hidePriceOnReceipt: formData.hidePriceOnReceipt,
    };

    onSave(updatedItem);
    onClose();
  };

  const handleCancel = () => {
    setErrors({});
    onClose();
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // 清除該欄位的錯誤
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  if (!item) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Edit Item"
      onConfirm={handleSave}
      onCancel={handleCancel}
      confirmText="Save Changes"
      cancelText="Cancel"
      size="medium"
    >
      <div className={styles.form}>
        <div className={styles.formGroup}>
          <Input
            label="Item Name"
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            error={errors.name}
            placeholder="Enter item name"
            required
          />
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Category</label>
          <select
            className={styles.select}
            value={formData.category}
            onChange={(e) => handleInputChange('category', e.target.value as ReceiptItem['category'])}
          >
            <option value="PC Case">PC Case</option>
            <option value="CPU">CPU</option>
            <option value="CPU Cooler">CPU Cooler</option>
            <option value="GPU">GPU</option>
            <option value="RAM">RAM</option>
            <option value="SSD">SSD</option>
            <option value="Motherboard">Motherboard</option>
            <option value="PSU">PSU</option>
            <option value="RGB">RGB</option>
            <option value="Other">Other</option>
            <option value="Labor Fee">Labor Fee</option>
            <option value="OS">OS</option>
            <option value="OC">OC</option>
            <option value="Package">Package</option>
            <option value="Other Services">Other Services</option>
          </select>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <Input
              label="Price"
              type="number"
              value={formData.price.toString()}
              onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
              error={errors.price}
              placeholder="0.00"
              step="0.01"
              min="0"
              required
            />
          </div>

          <div className={styles.formGroup}>
            <Input
              label="Original Price (Optional)"
              type="number"
              value={formData.originalPrice?.toString() || ''}
              onChange={(e) => {
                const value = e.target.value;
                handleInputChange('originalPrice', value ? parseFloat(value) : undefined);
              }}
              error={errors.originalPrice}
              placeholder="0.00"
              step="0.01"
              min="0"
            />
          </div>
        </div>

        <div className={styles.formGroup}>
          <Input
            label="Quantity"
            type="number"
            value={formData.quantity.toString()}
            onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 1)}
            error={errors.quantity}
            placeholder="1"
            min="1"
            required
          />
        </div>

        <div className={styles.checkboxGroup}>
          <label className={styles.checkboxLabel}>
            <input
              type="checkbox"
              checked={formData.hidePriceOnReceipt}
              onChange={(e) => handleInputChange('hidePriceOnReceipt', e.target.checked)}
              className={styles.checkbox}
            />
            <span className={styles.checkboxText}>
              Hide price on receipt (price will still be included in total)
            </span>
          </label>
        </div>

        {formData.originalPrice && formData.originalPrice > formData.price && (
          <div className={styles.discountInfo}>
            <div className={styles.discountLabel}>Discount Information:</div>
            <div className={styles.discountDetails}>
              <span className={styles.discountAmount}>
                Save ${(formData.originalPrice - formData.price).toFixed(2)}
              </span>
              <span className={styles.discountPercent}>
                ({Math.round(((formData.originalPrice - formData.price) / formData.originalPrice) * 100)}% OFF)
              </span>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};
