import React, { useState } from 'react';
import { PrintButton } from '../ui/PrintButton';
import { SavePDFButton } from '../ui/SavePDFButton';
import { useReceipt } from '../../hooks/useReceipt';
import styles from './ActionButtons.module.css';

interface ActionButtonsProps {
  receiptElementId: string;
  className?: string;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({ 
  receiptElementId,
  className = '' 
}) => {
  const { receiptData, isReceiptValid } = useReceipt();
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 3000);
  };

  const handlePrintSuccess = () => {
    showNotification('success', 'Receipt sent to printer successfully');
  };

  const handlePDFSuccess = () => {
    showNotification('success', 'PDF file saved successfully');
  };

  const handlePDFError = (error: Error) => {
    showNotification('error', `PDF generation failed: ${error.message}`);
  };

  const generateFilename = () => {
    const date = new Date().toISOString().split('T')[0];
    const receiptNum = receiptData.receiptNumber.replace(/[^a-zA-Z0-9]/g, '');
    return `receipt-${receiptNum}-${date}.pdf`;
  };

  return (
    <div className={`${styles.container} ${className}`}>
      {/* 通知訊息 */}
      {notification && (
        <div className={`${styles.notification} ${styles[notification.type]}`}>
          <span className={styles.notificationIcon}>
            {notification.type === 'success' ? '✅' : '❌'}
          </span>
          <span className={styles.notificationMessage}>
            {notification.message}
          </span>
        </div>
      )}

      {/* 操作按鈕區域 */}
      <div className={styles.buttonsSection}>
        <div className={styles.buttonsHeader}>
          <h3 className={styles.sectionTitle}>Receipt Actions</h3>
          <p className={styles.sectionDescription}>
            After completing the receipt information, you can print or save as PDF file
          </p>
        </div>

        <div className={styles.buttonsGrid}>
          {/* 列印按鈕 */}
          <div className={styles.buttonGroup}>
            <PrintButton
              targetElementId={receiptElementId}
              onAfterPrint={handlePrintSuccess}
              disabled={!isReceiptValid}
              className={styles.actionButton}
              size="large"
            >
              <span className={styles.buttonIcon}>🖨️</span>
              <span className={styles.buttonText}>Print Receipt</span>
            </PrintButton>
            <p className={styles.buttonDescription}>
              Print receipt directly, suitable for Letter paper size
            </p>
          </div>

          {/* PDF 儲存按鈕 */}
          <div className={styles.buttonGroup}>
            <SavePDFButton
              targetElementId={receiptElementId}
              filename={generateFilename()}
              onAfterSave={handlePDFSuccess}
              onError={handlePDFError}
              disabled={!isReceiptValid}
              className={styles.actionButton}
              size="large"
            >
              <span className={styles.buttonIcon}>💾</span>
              <span className={styles.buttonText}>Save PDF</span>
            </SavePDFButton>
            <p className={styles.buttonDescription}>
              Save receipt as PDF file for easy sharing and storage
            </p>
          </div>
        </div>

        {/* 狀態提示 */}
        {!isReceiptValid && (
          <div className={styles.validationWarning}>
            <div className={styles.warningIcon}>⚠️</div>
            <div className={styles.warningContent}>
              <h4 className={styles.warningTitle}>Receipt Information Incomplete</h4>
              <p className={styles.warningMessage}>
                Please add at least one receipt item before printing or saving.
              </p>
            </div>
          </div>
        )}

        {/* 操作提示 */}
        <div className={styles.tips}>
          <h4 className={styles.tipsTitle}>💡 Operation Tips</h4>
          <ul className={styles.tipsList}>
            <li>Receipt will automatically use Letter paper size format</li>
            <li>Please confirm printer settings before printing</li>
            <li>PDF files will be named with receipt number and date</li>
            <li>It is recommended to preview receipt content before printing</li>
          </ul>
        </div>
      </div>
    </div>
  );
};