import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ItemForm } from './ItemForm';
import { AppProvider } from '../../context/AppContext';
import { ReceiptItem } from '../../types';

const mockEditingItem: ReceiptItem = {
  id: '1',
  name: 'Test CPU',
  category: 'hardware',
  price: 10000,
  quantity: 1,
};

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <AppProvider>
      {component}
    </AppProvider>
  );
};

describe('ItemForm', () => {
  it('應該正確渲染新增表單', () => {
    renderWithProvider(<ItemForm />);
    
    expect(screen.getByText('新增項目')).toBeInTheDocument();
    expect(screen.getByLabelText('項目名稱')).toBeInTheDocument();
    expect(screen.getByLabelText('類別')).toBeInTheDocument();
    expect(screen.getByLabelText('價格')).toBeInTheDocument();
    expect(screen.getByLabelText('數量')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '新增' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument();
  });

  it('應該正確渲染編輯表單', () => {
    renderWithProvider(<ItemForm editingItem={mockEditingItem} />);
    
    expect(screen.getByText('編輯項目')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test CPU')).toBeInTheDocument();
    expect(screen.getByDisplayValue('10000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('1')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '更新' })).toBeInTheDocument();
  });

  it('應該正確處理表單輸入', () => {
    renderWithProvider(<ItemForm />);
    
    const nameInput = screen.getByLabelText('項目名稱');
    const priceInput = screen.getByLabelText('價格');
    const quantityInput = screen.getByLabelText('數量');
    
    fireEvent.change(nameInput, { target: { value: 'New Item' } });
    fireEvent.change(priceInput, { target: { value: '500' } });
    fireEvent.change(quantityInput, { target: { value: '2' } });
    
    expect(nameInput).toHaveValue('New Item');
    expect(priceInput).toHaveValue(500);
    expect(quantityInput).toHaveValue(2);
  });

  it('應該正確處理類別選擇', () => {
    renderWithProvider(<ItemForm />);
    
    const categorySelect = screen.getByLabelText('類別');
    
    fireEvent.change(categorySelect, { target: { value: 'service' } });
    expect(categorySelect).toHaveValue('service');
  });

  it('應該能夠提交有效的表單', () => {
    renderWithProvider(<ItemForm />);
    
    const nameInput = screen.getByLabelText('項目名稱');
    const priceInput = screen.getByLabelText('價格');
    const quantityInput = screen.getByLabelText('數量');
    const submitButton = screen.getByRole('button', { name: '新增' });
    
    fireEvent.change(nameInput, { target: { value: 'Test Item' } });
    fireEvent.change(priceInput, { target: { value: '100' } });
    fireEvent.change(quantityInput, { target: { value: '2' } });
    
    // 確保表單可以提交而不會出錯
    expect(() => fireEvent.click(submitButton)).not.toThrow();
  });

  it('應該正確處理取消操作', () => {
    const mockOnCancel = vi.fn();
    renderWithProvider(<ItemForm onCancel={mockOnCancel} />);
    
    const cancelButton = screen.getByRole('button', { name: '取消' });
    fireEvent.click(cancelButton);
    
    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });

  it('應該能夠重置表單', () => {
    renderWithProvider(<ItemForm />);
    
    const nameInput = screen.getByLabelText('項目名稱');
    const priceInput = screen.getByLabelText('價格');
    const cancelButton = screen.getByRole('button', { name: '取消' });
    
    fireEvent.change(nameInput, { target: { value: 'Test Item' } });
    fireEvent.change(priceInput, { target: { value: '100' } });
    fireEvent.click(cancelButton);
    
    // 表單應該被重置
    expect(nameInput).toHaveValue('');
    expect(priceInput).toHaveValue(null);
  });

  it('應該在編輯模式下預填表單資料', () => {
    renderWithProvider(<ItemForm editingItem={mockEditingItem} />);
    
    expect(screen.getByDisplayValue('Test CPU')).toBeInTheDocument();
    expect(screen.getByDisplayValue('10000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('1')).toBeInTheDocument();
    
    // 檢查 select 的值
    const categorySelect = screen.getByLabelText('類別') as HTMLSelectElement;
    expect(categorySelect.value).toBe('hardware');
  });

  it('應該顯示正確的類別選項', () => {
    renderWithProvider(<ItemForm />);
    
    expect(screen.getByRole('option', { name: '硬體' })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: '服務' })).toBeInTheDocument();
  });
});