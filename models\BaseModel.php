<?php
/**
 * Base Model Class
 * 基礎模型類
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

// 防止直接訪問
if (!defined('KMS_RECEIPT_MAKER')) {
    die('Direct access not permitted');
}

/**
 * 基礎模型類
 * Base Model Class
 */
abstract class BaseModel {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $timestamps = true;

    /**
     * 構造函數
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * 查找所有記錄
     * Find all records
     * 
     * @param array $conditions 查詢條件
     * @param string $orderBy 排序
     * @param int $limit 限制數量
     * @param int $offset 偏移量
     * @return array
     */
    public function findAll($conditions = [], $orderBy = null, $limit = null, $offset = 0) {
        $sql = "SELECT * FROM `{$this->table}`";
        $params = [];

        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }

        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }

        if ($limit) {
            $sql .= " LIMIT {$limit}";
            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }
        }

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * 根據ID查找記錄
     * Find record by ID
     * 
     * @param int $id ID
     * @return array|false
     */
    public function findById($id) {
        $sql = "SELECT * FROM `{$this->table}` WHERE `{$this->primaryKey}` = :id";
        return $this->db->fetchRow($sql, ['id' => $id]);
    }

    /**
     * 根據條件查找單條記錄
     * Find single record by conditions
     * 
     * @param array $conditions 查詢條件
     * @return array|false
     */
    public function findOne($conditions) {
        $params = [];
        $whereClause = $this->buildWhereClause($conditions, $params);
        $sql = "SELECT * FROM `{$this->table}` WHERE {$whereClause} LIMIT 1";
        return $this->db->fetchRow($sql, $params);
    }

    /**
     * 創建新記錄
     * Create new record
     * 
     * @param array $data 數據
     * @return string 新記錄的ID
     */
    public function create($data) {
        $data = $this->filterFillable($data);
        
        if ($this->timestamps) {
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
        }

        $fields = array_keys($data);
        $placeholders = array_map(function($field) {
            return ":{$field}";
        }, $fields);

        $sql = "INSERT INTO `{$this->table}` (`" . implode('`, `', $fields) . "`) VALUES (" . implode(', ', $placeholders) . ")";
        
        return $this->db->insert($sql, $data);
    }

    /**
     * 更新記錄
     * Update record
     * 
     * @param int $id ID
     * @param array $data 數據
     * @return int 影響的行數
     */
    public function update($id, $data) {
        $data = $this->filterFillable($data);
        
        if ($this->timestamps) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }

        $setClause = [];
        foreach (array_keys($data) as $field) {
            $setClause[] = "`{$field}` = :{$field}";
        }

        $data['id'] = $id;
        $sql = "UPDATE `{$this->table}` SET " . implode(', ', $setClause) . " WHERE `{$this->primaryKey}` = :id";
        
        return $this->db->update($sql, $data);
    }

    /**
     * 刪除記錄
     * Delete record
     * 
     * @param int $id ID
     * @return int 影響的行數
     */
    public function delete($id) {
        $sql = "DELETE FROM `{$this->table}` WHERE `{$this->primaryKey}` = :id";
        return $this->db->delete($sql, ['id' => $id]);
    }

    /**
     * 軟刪除記錄（如果支持）
     * Soft delete record (if supported)
     * 
     * @param int $id ID
     * @return int 影響的行數
     */
    public function softDelete($id) {
        if (in_array('is_active', $this->fillable)) {
            return $this->update($id, ['is_active' => 0]);
        }
        return $this->delete($id);
    }

    /**
     * 計算記錄數量
     * Count records
     * 
     * @param array $conditions 查詢條件
     * @return int
     */
    public function count($conditions = []) {
        $sql = "SELECT COUNT(*) as count FROM `{$this->table}`";
        $params = [];

        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }

        $result = $this->db->fetchRow($sql, $params);
        return (int)$result['count'];
    }

    /**
     * 檢查記錄是否存在
     * Check if record exists
     * 
     * @param array $conditions 查詢條件
     * @return bool
     */
    public function exists($conditions) {
        return $this->count($conditions) > 0;
    }

    /**
     * 分頁查詢
     * Paginated query
     * 
     * @param int $page 頁碼
     * @param int $perPage 每頁數量
     * @param array $conditions 查詢條件
     * @param string $orderBy 排序
     * @return array
     */
    public function paginate($page = 1, $perPage = 10, $conditions = [], $orderBy = null) {
        $offset = ($page - 1) * $perPage;
        $total = $this->count($conditions);
        $data = $this->findAll($conditions, $orderBy, $perPage, $offset);

        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
    }

    /**
     * 構建WHERE子句
     * Build WHERE clause
     * 
     * @param array $conditions 條件
     * @param array &$params 參數引用
     * @return string
     */
    protected function buildWhereClause($conditions, &$params) {
        $clauses = [];
        $paramIndex = 0;

        foreach ($conditions as $field => $value) {
            $paramName = "param_{$paramIndex}";
            
            if (is_array($value)) {
                // IN 查詢
                $placeholders = [];
                foreach ($value as $i => $v) {
                    $placeholder = "{$paramName}_{$i}";
                    $placeholders[] = ":{$placeholder}";
                    $params[$placeholder] = $v;
                }
                $clauses[] = "`{$field}` IN (" . implode(', ', $placeholders) . ")";
            } else {
                $clauses[] = "`{$field}` = :{$paramName}";
                $params[$paramName] = $value;
            }
            
            $paramIndex++;
        }

        return implode(' AND ', $clauses);
    }

    /**
     * 過濾可填充字段
     * Filter fillable fields
     * 
     * @param array $data 數據
     * @return array
     */
    protected function filterFillable($data) {
        if (empty($this->fillable)) {
            return $data;
        }

        return array_intersect_key($data, array_flip($this->fillable));
    }

    /**
     * 開始事務
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->db->beginTransaction();
    }

    /**
     * 提交事務
     * Commit transaction
     */
    public function commit() {
        return $this->db->commit();
    }

    /**
     * 回滾事務
     * Rollback transaction
     */
    public function rollback() {
        return $this->db->rollback();
    }

    /**
     * 執行原始SQL查詢
     * Execute raw SQL query
     * 
     * @param string $sql SQL語句
     * @param array $params 參數
     * @return PDOStatement
     */
    public function query($sql, $params = []) {
        return $this->db->query($sql, $params);
    }

    /**
     * 獲取表名
     * Get table name
     * 
     * @return string
     */
    public function getTable() {
        return $this->table;
    }

    /**
     * 獲取主鍵字段名
     * Get primary key field name
     * 
     * @return string
     */
    public function getPrimaryKey() {
        return $this->primaryKey;
    }
}
?>
