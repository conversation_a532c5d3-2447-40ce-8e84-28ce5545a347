.container {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header h4 {
  margin: 0;
  color: #000000;
}

.toggleButton {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.toggleButton:hover {
  background: #5a6268;
}

.categoryFilter {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.categoryButton {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.categoryButton:hover {
  background: #e9ecef;
}

.categoryButton.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.itemsList {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.itemRow {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  background: white;
  transition: all 0.2s;
  cursor: move;
}

.itemRow:last-child {
  border-bottom: none;
}

.itemRow:hover {
  background: #f8f9fa;
}

.itemRow.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

.dragHandle {
  color: #999;
  margin-right: 0.5rem;
  cursor: grab;
  user-select: none;
}

.dragHandle:active {
  cursor: grabbing;
}

.itemInfo {
  flex: 1;
  margin-right: 1rem;
}

.itemHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.itemName {
  font-weight: 500;
  color: #000000;
}

.itemCategory {
  font-size: 0.8rem;
  color: #666;
  background: #f8f9fa;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
}

.itemDescription {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.3;
}

.priceInfo {
  text-align: right;
  margin-right: 1rem;
  min-width: 100px;
}

.currentPrice {
  font-size: 1.1rem;
  font-weight: bold;
  color: #28a745;
}

.originalPrice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.strikethrough {
  text-decoration: line-through;
  color: #999;
  font-size: 0.9rem;
}

.discount {
  background: #dc3545;
  color: white;
  padding: 0.1rem 0.4rem;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: bold;
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.editButton,
.deleteButton,
.addButton {
  padding: 0.4rem 0.8rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s;
}

.editButton {
  background: #ffc107;
  color: #212529;
}

.editButton:hover {
  background: #e0a800;
}

.deleteButton {
  background: #dc3545;
  color: white;
}

.deleteButton:hover {
  background: #c82333;
}

.addButton {
  background: #28a745;
  color: white;
}

.addButton:hover {
  background: #218838;
}

/* 彈窗樣式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalContent {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.modalHeader h3 {
  margin: 0;
  color: #000000;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  color: #000000;
}

.modalBody {
  padding: 1rem;
}

.formGroup {
  margin-bottom: 1rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #000000;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.formGroup textarea {
  min-height: 80px;
  resize: vertical;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.discountInfo {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  border-left: 4px solid #28a745;
}

.discountInfo strong {
  display: block;
  margin-bottom: 0.5rem;
  color: #000000;
}

.discountInfo div {
  color: #28a745;
  font-weight: 500;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #e0e0e0;
}

.cancelButton,
.saveButton {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.cancelButton {
  background: #6c757d;
  color: white;
}

.cancelButton:hover {
  background: #5a6268;
}

.saveButton {
  background: #007bff;
  color: white;
}

.saveButton:hover {
  background: #0056b3;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .itemRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .itemInfo,
  .priceInfo {
    margin-right: 0;
    width: 100%;
  }
  
  .priceInfo {
    text-align: left;
  }
  
  .actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .formRow {
    grid-template-columns: 1fr;
  }
  
  .modalContent {
    width: 95%;
    margin: 1rem;
  }
}
