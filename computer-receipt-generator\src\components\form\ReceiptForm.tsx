import React from 'react';
import { StoreInfoSection } from '../info/StoreInfoSection';
import { CustomerInfoSection } from '../info/CustomerInfoSection';
import { ItemList } from '../items/ItemList';
import { LogoManager } from '../logo/LogoManager';
import { ReceiptTemplateManager } from '../templates/ReceiptTemplateManager';
import { Input } from '../ui';
import { useReceipt } from '../../hooks/useReceipt';
import { useValidation } from '../../hooks/useValidation';
import { formatDate } from '../../utils/calculations';
import styles from './ReceiptForm.module.css';

export const ReceiptForm: React.FC = () => {
  const { receiptData, setTaxRate, setReceiptDate, setReceiptNumber, updateLogoSettings } = useReceipt();
  const { validateTaxRateField, getFieldError } = useValidation();

  const handleTaxRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    setTaxRate(value);
    validateTaxRateField('taxRate', value);
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const dateValue = e.target.value;
    if (dateValue) {
      const newDate = new Date(dateValue);
      setReceiptDate(newDate);
    } else {
      setReceiptDate(null);
    }
  };

  const handleReceiptNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setReceiptNumber(value);
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>Receipt Generator</h1>
        <p className={styles.subtitle}>
          Fill in the following information to generate professional computer sales receipts
        </p>
      </div>

      <div className={styles.formSections}>
        {/* 商店資訊區塊 */}
        <section className={styles.section}>
          <StoreInfoSection />
        </section>

        {/* 客戶資訊區塊 */}
        <section className={styles.section}>
          <CustomerInfoSection />
        </section>

        {/* 收據模板管理區塊 */}
        <section className={styles.section}>
          <ReceiptTemplateManager />
        </section>

        {/* 項目管理區塊 */}
        <section className={styles.section}>
          <ItemList />
        </section>

        {/* 稅率設定區塊 */}
        <section className={styles.section}>
          <div className={styles.taxSection}>
            <div className={styles.taxHeader}>
              <h2>Tax Rate Settings</h2>
              <p className={styles.description}>
                Set the applicable tax rate percentage, which will be used to calculate the receipt total
              </p>
            </div>
            
            <div className={styles.taxInput}>
              <Input
                label="Tax Rate (%)"
                type="number"
                value={receiptData.taxRate.toString()}
                onChange={handleTaxRateChange}
                error={getFieldError('taxRate')}
                placeholder="e.g.: 5"
                min="0"
                max="100"
                step="0.1"
                helperText="Enter tax rate percentage, e.g. 5 means 5%"
              />
            </div>

            <div className={styles.taxPreview}>
              <div className={styles.previewLabel}>Current Tax Rate:</div>
              <div className={styles.previewValue}>{receiptData.taxRate}%</div>
            </div>
          </div>
        </section>

        {/* 日期設定區塊 */}
        <section className={styles.section}>
          <div className={styles.taxSection}>
            <div className={styles.taxHeader}>
              <h2>Receipt Date Settings</h2>
              <p className={styles.description}>
                Set the date for this receipt. This will be displayed on the receipt.
              </p>
            </div>

            <div className={styles.taxInput}>
              <Input
                label="Receipt Date"
                type="date"
                value={receiptData.date ? formatDate(receiptData.date) : ''}
                onChange={handleDateChange}
                fullWidth
                helperText="Select the date for this receipt"
              />
            </div>

            <div className={styles.taxPreview}>
              <div className={styles.previewLabel}>Current Date:</div>
              <div className={styles.previewValue}>
                {receiptData.date ? formatDate(receiptData.date) : 'No date selected'}
              </div>
            </div>
          </div>
        </section>

        {/* 收據編號設定區塊 */}
        <section className={styles.section}>
          <div className={styles.taxSection}>
            <div className={styles.taxHeader}>
              <h2>Receipt Number Settings</h2>
              <p className={styles.description}>
                Customize the receipt number. This will be displayed on the receipt.
              </p>
            </div>

            <div className={styles.taxInput}>
              <Input
                label="Receipt Number"
                type="text"
                value={receiptData.receiptNumber}
                onChange={handleReceiptNumberChange}
                fullWidth
                placeholder="e.g., RCP-2025-001"
                helperText="Enter a custom receipt number"
              />
            </div>

            <div className={styles.taxPreview}>
              <div className={styles.previewLabel}>Current Receipt Number:</div>
              <div className={styles.previewValue}>{receiptData.receiptNumber}</div>
            </div>
          </div>
        </section>

        {/* Logo 設定區塊 */}
        <section className={styles.section}>
          <LogoManager
            logoSettings={receiptData.logoSettings}
            onLogoChange={updateLogoSettings}
          />
        </section>
      </div>
    </div>
  );
};