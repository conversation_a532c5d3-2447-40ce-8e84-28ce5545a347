import { useCallback, useMemo } from 'react';
import { useAppContext } from '../context/AppContext';
import type { ReceiptItem, StoreInfo, CustomerInfo, LogoSettings } from '../types';
import { calculateReceiptTotals, generateReceiptNumber } from '../utils/calculations';

/**
 * 收據管理的自訂 Hook
 */
export const useReceipt = () => {
  const { state, dispatch } = useAppContext();

  // 計算收據總額
  const totals = useMemo(() => {
    return calculateReceiptTotals(state.receiptData.items, state.receiptData.taxRate);
  }, [state.receiptData.items, state.receiptData.taxRate]);

  // 新增項目
  const addItem = useCallback((item: Omit<ReceiptItem, 'id'>) => {
    const newItem: ReceiptItem = {
      ...item,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    };
    dispatch({ type: 'ADD_ITEM', payload: newItem });
  }, [dispatch]);

  // 更新項目
  const updateItem = useCallback((id: string, updates: Partial<ReceiptItem>) => {
    dispatch({ type: 'UPDATE_ITEM', payload: { id, item: updates } });
  }, [dispatch]);

  // 刪除項目
  const deleteItem = useCallback((id: string) => {
    dispatch({ type: 'DELETE_ITEM', payload: id });
  }, [dispatch]);

  // 向上移動項目
  const moveItemUp = useCallback((id: string) => {
    dispatch({ type: 'MOVE_ITEM_UP', payload: id });
  }, [dispatch]);

  // 向下移動項目
  const moveItemDown = useCallback((id: string) => {
    dispatch({ type: 'MOVE_ITEM_DOWN', payload: id });
  }, [dispatch]);

  // 更新商店資訊
  const updateStoreInfo = useCallback((updates: Partial<StoreInfo>) => {
    dispatch({ type: 'UPDATE_STORE_INFO', payload: updates });
  }, [dispatch]);

  // 更新客戶資訊
  const updateCustomerInfo = useCallback((updates: Partial<CustomerInfo>) => {
    dispatch({ type: 'UPDATE_CUSTOMER_INFO', payload: updates });
  }, [dispatch]);

  // 設定稅率
  const setTaxRate = useCallback((taxRate: number) => {
    dispatch({ type: 'SET_TAX_RATE', payload: taxRate });
  }, [dispatch]);

  // 設定收據日期
  const setReceiptDate = useCallback((date: Date | null) => {
    dispatch({ type: 'SET_RECEIPT_DATE', payload: date });
  }, [dispatch]);

  // 設定收據編號
  const setReceiptNumber = useCallback((receiptNumber: string) => {
    dispatch({ type: 'SET_RECEIPT_NUMBER', payload: receiptNumber });
  }, [dispatch]);

  // 更新 Logo 設定
  const updateLogoSettings = useCallback((logoSettings: LogoSettings) => {
    dispatch({ type: 'UPDATE_LOGO_SETTINGS', payload: logoSettings });
  }, [dispatch]);

  // 開始編輯項目
  const startEditingItem = useCallback((itemId: string) => {
    dispatch({ type: 'SET_EDITING', payload: { isEditing: true, itemId } });
  }, [dispatch]);

  // 停止編輯
  const stopEditing = useCallback(() => {
    dispatch({ type: 'SET_EDITING', payload: { isEditing: false } });
  }, [dispatch]);

  // 生成新的收據編號
  const generateNewReceiptNumber = useCallback(() => {
    generateReceiptNumber();
    dispatch({ 
      type: 'UPDATE_STORE_INFO', 
      payload: {} 
    }); // 觸發重新渲染
    // 注意：這裡我們需要在 reducer 中處理收據編號的更新
  }, [dispatch]);

  // 清除所有項目（不清除其他設定）
  const clearItems = useCallback(() => {
    state.receiptData.items.forEach(item => {
      dispatch({ type: 'DELETE_ITEM', payload: item.id });
    });

    // 停止編輯
    dispatch({ type: 'SET_EDITING', payload: { isEditing: false } });
  }, [state.receiptData.items, dispatch]);

  // 清空收據
  const clearReceipt = useCallback(() => {
    // 刪除所有項目
    clearItems();

    // 重置商店和客戶資訊
    dispatch({ type: 'UPDATE_STORE_INFO', payload: { name: '', address: '', phone: '', email: '' } });
    dispatch({ type: 'UPDATE_CUSTOMER_INFO', payload: { name: '', phone: '', email: '', address: '' } });

    // 重置稅率
    dispatch({ type: 'SET_TAX_RATE', payload: 0 });

    // 重置日期
    dispatch({ type: 'SET_RECEIPT_DATE', payload: null });

    // 重置 Logo 設定
    dispatch({ type: 'UPDATE_LOGO_SETTINGS', payload: {
      imageUrl: null,
      position: { x: 10, y: 10 },
      size: { width: 20, height: 15 },
      opacity: 1,
    }});
  }, [clearItems, dispatch]);

  // 取得正在編輯的項目
  const editingItem = useMemo(() => {
    if (!state.editingItemId) return null;
    return state.receiptData.items.find(item => item.id === state.editingItemId) || null;
  }, [state.editingItemId, state.receiptData.items]);

  // 檢查收據是否有效（只需要有項目即可）
  const isReceiptValid = useMemo(() => {
    const hasItems = state.receiptData.items.length > 0;

    return hasItems;
  }, [state.receiptData.items]);

  return {
    // 狀態
    receiptData: state.receiptData,
    isEditing: state.isEditing,
    editingItemId: state.editingItemId,
    editingItem,
    totals,
    isReceiptValid,
    
    // 動作
    addItem,
    updateItem,
    deleteItem,
    moveItemUp,
    moveItemDown,
    updateStoreInfo,
    updateCustomerInfo,
    setTaxRate,
    setReceiptDate,
    setReceiptNumber,
    updateLogoSettings,
    startEditingItem,
    stopEditing,
    generateNewReceiptNumber,
    clearItems,
    clearReceipt,
  };
};