/**
 * Main Styles
 * 主樣式文件
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* CSS Custom Properties (Variables) */
:root {
    /* Colors - Light Theme */
    --kms-color-primary: #2563eb;
    --kms-color-primary-hover: #1d4ed8;
    --kms-color-primary-light: #dbeafe;
    --kms-color-secondary: #64748b;
    --kms-color-secondary-hover: #475569;
    --kms-color-success: #059669;
    --kms-color-warning: #d97706;
    --kms-color-error: #dc2626;
    --kms-color-info: #0891b2;
    
    /* Neutral Colors */
    --kms-color-white: #ffffff;
    --kms-color-gray-50: #f8fafc;
    --kms-color-gray-100: #f1f5f9;
    --kms-color-gray-200: #e2e8f0;
    --kms-color-gray-300: #cbd5e1;
    --kms-color-gray-400: #94a3b8;
    --kms-color-gray-500: #64748b;
    --kms-color-gray-600: #475569;
    --kms-color-gray-700: #334155;
    --kms-color-gray-800: #1e293b;
    --kms-color-gray-900: #0f172a;
    
    /* Background Colors */
    --kms-bg-primary: var(--kms-color-white);
    --kms-bg-secondary: var(--kms-color-gray-50);
    --kms-bg-tertiary: var(--kms-color-gray-100);
    
    /* Text Colors */
    --kms-text-primary: var(--kms-color-gray-900);
    --kms-text-secondary: var(--kms-color-gray-600);
    --kms-text-muted: var(--kms-color-gray-500);
    
    /* Border Colors */
    --kms-border-color: var(--kms-color-gray-200);
    --kms-border-color-hover: var(--kms-color-gray-300);
    
    /* Typography */
    --kms-font-family-sans: 'Segoe UI', 'Microsoft JhengHei', 'PingFang TC', 'Helvetica Neue', Arial, sans-serif;
    --kms-font-family-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
    
    --kms-font-size-xs: 0.75rem;    /* 12px */
    --kms-font-size-sm: 0.875rem;   /* 14px */
    --kms-font-size-base: 1rem;     /* 16px */
    --kms-font-size-lg: 1.125rem;   /* 18px */
    --kms-font-size-xl: 1.25rem;    /* 20px */
    --kms-font-size-2xl: 1.5rem;    /* 24px */
    --kms-font-size-3xl: 1.875rem;  /* 30px */
    
    --kms-font-weight-normal: 400;
    --kms-font-weight-medium: 500;
    --kms-font-weight-semibold: 600;
    --kms-font-weight-bold: 700;
    
    --kms-line-height-tight: 1.25;
    --kms-line-height-normal: 1.5;
    --kms-line-height-relaxed: 1.75;
    
    /* Spacing */
    --kms-spacing-1: 0.25rem;   /* 4px */
    --kms-spacing-2: 0.5rem;    /* 8px */
    --kms-spacing-3: 0.75rem;   /* 12px */
    --kms-spacing-4: 1rem;      /* 16px */
    --kms-spacing-5: 1.25rem;   /* 20px */
    --kms-spacing-6: 1.5rem;    /* 24px */
    --kms-spacing-8: 2rem;      /* 32px */
    --kms-spacing-10: 2.5rem;   /* 40px */
    --kms-spacing-12: 3rem;     /* 48px */
    --kms-spacing-16: 4rem;     /* 64px */
    
    /* Border Radius */
    --kms-radius-sm: 0.25rem;   /* 4px */
    --kms-radius-md: 0.375rem;  /* 6px */
    --kms-radius-lg: 0.5rem;    /* 8px */
    --kms-radius-xl: 0.75rem;   /* 12px */
    
    /* Shadows */
    --kms-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --kms-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --kms-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --kms-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --kms-transition-fast: 150ms ease-in-out;
    --kms-transition-normal: 250ms ease-in-out;
    --kms-transition-slow: 350ms ease-in-out;
    
    /* Z-index */
    --kms-z-dropdown: 1000;
    --kms-z-sticky: 1020;
    --kms-z-fixed: 1030;
    --kms-z-modal-backdrop: 1040;
    --kms-z-modal: 1050;
    --kms-z-popover: 1060;
    --kms-z-tooltip: 1070;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --kms-color-primary: #3b82f6;
    --kms-color-primary-hover: #2563eb;
    --kms-color-primary-light: #1e3a8a;
    
    --kms-bg-primary: var(--kms-color-gray-900);
    --kms-bg-secondary: var(--kms-color-gray-800);
    --kms-bg-tertiary: var(--kms-color-gray-700);
    
    --kms-text-primary: var(--kms-color-gray-100);
    --kms-text-secondary: var(--kms-color-gray-300);
    --kms-text-muted: var(--kms-color-gray-400);
    
    --kms-border-color: var(--kms-color-gray-700);
    --kms-border-color-hover: var(--kms-color-gray-600);
}

/* Base HTML Elements */
html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--kms-font-family-sans);
    font-size: var(--kms-font-size-base);
    font-weight: var(--kms-font-weight-normal);
    line-height: var(--kms-line-height-normal);
    color: var(--kms-text-primary);
    background-color: var(--kms-bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Skip Link for Accessibility */
.kms-skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--kms-color-primary);
    color: var(--kms-color-white);
    padding: var(--kms-spacing-2) var(--kms-spacing-4);
    text-decoration: none;
    border-radius: var(--kms-radius-md);
    z-index: var(--kms-z-tooltip);
    transition: top var(--kms-transition-fast);
}

.kms-skip-link:focus {
    top: 6px;
}

/* Layout Components */
.kms-layout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--kms-spacing-4);
}

.kms-layout-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--kms-spacing-8);
    margin-top: var(--kms-spacing-6);
}

/* Header Styles */
.kms-header {
    background-color: var(--kms-bg-primary);
    border-bottom: 1px solid var(--kms-border-color);
    padding: var(--kms-spacing-4) 0;
    position: sticky;
    top: 0;
    z-index: var(--kms-z-sticky);
}

.kms-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.kms-header-brand {
    display: flex;
    align-items: center;
    gap: var(--kms-spacing-3);
}

.kms-logo {
    border-radius: var(--kms-radius-md);
}

.kms-title {
    display: flex;
    flex-direction: column;
    gap: var(--kms-spacing-1);
}

.kms-title-main {
    font-size: var(--kms-font-size-xl);
    font-weight: var(--kms-font-weight-bold);
    color: var(--kms-text-primary);
}

.kms-title-sub {
    font-size: var(--kms-font-size-sm);
    font-weight: var(--kms-font-weight-normal);
    color: var(--kms-text-secondary);
}

.kms-header-controls {
    display: flex;
    align-items: center;
    gap: var(--kms-spacing-3);
}

/* Main Content */
.kms-main {
    min-height: calc(100vh - 120px);
    padding: var(--kms-spacing-6) 0;
}

/* Section Styles */
.kms-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--kms-spacing-6);
    padding-bottom: var(--kms-spacing-4);
    border-bottom: 2px solid var(--kms-border-color);
}

.kms-section-title {
    font-size: var(--kms-font-size-2xl);
    font-weight: var(--kms-font-weight-bold);
    color: var(--kms-text-primary);
}

.kms-section-actions {
    display: flex;
    gap: var(--kms-spacing-2);
}

.kms-form-section,
.kms-preview-section {
    background-color: var(--kms-bg-primary);
    border-radius: var(--kms-radius-lg);
    padding: var(--kms-spacing-6);
    box-shadow: var(--kms-shadow-sm);
    border: 1px solid var(--kms-border-color);
}

/* Footer */
.kms-footer {
    background-color: var(--kms-bg-primary);
    border-top: 1px solid var(--kms-border-color);
    padding: var(--kms-spacing-4) 0;
    margin-top: var(--kms-spacing-8);
}

.kms-footer-content {
    text-align: center;
}

.kms-footer-text {
    font-size: var(--kms-font-size-sm);
    color: var(--kms-text-secondary);
}

.kms-footer-separator {
    margin: 0 var(--kms-spacing-2);
    color: var(--kms-text-muted);
}

/* Loading Overlay */
.kms-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--kms-z-modal);
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--kms-transition-normal), visibility var(--kms-transition-normal);
}

.kms-loading-overlay.kms-loading-show {
    opacity: 1;
    visibility: visible;
}

.kms-loading-spinner {
    text-align: center;
}

.kms-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--kms-color-gray-200);
    border-top: 4px solid var(--kms-color-primary);
    border-radius: 50%;
    animation: kms-spin 1s linear infinite;
    margin: 0 auto var(--kms-spacing-4);
}

.kms-loading-text {
    font-size: var(--kms-font-size-sm);
    color: var(--kms-text-secondary);
}

@keyframes kms-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alert Container */
.kms-alert-container {
    margin-bottom: var(--kms-spacing-6);
}

/* Modal Container */
.kms-modal-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: var(--kms-z-modal);
    pointer-events: none;
}

/* Utility Classes */
.kms-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.kms-text-center { text-align: center; }
.kms-text-left { text-align: left; }
.kms-text-right { text-align: right; }

.kms-hidden { display: none !important; }
.kms-invisible { visibility: hidden; }

.kms-mb-0 { margin-bottom: 0; }
.kms-mb-2 { margin-bottom: var(--kms-spacing-2); }
.kms-mb-4 { margin-bottom: var(--kms-spacing-4); }
.kms-mb-6 { margin-bottom: var(--kms-spacing-6); }

/* Button Styles */
.kms-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--kms-spacing-2);
    padding: var(--kms-spacing-3) var(--kms-spacing-4);
    font-family: inherit;
    font-size: var(--kms-font-size-sm);
    font-weight: var(--kms-font-weight-medium);
    line-height: 1;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--kms-radius-md);
    cursor: pointer;
    transition: all var(--kms-transition-fast);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: nowrap;
}

.kms-btn:focus {
    outline: 2px solid var(--kms-color-primary);
    outline-offset: 2px;
}

.kms-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Variants */
.kms-btn-primary {
    background-color: var(--kms-color-primary);
    color: var(--kms-color-white);
    border-color: var(--kms-color-primary);
}

.kms-btn-primary:hover:not(:disabled) {
    background-color: var(--kms-color-primary-hover);
    border-color: var(--kms-color-primary-hover);
}

.kms-btn-secondary {
    background-color: var(--kms-bg-primary);
    color: var(--kms-text-primary);
    border-color: var(--kms-border-color);
}

.kms-btn-secondary:hover:not(:disabled) {
    background-color: var(--kms-bg-tertiary);
    border-color: var(--kms-border-color-hover);
}

.kms-btn-success {
    background-color: var(--kms-color-success);
    color: var(--kms-color-white);
    border-color: var(--kms-color-success);
}

.kms-btn-success:hover:not(:disabled) {
    background-color: #047857;
    border-color: #047857;
}

.kms-btn-warning {
    background-color: var(--kms-color-warning);
    color: var(--kms-color-white);
    border-color: var(--kms-color-warning);
}

.kms-btn-warning:hover:not(:disabled) {
    background-color: #b45309;
    border-color: #b45309;
}

.kms-btn-error {
    background-color: var(--kms-color-error);
    color: var(--kms-color-white);
    border-color: var(--kms-color-error);
}

.kms-btn-error:hover:not(:disabled) {
    background-color: #b91c1c;
    border-color: #b91c1c;
}

/* Button Sizes */
.kms-btn-sm {
    padding: var(--kms-spacing-2) var(--kms-spacing-3);
    font-size: var(--kms-font-size-xs);
}

.kms-btn-lg {
    padding: var(--kms-spacing-4) var(--kms-spacing-6);
    font-size: var(--kms-font-size-base);
}

/* Button Groups */
.kms-btn-group {
    display: inline-flex;
    border-radius: var(--kms-radius-md);
    overflow: hidden;
}

.kms-btn-group .kms-btn {
    border-radius: 0;
    border-right-width: 0;
}

.kms-btn-group .kms-btn:first-child {
    border-top-left-radius: var(--kms-radius-md);
    border-bottom-left-radius: var(--kms-radius-md);
}

.kms-btn-group .kms-btn:last-child {
    border-top-right-radius: var(--kms-radius-md);
    border-bottom-right-radius: var(--kms-radius-md);
    border-right-width: 1px;
}

/* Input Styles */
.kms-input {
    display: block;
    width: 100%;
    padding: var(--kms-spacing-3) var(--kms-spacing-4);
    font-family: inherit;
    font-size: var(--kms-font-size-sm);
    line-height: var(--kms-line-height-normal);
    color: var(--kms-text-primary);
    background-color: var(--kms-bg-primary);
    border: 1px solid var(--kms-border-color);
    border-radius: var(--kms-radius-md);
    transition: border-color var(--kms-transition-fast), box-shadow var(--kms-transition-fast);
}

.kms-input:focus {
    outline: none;
    border-color: var(--kms-color-primary);
    box-shadow: 0 0 0 3px var(--kms-color-primary-light);
}

.kms-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: var(--kms-bg-tertiary);
}

.kms-input::placeholder {
    color: var(--kms-text-muted);
}

/* Select Styles */
.kms-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--kms-spacing-3) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: var(--kms-spacing-10);
}

/* Textarea Styles */
.kms-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Label Styles */
.kms-label {
    display: block;
    font-size: var(--kms-font-size-sm);
    font-weight: var(--kms-font-weight-medium);
    color: var(--kms-text-primary);
    margin-bottom: var(--kms-spacing-2);
}

/* Alert Styles */
.kms-alert {
    padding: var(--kms-spacing-4);
    border-radius: var(--kms-radius-md);
    border: 1px solid transparent;
    margin-bottom: var(--kms-spacing-4);
}

.kms-alert-success {
    background-color: #dcfce7;
    border-color: #bbf7d0;
    color: #166534;
}

.kms-alert-warning {
    background-color: #fef3c7;
    border-color: #fde68a;
    color: #92400e;
}

.kms-alert-error {
    background-color: #fee2e2;
    border-color: #fecaca;
    color: #991b1b;
}

.kms-alert-info {
    background-color: #e0f2fe;
    border-color: #b3e5fc;
    color: #0c4a6e;
}
