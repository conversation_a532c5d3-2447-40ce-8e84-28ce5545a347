import React from 'react';

/**
 * PrintStyles 組件
 * 提供全域的列印樣式，確保收據在列印時正確顯示
 */
export const PrintStyles: React.FC = () => {
  return (
    <style>{`
      /* 全域列印樣式重置 */
      @media print {
        /* 重置頁面邊距 */
        @page {
          size: letter;
          margin: 0.5in;
        }
        
        /* 隱藏不需要列印的元素 */
        .no-print,
        .noPrint,
        button,
        input[type="button"],
        input[type="submit"],
        input[type="reset"] {
          display: none !important;
        }
        
        /* 確保收據容器佔滿頁面 */
        body {
          margin: 0 !important;
          padding: 0 !important;
          background: white !important;
          color: black !important;
        }
        
        /* 移除所有陰影和邊框效果 */
        * {
          box-shadow: none !important;
          text-shadow: none !important;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        
        /* 確保表格正確列印 */
        table {
          border-collapse: collapse !important;
        }
        
        table, th, td {
          border: 1px solid #000 !important;
        }
        
        /* 防止內容被截斷 */
        .receipt-container {
          page-break-inside: avoid;
        }
        
        /* 確保字體在列印時清晰 */
        body, * {
          font-family: 'Arial', 'Microsoft JhengHei', sans-serif !important;
        }
      }
    `}</style>
  );
};

/**
 * 列印功能的 Hook
 */
export const usePrint = () => {
  const handlePrint = React.useCallback(() => {
    // 觸發瀏覽器列印對話框
    window.print();
  }, []);

  const handlePrintWithDelay = React.useCallback((delay: number = 100) => {
    // 延遲列印，確保樣式已載入
    setTimeout(() => {
      window.print();
    }, delay);
  }, []);

  return {
    handlePrint,
    handlePrintWithDelay,
  };
};

/**
 * 列印預覽模式的 Hook
 */
export const usePrintPreview = () => {
  const [isPrintPreview, setIsPrintPreview] = React.useState(false);

  const togglePrintPreview = React.useCallback(() => {
    setIsPrintPreview(prev => !prev);
  }, []);

  const enablePrintPreview = React.useCallback(() => {
    setIsPrintPreview(true);
  }, []);

  const disablePrintPreview = React.useCallback(() => {
    setIsPrintPreview(false);
  }, []);

  return {
    isPrintPreview,
    togglePrintPreview,
    enablePrintPreview,
    disablePrintPreview,
  };
};