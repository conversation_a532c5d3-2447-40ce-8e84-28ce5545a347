import React, { useRef } from 'react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { LogoDisplay } from './LogoDisplay';
import styles from './LogoManager.module.css';

export interface LogoSettings {
  imageUrl: string | null;
  position: {
    x: number; // percentage from left
    y: number; // percentage from top
  };
  size: {
    width: number; // percentage of container width
    height: number; // percentage of container height
  };
  opacity: number; // 0-1
}

interface LogoManagerProps {
  logoSettings: LogoSettings;
  onLogoChange: (settings: LogoSettings) => void;
}

export const LogoManager: React.FC<LogoManagerProps> = ({
  logoSettings,
  onLogoChange,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        onLogoChange({
          ...logoSettings,
          imageUrl,
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePositionChange = (axis: 'x' | 'y', value: number) => {
    onLogoChange({
      ...logoSettings,
      position: {
        ...logoSettings.position,
        [axis]: Math.max(0, Math.min(100, value)),
      },
    });
  };

  const handleSizeChange = (dimension: 'width' | 'height', value: number) => {
    onLogoChange({
      ...logoSettings,
      size: {
        ...logoSettings.size,
        [dimension]: Math.max(5, Math.min(100, value)),
      },
    });
  };

  const handleOpacityChange = (value: number) => {
    onLogoChange({
      ...logoSettings,
      opacity: Math.max(0, Math.min(1, value)),
    });
  };

  const handleRemoveLogo = () => {
    onLogoChange({
      ...logoSettings,
      imageUrl: null,
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileUpload = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h3 className={styles.title}>Logo Settings</h3>
        <p className={styles.description}>
          Upload and position a logo on your receipt
        </p>
      </div>

      <div className={styles.uploadSection}>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          className={styles.hiddenInput}
        />
        
        {!logoSettings.imageUrl ? (
          <div className={styles.uploadArea} onClick={triggerFileUpload}>
            <div className={styles.uploadIcon}>📁</div>
            <div className={styles.uploadText}>
              <strong>Click to upload logo</strong>
              <br />
              <small>Supports JPG, PNG, GIF</small>
            </div>
          </div>
        ) : (
          <div className={styles.logoPreview}>
            <img
              src={logoSettings.imageUrl}
              alt="Logo preview"
              className={styles.previewImage}
            />
            <div className={styles.logoActions}>
              <Button
                size="small"
                variant="outline"
                onClick={triggerFileUpload}
              >
                Change Logo
              </Button>
              <Button
                size="small"
                variant="danger"
                onClick={handleRemoveLogo}
              >
                Remove
              </Button>
            </div>
          </div>
        )}
      </div>

      {logoSettings.imageUrl && (
        <div className={styles.controlsSection}>
          <div className={styles.controlGroup}>
            <h4 className={styles.controlTitle}>Position</h4>
            <div className={styles.controlRow}>
              <div className={styles.inputGroup}>
                <label className={styles.label}>X Position (%)</label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={logoSettings.position.x.toString()}
                  onChange={(e) => handlePositionChange('x', parseFloat(e.target.value) || 0)}
                />
              </div>
              <div className={styles.inputGroup}>
                <label className={styles.label}>Y Position (%)</label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={logoSettings.position.y.toString()}
                  onChange={(e) => handlePositionChange('y', parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>
          </div>

          <div className={styles.controlGroup}>
            <h4 className={styles.controlTitle}>Size</h4>
            <div className={styles.controlRow}>
              <div className={styles.inputGroup}>
                <label className={styles.label}>Width (%)</label>
                <Input
                  type="number"
                  min="5"
                  max="100"
                  value={logoSettings.size.width.toString()}
                  onChange={(e) => handleSizeChange('width', parseFloat(e.target.value) || 5)}
                />
              </div>
              <div className={styles.inputGroup}>
                <label className={styles.label}>Height (%)</label>
                <Input
                  type="number"
                  min="5"
                  max="100"
                  value={logoSettings.size.height.toString()}
                  onChange={(e) => handleSizeChange('height', parseFloat(e.target.value) || 5)}
                />
              </div>
            </div>
          </div>

          <div className={styles.controlGroup}>
            <h4 className={styles.controlTitle}>Opacity</h4>
            <div className={styles.inputGroup}>
              <label className={styles.label}>Opacity</label>
              <Input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={logoSettings.opacity.toString()}
                onChange={(e) => handleOpacityChange(parseFloat(e.target.value))}
              />
              <span className={styles.opacityValue}>
                {Math.round(logoSettings.opacity * 100)}%
              </span>
            </div>
          </div>

          <div className={styles.presetButtons}>
            <Button
              size="small"
              variant="outline"
              onClick={() => onLogoChange({
                ...logoSettings,
                position: { x: 10, y: 10 },
                size: { width: 20, height: 15 }
              })}
            >
              Top Left
            </Button>
            <Button
              size="small"
              variant="outline"
              onClick={() => onLogoChange({
                ...logoSettings,
                position: { x: 40, y: 10 },
                size: { width: 20, height: 15 }
              })}
            >
              Top Center
            </Button>
            <Button
              size="small"
              variant="outline"
              onClick={() => onLogoChange({
                ...logoSettings,
                position: { x: 70, y: 10 },
                size: { width: 20, height: 15 }
              })}
            >
              Top Right
            </Button>
          </div>

          <div className={styles.previewSection}>
            <h4 className={styles.controlTitle}>Interactive Preview</h4>
            <p className={styles.previewDescription}>
              Drag the logo to reposition it, or drag the blue handle to resize it
            </p>
            <div className={styles.letterPreview}>
              <LogoDisplay
                logoSettings={logoSettings}
                onLogoChange={onLogoChange}
                isDraggable={true}
                className={styles.previewLogo}
              />
              <div className={styles.letterOutline}>
                <div className={styles.letterHeader}>Letter Size Preview (8.5" × 11")</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
