:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #213547;
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

/* 基本按鈕樣式 */
button {
  border-radius: 6px;
  border: 1px solid #ddd;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #ffffff;
  color: #000000;
  cursor: pointer;
  transition: all 0.25s ease;
}

button:hover {
  background-color: #f0f0f0;
  border-color: #999;
}

button:focus,
button:focus-visible {
  outline: 2px solid #646cff;
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 基本輸入框樣式 */
input, select, textarea {
  border-radius: 4px;
  border: 1px solid #ddd;
  padding: 0.5em;
  font-size: 1em;
  font-family: inherit;
  background-color: #ffffff;
  transition: border-color 0.25s ease;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #646cff;
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.1);
}

/* 錯誤狀態樣式 */
.error {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1) !important;
}

.error-message {
  color: #dc3545;
  font-size: 0.875em;
  margin-top: 0.25rem;
}