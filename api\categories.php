<?php
/**
 * Categories API
 * 分類API接口
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

require_once 'init.php';

$categoryModel = new Category();

// 記錄API請求
logApiRequest('/categories', $_SERVER['REQUEST_METHOD'], getRequestData());

// 路由定義
$router->get('/categories', function() use ($categoryModel) {
    $data = getCleanRequestData();
    
    // 類型過濾
    $type = isset($data['type']) ? $data['type'] : null;
    
    if ($type) {
        if ($type === 'hardware') {
            $categories = $categoryModel->getHardwareCategories();
        } elseif ($type === 'service') {
            $categories = $categoryModel->getServiceCategories();
        } else {
            sendError('Invalid category type', 400);
            return;
        }
    } else {
        $categories = $categoryModel->getActiveCategories();
    }
    
    sendSuccess($categories, 'Categories retrieved successfully');
});

$router->get('/categories/{id}', function($id) use ($categoryModel) {
    $id = validateNumericParam($id, 'id');
    
    $category = $categoryModel->findById($id);
    if (!$category) {
        sendError('Category not found', 404);
    }
    
    // 獲取分類統計信息
    $stats = $categoryModel->getCategoryStats($id);
    $category['stats'] = $stats;
    
    sendSuccess($category, 'Category retrieved successfully');
});

$router->post('/categories', function() use ($categoryModel) {
    $data = getCleanRequestData();
    
    // 驗證必需參數
    if (!validateRequiredParams($data, ['name_en', 'name_zh', 'type'])) {
        return;
    }
    
    $result = $categoryModel->createCategory($data);
    
    if ($result['success']) {
        sendSuccess($result['data'], $result['message']);
    } else {
        sendError('Failed to create category', 400, $result['errors']);
    }
});

$router->put('/categories/{id}', function($id) use ($categoryModel) {
    $id = validateNumericParam($id, 'id');
    $data = getCleanRequestData();
    
    // 驗證必需參數
    if (!validateRequiredParams($data, ['name_en', 'name_zh', 'type'])) {
        return;
    }
    
    $result = $categoryModel->updateCategory($id, $data);
    
    if ($result['success']) {
        sendSuccess($result['data'], $result['message']);
    } else {
        sendError('Failed to update category', 400, $result['errors']);
    }
});

$router->delete('/categories/{id}', function($id) use ($categoryModel) {
    $id = validateNumericParam($id, 'id');
    
    $result = $categoryModel->deleteCategory($id);
    
    if ($result['success']) {
        sendSuccess(null, $result['message']);
    } else {
        sendError('Failed to delete category', 400, $result['errors']);
    }
});

// 獲取硬件分類
$router->get('/categories/hardware/list', function() use ($categoryModel) {
    $categories = $categoryModel->getHardwareCategories();
    sendSuccess($categories, 'Hardware categories retrieved successfully');
});

// 獲取服務分類
$router->get('/categories/service/list', function() use ($categoryModel) {
    $categories = $categoryModel->getServiceCategories();
    sendSuccess($categories, 'Service categories retrieved successfully');
});

// 更新分類排序
$router->post('/categories/sort', function() use ($categoryModel) {
    $data = getCleanRequestData();
    
    if (!validateRequiredParams($data, ['category_ids'])) {
        return;
    }
    
    $categoryIds = $data['category_ids'];
    
    if (!is_array($categoryIds) || empty($categoryIds)) {
        sendError('Category IDs must be a non-empty array', 400);
        return;
    }
    
    $result = $categoryModel->updateSortOrder($categoryIds);
    
    if ($result['success']) {
        sendSuccess(null, $result['message']);
    } else {
        sendError('Failed to update sort order', 400, $result['errors']);
    }
});

// 搜索分類
$router->get('/categories/search', function() use ($categoryModel) {
    $data = getCleanRequestData();
    
    if (!validateRequiredParams($data, ['keyword'])) {
        return;
    }
    
    $keyword = $data['keyword'];
    $type = isset($data['type']) ? $data['type'] : null;
    $limit = isset($data['limit']) ? (int)$data['limit'] : 10;
    
    $categories = $categoryModel->searchCategories($keyword, $type, $limit);
    sendSuccess($categories, 'Categories search completed');
});

// 批量操作
$router->post('/categories/batch', function() use ($categoryModel) {
    $data = getCleanRequestData();
    
    if (!validateRequiredParams($data, ['action', 'ids'])) {
        return;
    }
    
    $action = $data['action'];
    $ids = $data['ids'];
    
    if (!is_array($ids) || empty($ids)) {
        sendError('IDs must be a non-empty array', 400);
    }
    
    $results = [];
    $errors = [];
    
    switch ($action) {
        case 'delete':
            foreach ($ids as $id) {
                $result = $categoryModel->deleteCategory($id);
                if ($result['success']) {
                    $results[] = $id;
                } else {
                    $errors[$id] = $result['errors'];
                }
            }
            break;
            
        case 'activate':
            foreach ($ids as $id) {
                $result = $categoryModel->update($id, ['is_active' => 1]);
                if ($result > 0) {
                    $results[] = $id;
                } else {
                    $errors[$id] = 'Failed to activate category';
                }
            }
            break;
            
        case 'deactivate':
            foreach ($ids as $id) {
                $result = $categoryModel->update($id, ['is_active' => 0]);
                if ($result > 0) {
                    $results[] = $id;
                } else {
                    $errors[$id] = 'Failed to deactivate category';
                }
            }
            break;
            
        default:
            sendError('Invalid action', 400);
            return;
    }
    
    $response = [
        'processed' => $results,
        'errors' => $errors,
        'total_processed' => count($results),
        'total_errors' => count($errors)
    ];
    
    if (empty($errors)) {
        sendSuccess($response, 'Batch operation completed successfully');
    } else {
        sendSuccess($response, 'Batch operation completed with some errors');
    }
});

// 執行路由
$router->run();
?>
