<?php
/**
 * Stores API
 * 商店API接口
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

require_once 'init.php';

$storeModel = new Store();

// 記錄API請求
logApiRequest('/stores', $_SERVER['REQUEST_METHOD'], getRequestData());

// 路由定義
$router->get('/stores', function() use ($storeModel) {
    $data = getCleanRequestData();
    $pagination = handlePagination($data);
    
    // 搜索功能
    if (!empty($data['search'])) {
        $stores = $storeModel->searchStores($data['search'], $pagination['per_page']);
        sendSuccess($stores, 'Stores retrieved successfully');
        return;
    }
    
    // 獲取所有商店（分頁）
    $result = $storeModel->paginate($pagination['page'], $pagination['per_page'], ['is_active' => 1], 'name ASC');
    sendSuccess($result, 'Stores retrieved successfully');
});

$router->get('/stores/{id}', function($id) use ($storeModel) {
    $id = validateNumericParam($id, 'id');
    
    $store = $storeModel->findById($id);
    if (!$store) {
        sendError('Store not found', 404);
    }
    
    // 獲取商店統計信息
    $stats = $storeModel->getStoreStats($id);
    $store['stats'] = $stats;
    
    sendSuccess($store, 'Store retrieved successfully');
});

$router->post('/stores', function() use ($storeModel) {
    $data = getCleanRequestData();
    
    // 驗證必需參數
    if (!validateRequiredParams($data, ['name', 'address', 'phone', 'email'])) {
        return;
    }
    
    $result = $storeModel->createStore($data);
    
    if ($result['success']) {
        sendSuccess($result['data'], $result['message']);
    } else {
        sendError('Failed to create store', 400, $result['errors']);
    }
});

$router->put('/stores/{id}', function($id) use ($storeModel) {
    $id = validateNumericParam($id, 'id');
    $data = getCleanRequestData();
    
    // 驗證必需參數
    if (!validateRequiredParams($data, ['name', 'address', 'phone', 'email'])) {
        return;
    }
    
    $result = $storeModel->updateStore($id, $data);
    
    if ($result['success']) {
        sendSuccess($result['data'], $result['message']);
    } else {
        sendError('Failed to update store', 400, $result['errors']);
    }
});

$router->delete('/stores/{id}', function($id) use ($storeModel) {
    $id = validateNumericParam($id, 'id');
    
    $result = $storeModel->deleteStore($id);
    
    if ($result['success']) {
        sendSuccess(null, $result['message']);
    } else {
        sendError('Failed to delete store', 400, $result['errors']);
    }
});

// 獲取商店的收據列表
$router->get('/stores/{id}/receipts', function($id) use ($storeModel) {
    $id = validateNumericParam($id, 'id');
    $data = getCleanRequestData();
    $pagination = handlePagination($data);
    
    // 檢查商店是否存在
    $store = $storeModel->findById($id);
    if (!$store) {
        sendError('Store not found', 404);
    }
    
    $result = $storeModel->getStoreReceipts($id, $pagination['page'], $pagination['per_page']);
    sendSuccess($result, 'Store receipts retrieved successfully');
});

// 獲取商店統計信息
$router->get('/stores/{id}/stats', function($id) use ($storeModel) {
    $id = validateNumericParam($id, 'id');
    
    // 檢查商店是否存在
    $store = $storeModel->findById($id);
    if (!$store) {
        sendError('Store not found', 404);
    }
    
    $stats = $storeModel->getStoreStats($id);
    sendSuccess($stats, 'Store statistics retrieved successfully');
});

// 獲取所有啟用的商店（簡化版本，用於下拉選單）
$router->get('/stores/active/list', function() use ($storeModel) {
    $stores = $storeModel->getActiveStores();
    
    // 簡化數據結構
    $simplifiedStores = array_map(function($store) {
        return [
            'id' => $store['id'],
            'name' => $store['name'],
            'email' => $store['email'],
            'phone' => $store['phone']
        ];
    }, $stores);
    
    sendSuccess($simplifiedStores, 'Active stores list retrieved successfully');
});

// 檢查郵箱是否可用
$router->post('/stores/check-email', function() use ($storeModel) {
    $data = getCleanRequestData();
    
    if (!validateRequiredParams($data, ['email'])) {
        return;
    }
    
    $excludeId = isset($data['exclude_id']) ? (int)$data['exclude_id'] : null;
    $exists = $storeModel->emailExists($data['email'], $excludeId);
    
    sendSuccess([
        'available' => !$exists,
        'message' => $exists ? 
            '此郵箱已被使用 / This email is already in use' : 
            '此郵箱可以使用 / This email is available'
    ], 'Email availability checked');
});

// 批量操作
$router->post('/stores/batch', function() use ($storeModel) {
    $data = getCleanRequestData();
    
    if (!validateRequiredParams($data, ['action', 'ids'])) {
        return;
    }
    
    $action = $data['action'];
    $ids = $data['ids'];
    
    if (!is_array($ids) || empty($ids)) {
        sendError('IDs must be a non-empty array', 400);
    }
    
    $results = [];
    $errors = [];
    
    switch ($action) {
        case 'delete':
            foreach ($ids as $id) {
                $result = $storeModel->deleteStore($id);
                if ($result['success']) {
                    $results[] = $id;
                } else {
                    $errors[$id] = $result['errors'];
                }
            }
            break;
            
        case 'activate':
            foreach ($ids as $id) {
                $result = $storeModel->update($id, ['is_active' => 1]);
                if ($result > 0) {
                    $results[] = $id;
                } else {
                    $errors[$id] = 'Failed to activate store';
                }
            }
            break;
            
        case 'deactivate':
            foreach ($ids as $id) {
                $result = $storeModel->update($id, ['is_active' => 0]);
                if ($result > 0) {
                    $results[] = $id;
                } else {
                    $errors[$id] = 'Failed to deactivate store';
                }
            }
            break;
            
        default:
            sendError('Invalid action', 400);
            return;
    }
    
    $response = [
        'processed' => $results,
        'errors' => $errors,
        'total_processed' => count($results),
        'total_errors' => count($errors)
    ];
    
    if (empty($errors)) {
        sendSuccess($response, 'Batch operation completed successfully');
    } else {
        sendSuccess($response, 'Batch operation completed with some errors');
    }
});

// 執行路由
$router->run();
?>
