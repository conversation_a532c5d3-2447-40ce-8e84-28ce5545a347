.container {
  background: #ffffff;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.header {
  margin-bottom: 1.5rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.uploadSection {
  margin-bottom: 1.5rem;
}

.hiddenInput {
  display: none;
}

.uploadArea {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f9fafb;
}

.uploadArea:hover {
  border-color: #6366f1;
  background: #f0f9ff;
}

.uploadIcon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.uploadText {
  color: #374151;
}

.uploadText strong {
  color: #1f2937;
}

.uploadText small {
  color: #6b7280;
}

.logoPreview {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
}

.previewImage {
  width: 80px;
  height: 60px;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  background: white;
}

.logoActions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.controlsSection {
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}

.controlGroup {
  margin-bottom: 1.5rem;
}

.controlTitle {
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 0.75rem 0;
}

.controlRow {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.inputGroup {
  flex: 1;
  min-width: 120px;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.opacityValue {
  font-size: 0.875rem;
  color: #6b7280;
  margin-left: 0.5rem;
}

.presetButtons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 1rem;
}

.previewSection {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.previewDescription {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.letterPreview {
  position: relative;
  width: 100%;
  max-width: 340px; /* 8.5:11 ratio scaled down */
  height: 440px; /* Letter size aspect ratio */
  margin: 0 auto;
  background: white;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.letterOutline {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border: 1px dashed #9ca3af;
  box-sizing: border-box;
}

.letterHeader {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: #6b7280;
  background: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

.previewLogo {
  z-index: 10;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .controlRow {
    flex-direction: column;
  }
  
  .inputGroup {
    min-width: auto;
  }
  
  .logoPreview {
    flex-direction: column;
    text-align: center;
  }
  
  .presetButtons {
    justify-content: center;
  }
}

/* 拖拽狀態 */
.dragging {
  opacity: 0.7;
  transform: scale(0.95);
  transition: all 0.2s ease;
}

/* 焦點狀態 */
.uploadArea:focus-within {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* 載入狀態 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #6366f1;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 錯誤狀態 */
.error {
  border-color: #ef4444;
  background: #fef2f2;
}

.error .uploadText {
  color: #dc2626;
}

/* 成功狀態 */
.success {
  border-color: #10b981;
  background: #f0fdf4;
}

.success .uploadText {
  color: #059669;
}
