# 設計文件

## 概述

電腦收據生成器是一個單頁面網站應用程式，使用現代前端技術構建。系統採用客戶端渲染架構，所有資料處理和計算都在瀏覽器中進行，無需後端服務器。應用程式專門針對 letter 紙張尺寸（8.5" x 11"）進行最佳化，提供專業的收據生成和列印功能。

## 架構

### 技術棧
- **前端框架**: React 18 with TypeScript
- **狀態管理**: React Context API + useReducer
- **樣式**: CSS Modules + CSS Grid/Flexbox
- **PDF 生成**: html2pdf.js 或 jsPDF
- **建置工具**: Vite
- **部署**: 靜態網站託管（Netlify/Vercel）

### 架構模式
- **組件化架構**: 將 UI 分解為可重用的 React 組件
- **狀態提升**: 使用 Context API 管理全域狀態
- **關注點分離**: 將業務邏輯、UI 組件和樣式分離

## 組件和介面

### 核心組件結構

```
App
├── Header
├── ReceiptForm
│   ├── StoreInfoSection
│   ├── CustomerInfoSection
│   └── ItemsSection
│       ├── ItemForm
│       └── ItemList
│           └── ItemRow
├── ReceiptPreview
│   ├── ReceiptHeader
│   ├── ReceiptBody
│   │   └── ItemTable
│   └── ReceiptFooter
└── ActionButtons
    ├── PrintButton
    └── SavePDFButton
```

### 主要介面定義

```typescript
interface ReceiptItem {
  id: string;
  name: string;
  category: 'hardware' | 'service';
  price: number;
  quantity: number;
}

interface StoreInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
}

interface CustomerInfo {
  name: string;
  phone: string;
  email: string;
}

interface ReceiptData {
  receiptNumber: string;
  date: Date;
  storeInfo: StoreInfo;
  customerInfo: CustomerInfo;
  items: ReceiptItem[];
  taxRate: number;
  subtotal: number;
  taxAmount: number;
  total: number;
}
```

### 狀態管理

使用 React Context 和 useReducer 管理應用程式狀態：

```typescript
interface AppState {
  receiptData: ReceiptData;
  isEditing: boolean;
  editingItemId: string | null;
}

type AppAction = 
  | { type: 'ADD_ITEM'; payload: ReceiptItem }
  | { type: 'UPDATE_ITEM'; payload: { id: string; item: Partial<ReceiptItem> } }
  | { type: 'DELETE_ITEM'; payload: string }
  | { type: 'UPDATE_STORE_INFO'; payload: Partial<StoreInfo> }
  | { type: 'UPDATE_CUSTOMER_INFO'; payload: Partial<CustomerInfo> }
  | { type: 'SET_TAX_RATE'; payload: number };
```

## 資料模型

### 收據項目分類
- **硬體類別**: CPU、主機板、記憶體、硬碟、顯示卡、電源供應器等
- **服務類別**: 組裝服務、系統安裝、維修服務、技術支援等

### 計算邏輯
1. **小計計算**: `subtotal = sum(item.price * item.quantity)`
2. **稅額計算**: `taxAmount = subtotal * taxRate`
3. **總計計算**: `total = subtotal + taxAmount`

### 收據編號生成
使用時間戳和隨機數生成唯一收據編號：
```typescript
const generateReceiptNumber = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `RCP-${timestamp}-${random}`.toUpperCase();
};
```

## 錯誤處理

### 輸入驗證
- **價格驗證**: 必須為正數，最多兩位小數
- **必填欄位**: 項目名稱、價格、數量不能為空
- **格式驗證**: 電子郵件和電話號碼格式檢查

### 錯誤狀態管理
```typescript
interface ValidationError {
  field: string;
  message: string;
}

interface ErrorState {
  itemErrors: ValidationError[];
  storeInfoErrors: ValidationError[];
  customerInfoErrors: ValidationError[];
}
```

### 錯誤顯示策略
- 即時驗證：在用戶輸入時提供即時反饋
- 錯誤高亮：使用紅色邊框和錯誤訊息標示無效欄位
- 阻止提交：當存在驗證錯誤時禁用提交按鈕

## 測試策略

### 單元測試
- **組件測試**: 使用 React Testing Library 測試每個組件的渲染和互動
- **工具函數測試**: 測試計算邏輯、驗證函數和格式化函數
- **Hook 測試**: 測試自訂 Hook 的行為

### 整合測試
- **用戶流程測試**: 測試完整的收據建立流程
- **狀態管理測試**: 測試 Context 和 Reducer 的整合
- **列印功能測試**: 測試 PDF 生成和列印功能

### 視覺測試
- **響應式測試**: 確保在不同螢幕尺寸下的正確顯示
- **列印樣式測試**: 驗證 letter 紙張尺寸的列印效果
- **跨瀏覽器測試**: 確保在主要瀏覽器中的一致性

### CSS 列印樣式設計

```css
/* Letter 紙張尺寸: 8.5" x 11" = 816px x 1056px at 96 DPI */
@media print {
  .receipt-container {
    width: 8.5in;
    min-height: 11in;
    margin: 0;
    padding: 0.5in;
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .no-print {
    display: none !important;
  }
  
  .page-break {
    page-break-before: always;
  }
}

@page {
  size: letter;
  margin: 0.5in;
}
```

### PDF 生成策略

使用 html2pdf.js 進行 PDF 生成：

```typescript
const generatePDF = async (element: HTMLElement): Promise<void> => {
  const options = {
    margin: 0.5,
    filename: `receipt-${receiptNumber}.pdf`,
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2, useCORS: true },
    jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
  };
  
  await html2pdf().set(options).from(element).save();
};
```

### 效能最佳化

- **懶載入**: 對 PDF 生成庫進行動態載入
- **記憶化**: 使用 React.memo 和 useMemo 最佳化重新渲染
- **防抖動**: 對計算密集的操作使用 debounce
- **虛擬化**: 如果項目列表很長，考慮使用虛擬滾動