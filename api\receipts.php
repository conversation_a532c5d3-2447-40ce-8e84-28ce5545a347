<?php
/**
 * Receipts API
 * 收據API接口
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

require_once 'init.php';

$receiptModel = new Receipt();

// 記錄API請求
logApiRequest('/receipts', $_SERVER['REQUEST_METHOD'], getRequestData());

// 路由定義
$router->get('/receipts', function() use ($receiptModel) {
    $data = getCleanRequestData();
    $pagination = handlePagination($data);
    
    // 構建過濾條件
    $filters = [];
    if (!empty($data['store_id'])) {
        $filters['store_id'] = $data['store_id'];
    }
    if (!empty($data['customer_id'])) {
        $filters['customer_id'] = $data['customer_id'];
    }
    if (!empty($data['status'])) {
        $filters['status'] = $data['status'];
    }
    if (!empty($data['date_from'])) {
        $filters['date_from'] = $data['date_from'];
    }
    if (!empty($data['date_to'])) {
        $filters['date_to'] = $data['date_to'];
    }
    if (!empty($data['search'])) {
        $filters['search'] = $data['search'];
    }
    if (!empty($data['receipt_number'])) {
        $filters['receipt_number'] = $data['receipt_number'];
    }
    
    $result = $receiptModel->getReceiptsList($filters, $pagination['page'], $pagination['per_page']);
    sendSuccess($result, 'Receipts retrieved successfully');
});

$router->get('/receipts/{id}', function($id) use ($receiptModel) {
    $id = validateNumericParam($id, 'id');
    
    $receipt = $receiptModel->getReceiptWithDetails($id);
    if (!$receipt) {
        sendError('Receipt not found', 404);
    }
    
    sendSuccess($receipt, 'Receipt retrieved successfully');
});

$router->post('/receipts', function() use ($receiptModel) {
    $data = getCleanRequestData();
    
    // 驗證必需參數
    if (!validateRequiredParams($data, ['store_id', 'customer_name', 'customer_phone', 'receipt_date'])) {
        return;
    }
    
    // 驗證項目數據
    if (empty($data['items']) || !is_array($data['items'])) {
        sendError('At least one item is required', 400);
        return;
    }
    
    // 分離收據數據和項目數據
    $receiptData = $data;
    $items = $receiptData['items'];
    unset($receiptData['items']);
    
    $result = $receiptModel->createReceipt($receiptData, $items);
    
    if ($result['success']) {
        sendSuccess($result['data'], $result['message']);
    } else {
        sendError('Failed to create receipt', 400, $result['errors']);
    }
});

$router->put('/receipts/{id}', function($id) use ($receiptModel) {
    $id = validateNumericParam($id, 'id');
    $data = getCleanRequestData();
    
    // 驗證必需參數
    if (!validateRequiredParams($data, ['store_id', 'customer_name', 'customer_phone', 'receipt_date'])) {
        return;
    }
    
    // 分離收據數據和項目數據
    $receiptData = $data;
    $items = isset($receiptData['items']) ? $receiptData['items'] : [];
    unset($receiptData['items']);
    
    $result = $receiptModel->updateReceipt($id, $receiptData, $items);
    
    if ($result['success']) {
        sendSuccess($result['data'], $result['message']);
    } else {
        sendError('Failed to update receipt', 400, $result['errors']);
    }
});

$router->delete('/receipts/{id}', function($id) use ($receiptModel) {
    $id = validateNumericParam($id, 'id');
    
    try {
        $affected = $receiptModel->delete($id);
        
        if ($affected > 0) {
            sendSuccess(null, 'Receipt deleted successfully');
        } else {
            sendError('Receipt not found', 404);
        }
    } catch (Exception $e) {
        logMessage("Receipt deletion failed: " . $e->getMessage(), 'ERROR');
        sendError('Failed to delete receipt', 500);
    }
});

// 更新收據狀態
$router->put('/receipts/{id}/status', function($id) use ($receiptModel) {
    $id = validateNumericParam($id, 'id');
    $data = getCleanRequestData();
    
    if (!validateRequiredParams($data, ['status'])) {
        return;
    }
    
    $result = $receiptModel->updateStatus($id, $data['status']);
    
    if ($result['success']) {
        sendSuccess(null, $result['message']);
    } else {
        sendError('Failed to update status', 400, $result['errors']);
    }
});

// 根據收據編號查找
$router->get('/receipts/number/{receiptNumber}', function($receiptNumber) use ($receiptModel) {
    $receipt = $receiptModel->findByReceiptNumber($receiptNumber);
    
    if (!$receipt) {
        sendError('Receipt not found', 404);
    }
    
    // 獲取完整詳情
    $receiptDetails = $receiptModel->getReceiptWithDetails($receipt['id']);
    sendSuccess($receiptDetails, 'Receipt retrieved successfully');
});

// 獲取收據統計
$router->get('/receipts/stats', function() use ($receiptModel) {
    $data = getCleanRequestData();
    
    // 構建過濾條件
    $filters = [];
    if (!empty($data['store_id'])) {
        $filters['store_id'] = $data['store_id'];
    }
    if (!empty($data['date_from'])) {
        $filters['date_from'] = $data['date_from'];
    }
    if (!empty($data['date_to'])) {
        $filters['date_to'] = $data['date_to'];
    }
    
    // 這裡可以添加統計查詢邏輯
    $stats = [
        'total_receipts' => 0,
        'total_revenue' => 0,
        'avg_receipt_value' => 0,
        'status_breakdown' => [
            'draft' => 0,
            'completed' => 0,
            'cancelled' => 0
        ]
    ];
    
    sendSuccess($stats, 'Receipt statistics retrieved successfully');
});

// 複製收據
$router->post('/receipts/{id}/copy', function($id) use ($receiptModel) {
    $id = validateNumericParam($id, 'id');
    
    $originalReceipt = $receiptModel->getReceiptWithDetails($id);
    if (!$originalReceipt) {
        sendError('Original receipt not found', 404);
    }
    
    // 準備新收據數據
    $newReceiptData = $originalReceipt;
    unset($newReceiptData['id'], $newReceiptData['receipt_number'], $newReceiptData['created_at'], $newReceiptData['updated_at']);
    
    // 生成新的收據編號
    $newReceiptData['receipt_number'] = '';
    $newReceiptData['status'] = 'draft';
    
    // 準備項目數據
    $items = $originalReceipt['items'];
    foreach ($items as &$item) {
        unset($item['id'], $item['receipt_id'], $item['created_at']);
    }
    
    $result = $receiptModel->createReceipt($newReceiptData, $items);
    
    if ($result['success']) {
        sendSuccess($result['data'], 'Receipt copied successfully');
    } else {
        sendError('Failed to copy receipt', 400, $result['errors']);
    }
});

// 批量操作
$router->post('/receipts/batch', function() use ($receiptModel) {
    $data = getCleanRequestData();
    
    if (!validateRequiredParams($data, ['action', 'ids'])) {
        return;
    }
    
    $action = $data['action'];
    $ids = $data['ids'];
    
    if (!is_array($ids) || empty($ids)) {
        sendError('IDs must be a non-empty array', 400);
    }
    
    $results = [];
    $errors = [];
    
    switch ($action) {
        case 'delete':
            foreach ($ids as $id) {
                try {
                    $affected = $receiptModel->delete($id);
                    if ($affected > 0) {
                        $results[] = $id;
                    } else {
                        $errors[$id] = 'Receipt not found';
                    }
                } catch (Exception $e) {
                    $errors[$id] = 'Failed to delete receipt';
                }
            }
            break;
            
        case 'complete':
            foreach ($ids as $id) {
                $result = $receiptModel->updateStatus($id, 'completed');
                if ($result['success']) {
                    $results[] = $id;
                } else {
                    $errors[$id] = $result['errors'];
                }
            }
            break;
            
        case 'cancel':
            foreach ($ids as $id) {
                $result = $receiptModel->updateStatus($id, 'cancelled');
                if ($result['success']) {
                    $results[] = $id;
                } else {
                    $errors[$id] = $result['errors'];
                }
            }
            break;
            
        default:
            sendError('Invalid action', 400);
            return;
    }
    
    $response = [
        'processed' => $results,
        'errors' => $errors,
        'total_processed' => count($results),
        'total_errors' => count($errors)
    ];
    
    if (empty($errors)) {
        sendSuccess($response, 'Batch operation completed successfully');
    } else {
        sendSuccess($response, 'Batch operation completed with some errors');
    }
});

// 執行路由
$router->run();
?>
