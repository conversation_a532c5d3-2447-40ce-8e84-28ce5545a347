<?php
/**
 * Database Test API
 * 數據庫測試API
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

require_once 'init.php';

// 記錄API請求
logApiRequest('/test-db', $_SERVER['REQUEST_METHOD'], getRequestData());

try {
    // 測試數據庫連接
    $db = Database::getInstance();
    
    // 測試基本查詢
    $result = $db->query("SELECT 1 as test");
    
    if ($result) {
        // 測試表是否存在
        $tables = [];
        $requiredTables = ['stores', 'customers', 'categories', 'receipts', 'receipt_items'];
        
        foreach ($requiredTables as $table) {
            $checkTable = $db->query("SHOW TABLES LIKE '{$table}'");
            if ($checkTable && $checkTable->rowCount() > 0) {
                $tables[$table] = 'exists';
            } else {
                $tables[$table] = 'missing';
            }
        }
        
        // 測試數據庫版本
        $versionResult = $db->query("SELECT VERSION() as version");
        $version = $versionResult ? $versionResult->fetch(PDO::FETCH_ASSOC)['version'] : 'unknown';
        
        // 測試字符集
        $charsetResult = $db->query("SELECT @@character_set_database as charset, @@collation_database as collation");
        $charset = $charsetResult ? $charsetResult->fetch(PDO::FETCH_ASSOC) : ['charset' => 'unknown', 'collation' => 'unknown'];
        
        sendSuccess([
            'connection' => 'success',
            'database' => DB_NAME,
            'version' => $version,
            'charset' => $charset['charset'],
            'collation' => $charset['collation'],
            'tables' => $tables,
            'timestamp' => date('Y-m-d H:i:s')
        ], 'Database connection test successful');
        
    } else {
        sendError('Database query failed', 500);
    }
    
} catch (Exception $e) {
    logMessage("Database test failed: " . $e->getMessage(), 'ERROR');
    sendError('Database connection failed: ' . $e->getMessage(), 500);
}
?>
