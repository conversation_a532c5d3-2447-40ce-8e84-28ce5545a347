.logoWrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.logoContainer {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  user-select: none;
  transition: box-shadow 0.2s ease;
}

.logoContainer.draggable {
  pointer-events: auto;
  border: 2px dashed transparent;
}

.logoContainer.draggable:hover {
  border-color: #6366f1;
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.3);
}

.logoImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;

  /* 確保在 PDF 中正確顯示 */
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

.resizeHandle {
  position: absolute;
  bottom: -5px;
  right: -5px;
  width: 12px;
  height: 12px;
  background: #6366f1;
  border: 2px solid white;
  border-radius: 50%;
  cursor: se-resize;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.resizeHandle:hover {
  background: #4f46e5;
  transform: scale(1.1);
}

/* 列印時的優化 */
@media print {
  .logoContainer {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  .logoImage {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}

/* PDF 生成優化 */
.pdfOptimized .logoContainer {
  position: absolute;
  z-index: 10;
}

.pdfOptimized .logoImage {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}
