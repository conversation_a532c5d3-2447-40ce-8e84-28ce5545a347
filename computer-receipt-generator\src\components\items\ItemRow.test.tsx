import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { ItemRow } from './ItemRow';
import { AppProvider } from '../../context/AppContext';
import { ReceiptItem } from '../../types';

const mockItem: ReceiptItem = {
  id: '1',
  name: 'Test CPU',
  category: 'hardware',
  price: 10000,
  quantity: 2,
};

const mockServiceItem: ReceiptItem = {
  id: '2',
  name: 'Installation Service',
  category: 'service',
  price: 1000,
  quantity: 1,
};

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <AppProvider>
      <table>
        <tbody>
          {component}
        </tbody>
      </table>
    </AppProvider>
  );
};

// Mock window.confirm
const mockConfirm = vi.fn();
Object.defineProperty(window, 'confirm', {
  value: mockConfirm,
  writable: true,
});

describe('ItemRow', () => {
  beforeEach(() => {
    mockConfirm.mockClear();
  });

  it('應該正確顯示項目資訊', () => {
    renderWithProvider(<ItemRow item={mockItem} />);
    
    expect(screen.getByText('Test CPU')).toBeInTheDocument();
    expect(screen.getByText('硬體')).toBeInTheDocument();
    expect(screen.getByText('NT$10,000.00')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('NT$20,000.00')).toBeInTheDocument(); // 10000 * 2
  });

  it('應該正確顯示服務類別', () => {
    renderWithProvider(<ItemRow item={mockServiceItem} />);
    
    expect(screen.getByText('Installation Service')).toBeInTheDocument();
    expect(screen.getByText('服務')).toBeInTheDocument();
  });

  it('應該正確計算小計', () => {
    renderWithProvider(<ItemRow item={mockItem} />);
    
    // 價格 10000 * 數量 2 = 20000
    expect(screen.getByText('NT$20,000.00')).toBeInTheDocument();
  });

  it('應該顯示編輯和刪除按鈕', () => {
    renderWithProvider(<ItemRow item={mockItem} />);
    
    expect(screen.getByRole('button', { name: '編輯' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '刪除' })).toBeInTheDocument();
  });

  it('應該正確處理編輯按鈕點擊', () => {
    renderWithProvider(<ItemRow item={mockItem} />);
    
    const editButton = screen.getByRole('button', { name: '編輯' });
    fireEvent.click(editButton);
    
    // 這裡我們無法直接測試 startEditingItem 的調用，
    // 但可以確保按鈕是可點擊的且沒有錯誤
    expect(editButton).toBeInTheDocument();
  });

  it('應該在刪除時顯示確認對話框', () => {
    mockConfirm.mockReturnValue(true);
    renderWithProvider(<ItemRow item={mockItem} />);
    
    const deleteButton = screen.getByRole('button', { name: '刪除' });
    fireEvent.click(deleteButton);
    
    expect(mockConfirm).toHaveBeenCalledWith('確定要刪除這個項目嗎？');
  });

  it('應該在用戶取消刪除時不執行刪除', () => {
    mockConfirm.mockReturnValue(false);
    renderWithProvider(<ItemRow item={mockItem} />);
    
    const deleteButton = screen.getByRole('button', { name: '刪除' });
    fireEvent.click(deleteButton);
    
    expect(mockConfirm).toHaveBeenCalledWith('確定要刪除這個項目嗎？');
    // 項目應該仍然存在
    expect(screen.getByText('Test CPU')).toBeInTheDocument();
  });

  it('應該正確格式化貨幣顯示', () => {
    const expensiveItem: ReceiptItem = {
      id: '3',
      name: 'Expensive Item',
      category: 'hardware',
      price: 123456.78,
      quantity: 1,
    };
    
    renderWithProvider(<ItemRow item={expensiveItem} />);
    
    expect(screen.getAllByText('NT$123,456.78')).toHaveLength(2); // 單價和小計都會顯示
  });

  it('應該正確處理小數價格', () => {
    const decimalItem: ReceiptItem = {
      id: '4',
      name: 'Decimal Item',
      category: 'service',
      price: 99.99,
      quantity: 3,
    };
    
    renderWithProvider(<ItemRow item={decimalItem} />);
    
    expect(screen.getByText('NT$99.99')).toBeInTheDocument();
    expect(screen.getByText('NT$299.97')).toBeInTheDocument(); // 99.99 * 3
  });

  it('應該有正確的按鈕標題屬性', () => {
    renderWithProvider(<ItemRow item={mockItem} />);
    
    const editButton = screen.getByRole('button', { name: '編輯' });
    const deleteButton = screen.getByRole('button', { name: '刪除' });
    
    expect(editButton).toHaveAttribute('title', '編輯項目');
    expect(deleteButton).toHaveAttribute('title', '刪除項目');
  });
});