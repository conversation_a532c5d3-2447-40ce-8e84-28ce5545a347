-- KMS PC Receipt Maker Database
-- 電腦收據生成器數據庫
-- Created: 2025-01-14

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- 創建數據庫
CREATE DATABASE IF NOT EXISTS `kms_receipt_maker` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `kms_receipt_maker`;

-- --------------------------------------------------------
-- 商店管理功能 - Stores Management
-- --------------------------------------------------------

CREATE TABLE `stores` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '商店名稱',
  `address` text NOT NULL COMMENT '商店地址',
  `phone` varchar(50) NOT NULL COMMENT '電話號碼',
  `email` varchar(255) NOT NULL COMMENT '電子郵件',
  `logo_url` varchar(500) DEFAULT NULL COMMENT 'Logo圖片URL',
  `tax_id` varchar(50) DEFAULT NULL COMMENT '統一編號',
  `website` varchar(255) DEFAULT NULL COMMENT '網站',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否啟用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商店信息表';

-- --------------------------------------------------------
-- 客戶管理功能 - Customers Management
-- --------------------------------------------------------

CREATE TABLE `customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '客戶姓名',
  `phone` varchar(50) NOT NULL COMMENT '電話號碼',
  `email` varchar(255) DEFAULT NULL COMMENT '電子郵件',
  `address` text DEFAULT NULL COMMENT '地址',
  `company` varchar(255) DEFAULT NULL COMMENT '公司名稱',
  `tax_id` varchar(50) DEFAULT NULL COMMENT '統一編號',
  `notes` text DEFAULT NULL COMMENT '備註',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否啟用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `phone` (`phone`),
  KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客戶信息表';

-- --------------------------------------------------------
-- 分類管理功能 - Categories Management
-- --------------------------------------------------------

CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name_en` varchar(100) NOT NULL COMMENT '英文名稱',
  `name_zh` varchar(100) NOT NULL COMMENT '中文名稱',
  `type` enum('hardware','service') NOT NULL DEFAULT 'hardware' COMMENT '類型：硬件或服務',
  `description_en` text DEFAULT NULL COMMENT '英文描述',
  `description_zh` text DEFAULT NULL COMMENT '中文描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序順序',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否啟用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='項目分類表';

-- --------------------------------------------------------
-- 收據管理功能 - Receipts Management
-- --------------------------------------------------------

CREATE TABLE `receipts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `receipt_number` varchar(50) NOT NULL COMMENT '收據編號',
  `store_id` int(11) NOT NULL COMMENT '商店ID',
  `customer_id` int(11) DEFAULT NULL COMMENT '客戶ID',
  `customer_name` varchar(255) DEFAULT NULL COMMENT '客戶姓名（臨時）',
  `customer_phone` varchar(50) DEFAULT NULL COMMENT '客戶電話（臨時）',
  `customer_email` varchar(255) DEFAULT NULL COMMENT '客戶郵箱（臨時）',
  `customer_address` text DEFAULT NULL COMMENT '客戶地址（臨時）',
  `receipt_date` date NOT NULL COMMENT '收據日期',
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '稅率（%）',
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '小計',
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '稅額',
  `total` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '總計',
  `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '折扣金額',
  `notes` text DEFAULT NULL COMMENT '備註',
  `status` enum('draft','completed','cancelled') NOT NULL DEFAULT 'draft' COMMENT '狀態',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `receipt_number` (`receipt_number`),
  KEY `store_id` (`store_id`),
  KEY `customer_id` (`customer_id`),
  KEY `receipt_date` (`receipt_date`),
  KEY `status` (`status`),
  CONSTRAINT `receipts_ibfk_1` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`) ON DELETE CASCADE,
  CONSTRAINT `receipts_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收據主表';

-- --------------------------------------------------------
-- 收據項目功能 - Receipt Items Management
-- --------------------------------------------------------

CREATE TABLE `receipt_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `receipt_id` int(11) NOT NULL COMMENT '收據ID',
  `category_id` int(11) DEFAULT NULL COMMENT '分類ID',
  `name` varchar(255) NOT NULL COMMENT '項目名稱',
  `description` text DEFAULT NULL COMMENT '項目描述',
  `price` decimal(10,2) NOT NULL COMMENT '單價',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原價',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '數量',
  `hide_price` tinyint(1) DEFAULT 0 COMMENT '是否隱藏價格',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序順序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `receipt_id` (`receipt_id`),
  KEY `category_id` (`category_id`),
  KEY `sort_order` (`sort_order`),
  CONSTRAINT `receipt_items_ibfk_1` FOREIGN KEY (`receipt_id`) REFERENCES `receipts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `receipt_items_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收據項目明細表';

-- --------------------------------------------------------
-- 系統設置功能 - System Settings
-- --------------------------------------------------------

CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_name` varchar(100) NOT NULL COMMENT '設置鍵名',
  `value` text DEFAULT NULL COMMENT '設置值',
  `description_en` text DEFAULT NULL COMMENT '英文描述',
  `description_zh` text DEFAULT NULL COMMENT '中文描述',
  `type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '數據類型',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公開',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_name` (`key_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系統設置表';

-- --------------------------------------------------------
-- 插入示例數據 - Sample Data
-- --------------------------------------------------------

-- 插入商店示例數據
INSERT INTO `stores` (`name`, `address`, `phone`, `email`, `tax_id`, `website`) VALUES
('KMS PC Store', '123 Tech Street, Taipei City, Taiwan', '+886-2-1234-5678', '<EMAIL>', '12345678', 'https://www.kmspc.com'),
('電腦專家', '台北市信義區科技路456號', '02-8765-4321', '<EMAIL>', '87654321', 'https://www.pcexpert.tw');

-- 插入分類示例數據
INSERT INTO `categories` (`name_en`, `name_zh`, `type`, `description_en`, `description_zh`, `sort_order`) VALUES
('PC Case', '機殼', 'hardware', 'Computer cases and enclosures', '電腦機殼和外殼', 1),
('CPU', '處理器', 'hardware', 'Central Processing Units', '中央處理器', 2),
('CPU Cooler', 'CPU散熱器', 'hardware', 'CPU cooling solutions', 'CPU散熱解決方案', 3),
('GPU', '顯示卡', 'hardware', 'Graphics Processing Units', '圖形處理器', 4),
('RAM', '記憶體', 'hardware', 'Random Access Memory', '隨機存取記憶體', 5),
('SSD', '固態硬碟', 'hardware', 'Solid State Drives', '固態硬碟', 6),
('Motherboard', '主機板', 'hardware', 'Computer motherboards', '電腦主機板', 7),
('PSU', '電源供應器', 'hardware', 'Power Supply Units', '電源供應器', 8),
('RGB', 'RGB燈效', 'hardware', 'RGB lighting components', 'RGB燈效組件', 9),
('Other Hardware', '其他硬體', 'hardware', 'Other hardware components', '其他硬體組件', 10),
('Labor Fee', '工時費', 'service', 'Labor and service fees', '工時和服務費用', 11),
('OS Installation', '系統安裝', 'service', 'Operating system installation', '作業系統安裝', 12),
('Overclocking', '超頻服務', 'service', 'CPU/GPU overclocking service', 'CPU/GPU超頻服務', 13),
('Package Deal', '套裝優惠', 'service', 'Package deals and bundles', '套裝優惠和組合', 14),
('Other Services', '其他服務', 'service', 'Other services', '其他服務', 15);

-- 插入客戶示例數據
INSERT INTO `customers` (`name`, `phone`, `email`, `address`, `company`) VALUES
('張小明', '0912-345-678', '<EMAIL>', '台北市大安區復興南路100號', '科技公司'),
('李美華', '0987-654-321', '<EMAIL>', '新北市板橋區中山路200號', NULL),
('John Smith', '0955-123-456', '<EMAIL>', 'No.300, Xinyi Road, Taipei', 'Tech Solutions Inc.');

-- 插入系統設置示例數據
INSERT INTO `settings` (`key_name`, `value`, `description_en`, `description_zh`, `type`) VALUES
('default_tax_rate', '5.00', 'Default tax rate percentage', '預設稅率百分比', 'number'),
('company_name', 'KMS PC Store', 'Default company name', '預設公司名稱', 'string'),
('receipt_prefix', 'RCP', 'Receipt number prefix', '收據編號前綴', 'string'),
('currency_symbol', 'NT$', 'Currency symbol', '貨幣符號', 'string'),
('date_format', 'Y-m-d', 'Date format', '日期格式', 'string'),
('language', 'zh-TW', 'Default language', '預設語言', 'string'),
('enable_logo', '1', 'Enable logo display', '啟用Logo顯示', 'boolean'),
('max_items_per_receipt', '50', 'Maximum items per receipt', '每張收據最大項目數', 'number');

-- --------------------------------------------------------
-- 創建索引和約束 - Indexes and Constraints
-- --------------------------------------------------------

-- 為性能優化添加額外索引
CREATE INDEX `idx_receipts_date_status` ON `receipts` (`receipt_date`, `status`);
CREATE INDEX `idx_receipt_items_receipt_sort` ON `receipt_items` (`receipt_id`, `sort_order`);
CREATE INDEX `idx_customers_name_phone` ON `customers` (`name`, `phone`);
CREATE INDEX `idx_categories_type_sort` ON `categories` (`type`, `sort_order`);

-- --------------------------------------------------------
-- 創建視圖 - Views
-- --------------------------------------------------------

-- 收據詳細視圖（包含商店和客戶信息）
CREATE VIEW `receipt_details` AS
SELECT
    r.id,
    r.receipt_number,
    r.receipt_date,
    r.subtotal,
    r.tax_amount,
    r.total,
    r.status,
    s.name as store_name,
    s.address as store_address,
    s.phone as store_phone,
    s.email as store_email,
    COALESCE(c.name, r.customer_name) as customer_name,
    COALESCE(c.phone, r.customer_phone) as customer_phone,
    COALESCE(c.email, r.customer_email) as customer_email,
    COALESCE(c.address, r.customer_address) as customer_address
FROM receipts r
LEFT JOIN stores s ON r.store_id = s.id
LEFT JOIN customers c ON r.customer_id = c.id;

-- 收據項目詳細視圖（包含分類信息）
CREATE VIEW `receipt_item_details` AS
SELECT
    ri.id,
    ri.receipt_id,
    ri.name,
    ri.description,
    ri.price,
    ri.original_price,
    ri.quantity,
    ri.hide_price,
    ri.sort_order,
    (ri.price * ri.quantity) as line_total,
    COALESCE(cat.name_zh, cat.name_en) as category_name,
    cat.type as category_type
FROM receipt_items ri
LEFT JOIN categories cat ON ri.category_id = cat.id
ORDER BY ri.receipt_id, ri.sort_order;

COMMIT;
