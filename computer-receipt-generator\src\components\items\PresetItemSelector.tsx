import React, { useState, useEffect } from 'react';
import { Button } from '../ui';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { presetItems, presetItemsByCategory, type PresetItem } from '../../data/presetItems';
import styles from './PresetItemSelector.module.css';

interface PresetItemSelectorProps {
  onSelectItem: (item: PresetItem) => void;
  className?: string;
}

export const PresetItemSelector: React.FC<PresetItemSelectorProps> = ({
  onSelectItem,
  className = '',
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [isExpanded, setIsExpanded] = useState(false);
  const [customItems, setCustomItems] = useState<PresetItem[]>([]);
  const [editingItem, setEditingItem] = useState<PresetItem | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    name: '',
    price: 0,
    originalPrice: undefined as number | undefined,
    description: '',
  });

  // 從 localStorage 載入自定義項目
  useEffect(() => {
    const saved = localStorage.getItem('customPresetItems');
    if (saved) {
      try {
        setCustomItems(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load custom preset items:', error);
      }
    }
  }, []);

  // 保存自定義項目到 localStorage
  const saveCustomItems = (items: PresetItem[]) => {
    localStorage.setItem('customPresetItems', JSON.stringify(items));
    setCustomItems(items);
  };

  // 為預設項目添加 id
  const itemsWithId = presetItems.map((item, index) => ({
    ...item,
    id: item.id || `preset-${index}`,
  }));

  // 合併預設項目和自定義項目
  const allItems = [...itemsWithId, ...customItems];
  const allItemsByCategory = presetItemsByCategory;

  // 獲取類別圖示
  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: string } = {
      'PC Case': '🏠',
      'CPU': '🧠',
      'CPU Cooler': '❄️',
      'GPU': '🎮',
      'RAM': '💾',
      'SSD': '💿',
      'Motherboard': '🔌',
      'PSU': '⚡',
      'RGB': '🌈',
      'Other': '🔧',
      'Labor Fee': '👷',
      'OS': '💻',
      'OC': '⚡',
      'Package': '📦',
      'Other Services': '⚙️'
    };
    return icons[category] || '📦';
  };

  const getFilteredItems = () => {
    if (selectedCategory === 'All') {
      return allItems;
    } else {
      return allItemsByCategory[selectedCategory as keyof typeof allItemsByCategory] || [];
    }
  };

  const handleItemSelect = (item: PresetItem) => {
    onSelectItem(item);
  };

  const handleEditItem = (item: PresetItem) => {
    setEditingItem(item);
    setEditForm({
      name: item.name,
      price: item.price,
      originalPrice: item.originalPrice,
      description: item.description || '',
    });
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = () => {
    if (!editingItem) return;

    const updatedItem: PresetItem = {
      ...editingItem,
      name: editForm.name.trim(),
      price: editForm.price,
      originalPrice: editForm.originalPrice,
      description: editForm.description.trim(),
    };

    if (editingItem.isCustom) {
      // 更新自定義項目
      const updatedCustomItems = customItems.map(item => 
        item.id === editingItem.id ? updatedItem : item
      );
      saveCustomItems(updatedCustomItems);
    } else {
      // 將預設項目轉為自定義項目
      const newCustomItem: PresetItem = {
        ...updatedItem,
        id: `custom-${Date.now()}`,
        isCustom: true,
      };
      saveCustomItems([...customItems, newCustomItem]);
    }

    setIsEditModalOpen(false);
    setEditingItem(null);
  };

  const handleDeleteCustomItem = (itemId: string) => {
    const updatedCustomItems = customItems.filter(item => item.id !== itemId);
    saveCustomItems(updatedCustomItems);
  };

  const filteredItems = getFilteredItems();

  return (
    <div className={`${styles.container} ${className}`}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h4 className={styles.title}>Quick Add Items</h4>
          <p className={styles.description}>
            Click any item below to quickly add it to your receipt
          </p>
        </div>
        <Button
          variant="outline"
          size="small"
          onClick={() => setIsExpanded(!isExpanded)}
          className={styles.toggleButton}
        >
          {isExpanded ? 'Hide Presets' : 'Show Presets'}
        </Button>
      </div>

      {isExpanded && (
        <div className={styles.content}>
          {/* Category Filter */}
          <div className={styles.categoryFilter}>
            <Button
              variant={selectedCategory === 'All' ? 'primary' : 'outline'}
              size="small"
              onClick={() => setSelectedCategory('All')}
              className={styles.categoryButton}
            >
              All ({allItems.length})
            </Button>
            {Object.keys(allItemsByCategory).map(category => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'primary' : 'outline'}
                size="small"
                onClick={() => setSelectedCategory(category)}
                className={styles.categoryButton}
              >
                {category} ({allItemsByCategory[category as keyof typeof allItemsByCategory].length})
              </Button>
            ))}
          </div>

          {/* 項目列表 - List View */}
          <div className={styles.itemsList}>
            {filteredItems.map((item: PresetItem) => {
              const discountPercentage = item.originalPrice && item.originalPrice > item.price
                ? Math.round(((item.originalPrice - item.price) / item.originalPrice) * 100)
                : 0;
              
              return (
                <div key={item.id} className={styles.itemRow}>
                  <div className={styles.itemInfo}>
                    <div className={styles.itemHeader}>
                      <span className={styles.itemName}>{item.name}</span>
                      <div className={styles.itemMeta}>
                        <span className={styles.itemCategory}>
                          {getCategoryIcon(item.category)} {item.category}
                        </span>
                        {item.isCustom && (
                          <span className={styles.customBadge}>Custom</span>
                        )}
                      </div>
                    </div>
                    
                    {item.description && (
                      <div className={styles.itemDescription}>{item.description}</div>
                    )}
                  </div>
                  
                  <div className={styles.itemPrice}>
                    <span className={styles.currentPrice}>${item.price.toFixed(2)}</span>
                    {item.originalPrice && (
                      <div className={styles.originalPriceContainer}>
                        <span className={styles.originalPrice}>${item.originalPrice.toFixed(2)}</span>
                        <span className={styles.discountBadge}>-{discountPercentage}%</span>
                      </div>
                    )}
                  </div>
                  
                  <div className={styles.itemActions}>
                    <Button
                      size="small"
                      variant="outline"
                      onClick={() => handleEditItem(item)}
                      className={styles.editButton}
                    >
                      Edit
                    </Button>
                    {item.isCustom && (
                      <Button
                        size="small"
                        variant="danger"
                        onClick={() => handleDeleteCustomItem(item.id)}
                        className={styles.deleteButton}
                      >
                        Delete
                      </Button>
                    )}
                    <Button
                      size="small"
                      variant="primary"
                      onClick={() => handleItemSelect(item)}
                      className={styles.addButton}
                    >
                      Add Item
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* 編輯彈窗 */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title={editingItem?.isCustom ? "Edit Custom Item" : "Edit Preset Item"}
        onConfirm={handleSaveEdit}
        onCancel={() => setIsEditModalOpen(false)}
        confirmText="Save Changes"
        cancelText="Cancel"
        size="medium"
      >
        <div className={styles.editForm}>
          <div className={styles.formGroup}>
            <Input
              label="Item Name"
              type="text"
              value={editForm.name}
              onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter item name"
              required
            />
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <Input
                label="Price"
                type="number"
                value={editForm.price.toString()}
                onChange={(e) => setEditForm(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                placeholder="0.00"
                step="0.01"
                min="0"
                required
              />
            </div>

            <div className={styles.formGroup}>
              <Input
                label="Original Price (Optional)"
                type="number"
                value={editForm.originalPrice?.toString() || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  setEditForm(prev => ({ ...prev, originalPrice: value ? parseFloat(value) : undefined }));
                }}
                placeholder="0.00"
                step="0.01"
                min="0"
              />
            </div>
          </div>

          <div className={styles.formGroup}>
            <Input
              label="Description"
              type="text"
              value={editForm.description}
              onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Enter item description"
            />
          </div>

          {editForm.originalPrice && editForm.originalPrice > editForm.price && (
            <div className={styles.discountInfo}>
              <div className={styles.discountLabel}>Discount Information:</div>
              <div className={styles.discountDetails}>
                <span className={styles.discountAmount}>
                  Save ${(editForm.originalPrice - editForm.price).toFixed(2)}
                </span>
                <span className={styles.discountPercent}>
                  ({Math.round(((editForm.originalPrice - editForm.price) / editForm.originalPrice) * 100)}% OFF)
                </span>
              </div>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};
