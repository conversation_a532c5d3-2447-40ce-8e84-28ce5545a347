import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { ReceiptHeader } from './ReceiptHeader';
import { StoreInfo, CustomerInfo } from '../../types';

describe('ReceiptHeader', () => {
  const mockStoreInfo: StoreInfo = {
    name: '測試電腦店',
    address: '台北市信義區信義路五段7號',
    phone: '02-1234-5678',
    email: '<EMAIL>',
  };

  const mockCustomerInfo: CustomerInfo = {
    name: '王小明',
    phone: '0912-345-678',
    email: '<EMAIL>',
  };

  const mockDate = new Date('2024-01-15T10:30:00');
  const mockReceiptNumber = 'RCP-TEST-12345';

  it('renders store information correctly', () => {
    render(
      <ReceiptHeader
        storeInfo={mockStoreInfo}
        customerInfo={mockCustomerInfo}
        receiptNumber={mockReceiptNumber}
        date={mockDate}
      />
    );

    expect(screen.getByText('測試電腦店')).toBeInTheDocument();
    expect(screen.getByText('台北市信義區信義路五段7號')).toBeInTheDocument();
    expect(screen.getByText('電話: 02-1234-5678')).toBeInTheDocument();
    expect(screen.getByText('Email: <EMAIL>')).toBeInTheDocument();
  });

  it('renders receipt information correctly', () => {
    render(
      <ReceiptHeader
        storeInfo={mockStoreInfo}
        customerInfo={mockCustomerInfo}
        receiptNumber={mockReceiptNumber}
        date={mockDate}
      />
    );

    expect(screen.getByText('銷售收據')).toBeInTheDocument();
    expect(screen.getByText('RCP-TEST-12345')).toBeInTheDocument();
    expect(screen.getByText('2024/01/15 10:30')).toBeInTheDocument();
  });

  it('renders customer information correctly', () => {
    render(
      <ReceiptHeader
        storeInfo={mockStoreInfo}
        customerInfo={mockCustomerInfo}
        receiptNumber={mockReceiptNumber}
        date={mockDate}
      />
    );

    expect(screen.getByText('客戶資訊')).toBeInTheDocument();
    expect(screen.getByText('王小明')).toBeInTheDocument();
    expect(screen.getByText('0912-345-678')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('handles empty store name with fallback', () => {
    const emptyStoreInfo: StoreInfo = {
      name: '',
      address: '',
      phone: '',
      email: '',
    };

    render(
      <ReceiptHeader
        storeInfo={emptyStoreInfo}
        customerInfo={mockCustomerInfo}
        receiptNumber={mockReceiptNumber}
        date={mockDate}
      />
    );

    expect(screen.getByText('商店名稱')).toBeInTheDocument();
  });

  it('handles empty customer name with fallback', () => {
    const emptyCustomerInfo: CustomerInfo = {
      name: '',
      phone: '',
      email: '',
    };

    render(
      <ReceiptHeader
        storeInfo={mockStoreInfo}
        customerInfo={emptyCustomerInfo}
        receiptNumber={mockReceiptNumber}
        date={mockDate}
      />
    );

    expect(screen.getByText('未提供')).toBeInTheDocument();
  });

  it('does not render optional fields when empty', () => {
    const minimalStoreInfo: StoreInfo = {
      name: '簡單店',
      address: '',
      phone: '',
      email: '',
    };

    const minimalCustomerInfo: CustomerInfo = {
      name: '客戶',
      phone: '',
      email: '',
    };

    render(
      <ReceiptHeader
        storeInfo={minimalStoreInfo}
        customerInfo={minimalCustomerInfo}
        receiptNumber={mockReceiptNumber}
        date={mockDate}
      />
    );

    expect(screen.getByText('簡單店')).toBeInTheDocument();
    expect(screen.getByText('客戶')).toBeInTheDocument();
    
    // 空的聯絡資訊不應該顯示
    expect(screen.queryByText('電話:')).not.toBeInTheDocument();
    expect(screen.queryByText('Email:')).not.toBeInTheDocument();
  });

  it('renders store contact info with separator when both phone and email exist', () => {
    render(
      <ReceiptHeader
        storeInfo={mockStoreInfo}
        customerInfo={mockCustomerInfo}
        receiptNumber={mockReceiptNumber}
        date={mockDate}
      />
    );

    const separator = screen.getByText('|');
    expect(separator).toBeInTheDocument();
  });

  it('does not render separator when only one contact method exists', () => {
    const storeWithOnlyPhone: StoreInfo = {
      name: '測試店',
      address: '地址',
      phone: '02-1234-5678',
      email: '',
    };

    render(
      <ReceiptHeader
        storeInfo={storeWithOnlyPhone}
        customerInfo={mockCustomerInfo}
        receiptNumber={mockReceiptNumber}
        date={mockDate}
      />
    );

    expect(screen.queryByText('|')).not.toBeInTheDocument();
  });
});