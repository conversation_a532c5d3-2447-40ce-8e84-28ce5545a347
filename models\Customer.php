<?php
/**
 * Customer Model
 * 客戶模型類
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

// 防止直接訪問
if (!defined('KMS_RECEIPT_MAKER')) {
    die('Direct access not permitted');
}

require_once 'BaseModel.php';

/**
 * 客戶模型類
 * Customer Model Class
 */
class Customer extends BaseModel {
    protected $table = 'customers';
    protected $fillable = [
        'name',
        'phone',
        'email',
        'address',
        'company',
        'tax_id',
        'notes',
        'is_active'
    ];

    /**
     * 獲取所有啟用的客戶
     * Get all active customers
     * 
     * @return array
     */
    public function getActiveCustomers() {
        return $this->findAll(['is_active' => 1], 'name ASC');
    }

    /**
     * 根據電話查找客戶
     * Find customer by phone
     * 
     * @param string $phone 電話
     * @return array|false
     */
    public function findByPhone($phone) {
        return $this->findOne(['phone' => $phone]);
    }

    /**
     * 根據郵箱查找客戶
     * Find customer by email
     * 
     * @param string $email 郵箱
     * @return array|false
     */
    public function findByEmail($email) {
        return $this->findOne(['email' => $email]);
    }

    /**
     * 檢查電話是否已存在
     * Check if phone exists
     * 
     * @param string $phone 電話
     * @param int $excludeId 排除的ID（用於更新時檢查）
     * @return bool
     */
    public function phoneExists($phone, $excludeId = null) {
        if ($excludeId) {
            $sql = "SELECT COUNT(*) as count FROM `{$this->table}` WHERE `phone` = :phone AND `id` != :exclude_id";
            $result = $this->db->fetchRow($sql, ['phone' => $phone, 'exclude_id' => $excludeId]);
            return (int)$result['count'] > 0;
        }
        
        return $this->exists(['phone' => $phone]);
    }

    /**
     * 創建客戶
     * Create customer
     * 
     * @param array $data 客戶數據
     * @return array 結果
     */
    public function createCustomer($data) {
        // 驗證數據
        $errors = validateCustomerData($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        // 檢查電話是否已存在
        if ($this->phoneExists($data['phone'])) {
            return [
                'success' => false,
                'errors' => ['phone' => '此電話號碼已被使用 / This phone number is already in use']
            ];
        }

        // 檢查郵箱是否已存在（如果提供）
        if (!empty($data['email']) && $this->exists(['email' => $data['email']])) {
            return [
                'success' => false,
                'errors' => ['email' => '此郵箱已被使用 / This email is already in use']
            ];
        }

        try {
            $customerId = $this->create($data);
            $customer = $this->findById($customerId);
            
            return [
                'success' => true,
                'data' => $customer,
                'message' => '客戶創建成功 / Customer created successfully'
            ];
        } catch (Exception $e) {
            logMessage("Customer creation failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '創建客戶失敗 / Failed to create customer']
            ];
        }
    }

    /**
     * 更新客戶
     * Update customer
     * 
     * @param int $id 客戶ID
     * @param array $data 客戶數據
     * @return array 結果
     */
    public function updateCustomer($id, $data) {
        // 檢查客戶是否存在
        $customer = $this->findById($id);
        if (!$customer) {
            return [
                'success' => false,
                'errors' => ['general' => '客戶不存在 / Customer not found']
            ];
        }

        // 驗證數據
        $errors = validateCustomerData($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        // 檢查電話是否已被其他客戶使用
        if ($this->phoneExists($data['phone'], $id)) {
            return [
                'success' => false,
                'errors' => ['phone' => '此電話號碼已被其他客戶使用 / This phone number is already used by another customer']
            ];
        }

        // 檢查郵箱是否已被其他客戶使用（如果提供）
        if (!empty($data['email'])) {
            $sql = "SELECT COUNT(*) as count FROM `{$this->table}` WHERE `email` = :email AND `id` != :exclude_id";
            $result = $this->db->fetchRow($sql, ['email' => $data['email'], 'exclude_id' => $id]);
            if ((int)$result['count'] > 0) {
                return [
                    'success' => false,
                    'errors' => ['email' => '此郵箱已被其他客戶使用 / This email is already used by another customer']
                ];
            }
        }

        try {
            $this->update($id, $data);
            $updatedCustomer = $this->findById($id);
            
            return [
                'success' => true,
                'data' => $updatedCustomer,
                'message' => '客戶更新成功 / Customer updated successfully'
            ];
        } catch (Exception $e) {
            logMessage("Customer update failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '更新客戶失敗 / Failed to update customer']
            ];
        }
    }

    /**
     * 刪除客戶
     * Delete customer
     * 
     * @param int $id 客戶ID
     * @return array 結果
     */
    public function deleteCustomer($id) {
        // 檢查客戶是否存在
        $customer = $this->findById($id);
        if (!$customer) {
            return [
                'success' => false,
                'errors' => ['general' => '客戶不存在 / Customer not found']
            ];
        }

        // 檢查是否有關聯的收據
        $receiptCount = $this->db->fetchRow(
            "SELECT COUNT(*) as count FROM receipts WHERE customer_id = :customer_id",
            ['customer_id' => $id]
        );

        if ($receiptCount['count'] > 0) {
            // 如果有關聯收據，執行軟刪除
            try {
                $this->update($id, ['is_active' => 0]);
                return [
                    'success' => true,
                    'message' => '客戶已停用（因為有關聯的收據） / Customer deactivated (has associated receipts)'
                ];
            } catch (Exception $e) {
                logMessage("Customer soft delete failed: " . $e->getMessage(), 'ERROR');
                return [
                    'success' => false,
                    'errors' => ['general' => '停用客戶失敗 / Failed to deactivate customer']
                ];
            }
        } else {
            // 如果沒有關聯收據，執行硬刪除
            try {
                $this->delete($id);
                return [
                    'success' => true,
                    'message' => '客戶刪除成功 / Customer deleted successfully'
                ];
            } catch (Exception $e) {
                logMessage("Customer delete failed: " . $e->getMessage(), 'ERROR');
                return [
                    'success' => false,
                    'errors' => ['general' => '刪除客戶失敗 / Failed to delete customer']
                ];
            }
        }
    }

    /**
     * 搜索客戶
     * Search customers
     * 
     * @param string $keyword 關鍵詞
     * @param int $limit 限制數量
     * @return array
     */
    public function searchCustomers($keyword, $limit = 10) {
        $sql = "
            SELECT * FROM `{$this->table}` 
            WHERE (`name` LIKE :keyword OR `phone` LIKE :keyword OR `email` LIKE :keyword OR `company` LIKE :keyword)
            AND `is_active` = 1
            ORDER BY `name` ASC
            LIMIT {$limit}
        ";
        
        $keyword = '%' . $keyword . '%';
        return $this->db->fetchAll($sql, ['keyword' => $keyword]);
    }

    /**
     * 獲取客戶統計信息
     * Get customer statistics
     * 
     * @param int $id 客戶ID
     * @return array
     */
    public function getCustomerStats($id) {
        $sql = "
            SELECT 
                COUNT(r.id) as total_receipts,
                COALESCE(SUM(r.total), 0) as total_spent,
                COALESCE(AVG(r.total), 0) as avg_receipt_value,
                MAX(r.receipt_date) as last_purchase_date,
                COUNT(CASE WHEN r.receipt_date >= CURDATE() - INTERVAL 30 DAY THEN 1 END) as receipts_last_30_days
            FROM customers c
            LEFT JOIN receipts r ON c.id = r.customer_id AND r.status = 'completed'
            WHERE c.id = :customer_id
            GROUP BY c.id
        ";

        $result = $this->db->fetchRow($sql, ['customer_id' => $id]);
        
        if (!$result) {
            return [
                'total_receipts' => 0,
                'total_spent' => 0,
                'avg_receipt_value' => 0,
                'last_purchase_date' => null,
                'receipts_last_30_days' => 0
            ];
        }

        return $result;
    }

    /**
     * 獲取客戶的收據列表
     * Get customer receipts
     * 
     * @param int $customerId 客戶ID
     * @param int $page 頁碼
     * @param int $perPage 每頁數量
     * @return array
     */
    public function getCustomerReceipts($customerId, $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        
        $sql = "
            SELECT r.*, s.name as store_name
            FROM receipts r
            LEFT JOIN stores s ON r.store_id = s.id
            WHERE r.customer_id = :customer_id
            ORDER BY r.receipt_date DESC, r.created_at DESC
            LIMIT {$perPage} OFFSET {$offset}
        ";
        
        $countSql = "SELECT COUNT(*) as count FROM receipts WHERE customer_id = :customer_id";
        
        $data = $this->db->fetchAll($sql, ['customer_id' => $customerId]);
        $total = $this->db->fetchRow($countSql, ['customer_id' => $customerId])['count'];
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
    }
}
?>
