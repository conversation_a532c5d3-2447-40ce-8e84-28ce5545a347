import React from 'react';
import { Button, type ButtonProps } from './Button';

interface PrintButtonProps extends Omit<ButtonProps, 'onClick' | 'children'> {
  targetElementId?: string;
  onBeforePrint?: () => void;
  onAfterPrint?: () => void;
  children?: React.ReactNode;
}

export const PrintButton: React.FC<PrintButtonProps> = ({
  targetElementId,
  onBeforePrint,
  onAfterPrint,
  children = '列印收據',
  variant = 'primary',
  ...props
}) => {
  const handlePrint = () => {
    // 執行列印前的回調
    onBeforePrint?.();

    // 如果指定了目標元素，則只列印該元素
    if (targetElementId) {
      const targetElement = document.getElementById(targetElementId);
      if (targetElement) {
        // 創建新視窗進行列印
        const printWindow = window.open('', '_blank');
        if (printWindow) {
          // 複製樣式
          const styles = Array.from(document.styleSheets)
            .map(styleSheet => {
              try {
                return Array.from(styleSheet.cssRules)
                  .map(rule => rule.cssText)
                  .join('\n');
              } catch (e) {
                // 處理跨域樣式表
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = styleSheet.href || '';
                return link.outerHTML;
              }
            })
            .join('\n');

          printWindow.document.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>收據列印</title>
                <meta charset="utf-8">
                <style>
                  ${styles}
                  
                  /* 額外的列印優化樣式 */
                  body {
                    margin: 0;
                    padding: 0;
                    font-family: 'Arial', 'Microsoft JhengHei', sans-serif;
                  }
                  
                  .noPrint {
                    display: none !important;
                  }
                  
                  @media print {
                    @page {
                      size: letter;
                      margin: 0.5in;
                    }
                    
                    body {
                      -webkit-print-color-adjust: exact;
                      print-color-adjust: exact;
                    }
                  }
                </style>
              </head>
              <body>
                ${targetElement.outerHTML}
              </body>
            </html>
          `);
          
          printWindow.document.close();
          
          // 等待內容載入後列印
          printWindow.onload = () => {
            printWindow.focus();
            printWindow.print();
            
            // 列印完成後關閉視窗
            printWindow.onafterprint = () => {
              printWindow.close();
              onAfterPrint?.();
            };
          };
        }
      }
    } else {
      // 列印整個頁面
      window.print();
      onAfterPrint?.();
    }
  };

  return (
    <Button
      variant={variant}
      onClick={handlePrint}
      {...props}
    >
      {children}
    </Button>
  );
};