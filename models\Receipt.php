<?php
/**
 * Receipt Model
 * 收據模型類
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

// 防止直接訪問
if (!defined('KMS_RECEIPT_MAKER')) {
    die('Direct access not permitted');
}

require_once 'BaseModel.php';

/**
 * 收據模型類
 * Receipt Model Class
 */
class Receipt extends BaseModel {
    protected $table = 'receipts';
    protected $fillable = [
        'receipt_number',
        'store_id',
        'customer_id',
        'customer_name',
        'customer_phone',
        'customer_email',
        'customer_address',
        'receipt_date',
        'tax_rate',
        'subtotal',
        'tax_amount',
        'total',
        'discount_amount',
        'notes',
        'status'
    ];

    /**
     * 創建收據
     * Create receipt
     * 
     * @param array $receiptData 收據數據
     * @param array $items 收據項目
     * @return array 結果
     */
    public function createReceipt($receiptData, $items = []) {
        // 驗證收據數據
        $errors = validateReceiptData($receiptData);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        // 驗證項目數據
        if (empty($items)) {
            return [
                'success' => false,
                'errors' => ['items' => '至少需要一個項目 / At least one item is required']
            ];
        }

        foreach ($items as $index => $item) {
            $itemErrors = validateReceiptItemData($item);
            if (!empty($itemErrors)) {
                return [
                    'success' => false,
                    'errors' => ["item_{$index}" => $itemErrors]
                ];
            }
        }

        // 生成收據編號（如果未提供）
        if (empty($receiptData['receipt_number'])) {
            $receiptData['receipt_number'] = $this->generateUniqueReceiptNumber();
        }

        // 計算金額
        $calculations = $this->calculateReceiptTotals($items, $receiptData['tax_rate'] ?? 0);
        $receiptData = array_merge($receiptData, $calculations);

        // 開始事務
        $this->beginTransaction();

        try {
            // 創建收據
            $receiptId = $this->create($receiptData);

            // 創建收據項目
            $receiptItemModel = new ReceiptItem();
            foreach ($items as $index => $item) {
                $item['receipt_id'] = $receiptId;
                $item['sort_order'] = $index + 1;
                $receiptItemModel->create($item);
            }

            $this->commit();

            // 獲取完整的收據數據
            $receipt = $this->getReceiptWithDetails($receiptId);

            return [
                'success' => true,
                'data' => $receipt,
                'message' => '收據創建成功 / Receipt created successfully'
            ];

        } catch (Exception $e) {
            $this->rollback();
            logMessage("Receipt creation failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '創建收據失敗 / Failed to create receipt']
            ];
        }
    }

    /**
     * 更新收據
     * Update receipt
     * 
     * @param int $id 收據ID
     * @param array $receiptData 收據數據
     * @param array $items 收據項目
     * @return array 結果
     */
    public function updateReceipt($id, $receiptData, $items = []) {
        // 檢查收據是否存在
        $receipt = $this->findById($id);
        if (!$receipt) {
            return [
                'success' => false,
                'errors' => ['general' => '收據不存在 / Receipt not found']
            ];
        }

        // 驗證數據
        $errors = validateReceiptData($receiptData);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        if (!empty($items)) {
            foreach ($items as $index => $item) {
                $itemErrors = validateReceiptItemData($item);
                if (!empty($itemErrors)) {
                    return [
                        'success' => false,
                        'errors' => ["item_{$index}" => $itemErrors]
                    ];
                }
            }
        }

        // 計算金額
        if (!empty($items)) {
            $calculations = $this->calculateReceiptTotals($items, $receiptData['tax_rate'] ?? $receipt['tax_rate']);
            $receiptData = array_merge($receiptData, $calculations);
        }

        // 開始事務
        $this->beginTransaction();

        try {
            // 更新收據
            $this->update($id, $receiptData);

            // 更新收據項目（如果提供）
            if (!empty($items)) {
                $receiptItemModel = new ReceiptItem();
                
                // 刪除舊項目
                $receiptItemModel->deleteByReceiptId($id);
                
                // 創建新項目
                foreach ($items as $index => $item) {
                    $item['receipt_id'] = $id;
                    $item['sort_order'] = $index + 1;
                    $receiptItemModel->create($item);
                }
            }

            $this->commit();

            // 獲取更新後的收據數據
            $updatedReceipt = $this->getReceiptWithDetails($id);

            return [
                'success' => true,
                'data' => $updatedReceipt,
                'message' => '收據更新成功 / Receipt updated successfully'
            ];

        } catch (Exception $e) {
            $this->rollback();
            logMessage("Receipt update failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '更新收據失敗 / Failed to update receipt']
            ];
        }
    }

    /**
     * 獲取收據詳細信息
     * Get receipt with details
     * 
     * @param int $id 收據ID
     * @return array|false
     */
    public function getReceiptWithDetails($id) {
        $sql = "
            SELECT 
                r.*,
                s.name as store_name,
                s.address as store_address,
                s.phone as store_phone,
                s.email as store_email,
                s.logo_url as store_logo,
                COALESCE(c.name, r.customer_name) as customer_name,
                COALESCE(c.phone, r.customer_phone) as customer_phone,
                COALESCE(c.email, r.customer_email) as customer_email,
                COALESCE(c.address, r.customer_address) as customer_address
            FROM receipts r
            LEFT JOIN stores s ON r.store_id = s.id
            LEFT JOIN customers c ON r.customer_id = c.id
            WHERE r.id = :id
        ";

        $receipt = $this->db->fetchRow($sql, ['id' => $id]);
        
        if ($receipt) {
            // 獲取收據項目
            $itemsSql = "
                SELECT 
                    ri.*,
                    cat.name_zh as category_name_zh,
                    cat.name_en as category_name_en,
                    cat.type as category_type
                FROM receipt_items ri
                LEFT JOIN categories cat ON ri.category_id = cat.id
                WHERE ri.receipt_id = :receipt_id
                ORDER BY ri.sort_order ASC
            ";
            
            $receipt['items'] = $this->db->fetchAll($itemsSql, ['receipt_id' => $id]);
        }

        return $receipt;
    }

    /**
     * 計算收據總計
     * Calculate receipt totals
     * 
     * @param array $items 項目數組
     * @param float $taxRate 稅率
     * @return array 計算結果
     */
    private function calculateReceiptTotals($items, $taxRate = 0) {
        $subtotal = 0;

        foreach ($items as $item) {
            $lineTotal = $item['price'] * $item['quantity'];
            $subtotal += $lineTotal;
        }

        $taxAmount = calculateTax($subtotal, $taxRate);
        $total = calculateTotal($subtotal, $taxAmount);

        return [
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total' => $total
        ];
    }

    /**
     * 生成唯一收據編號
     * Generate unique receipt number
     * 
     * @param string $prefix 前綴
     * @return string
     */
    private function generateUniqueReceiptNumber($prefix = 'RCP') {
        $maxAttempts = 10;
        $attempts = 0;

        do {
            $receiptNumber = generateReceiptNumber($prefix);
            $exists = $this->exists(['receipt_number' => $receiptNumber]);
            $attempts++;
        } while ($exists && $attempts < $maxAttempts);

        if ($exists) {
            // 如果仍然重複，添加時間戳
            $receiptNumber .= '-' . time();
        }

        return $receiptNumber;
    }

    /**
     * 根據收據編號查找
     * Find by receipt number
     * 
     * @param string $receiptNumber 收據編號
     * @return array|false
     */
    public function findByReceiptNumber($receiptNumber) {
        return $this->findOne(['receipt_number' => $receiptNumber]);
    }

    /**
     * 獲取收據列表
     * Get receipts list
     * 
     * @param array $filters 過濾條件
     * @param int $page 頁碼
     * @param int $perPage 每頁數量
     * @return array
     */
    public function getReceiptsList($filters = [], $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $whereConditions = [];
        $params = [];

        // 構建查詢條件
        if (!empty($filters['store_id'])) {
            $whereConditions[] = "r.store_id = :store_id";
            $params['store_id'] = $filters['store_id'];
        }

        if (!empty($filters['customer_id'])) {
            $whereConditions[] = "r.customer_id = :customer_id";
            $params['customer_id'] = $filters['customer_id'];
        }

        if (!empty($filters['status'])) {
            $whereConditions[] = "r.status = :status";
            $params['status'] = $filters['status'];
        }

        if (!empty($filters['date_from'])) {
            $whereConditions[] = "r.receipt_date >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $whereConditions[] = "r.receipt_date <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }

        if (!empty($filters['search'])) {
            $whereConditions[] = "(r.receipt_number LIKE :search OR r.customer_name LIKE :search OR c.name LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        $sql = "
            SELECT 
                r.*,
                s.name as store_name,
                COALESCE(c.name, r.customer_name) as customer_name,
                COALESCE(c.phone, r.customer_phone) as customer_phone
            FROM receipts r
            LEFT JOIN stores s ON r.store_id = s.id
            LEFT JOIN customers c ON r.customer_id = c.id
            {$whereClause}
            ORDER BY r.receipt_date DESC, r.created_at DESC
            LIMIT {$perPage} OFFSET {$offset}
        ";

        $countSql = "
            SELECT COUNT(*) as count
            FROM receipts r
            LEFT JOIN customers c ON r.customer_id = c.id
            {$whereClause}
        ";

        $data = $this->db->fetchAll($sql, $params);
        $total = $this->db->fetchRow($countSql, $params)['count'];

        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
    }

    /**
     * 更新收據狀態
     * Update receipt status
     * 
     * @param int $id 收據ID
     * @param string $status 狀態
     * @return array 結果
     */
    public function updateStatus($id, $status) {
        $validStatuses = ['draft', 'completed', 'cancelled'];
        
        if (!in_array($status, $validStatuses)) {
            return [
                'success' => false,
                'errors' => ['status' => '無效的狀態 / Invalid status']
            ];
        }

        try {
            $affected = $this->update($id, ['status' => $status]);
            
            if ($affected > 0) {
                return [
                    'success' => true,
                    'message' => '收據狀態更新成功 / Receipt status updated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'errors' => ['general' => '收據不存在 / Receipt not found']
                ];
            }
        } catch (Exception $e) {
            logMessage("Receipt status update failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '更新狀態失敗 / Failed to update status']
            ];
        }
    }
}
?>
