import React from 'react';
import { render } from '@testing-library/react';
import type { RenderOptions } from '@testing-library/react';
import { AppProvider } from '../context/AppContext';
import type { ReceiptItem, StoreInfo, CustomerInfo } from '../types';

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <AppProvider>
      {children}
    </AppProvider>
  );
};

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };

// Test data factories
export const createMockReceiptItem = (overrides: Partial<ReceiptItem> = {}): ReceiptItem => ({
  id: 'test-item-1',
  name: 'Test CPU',
  category: 'hardware',
  price: 299.99,
  quantity: 1,
  ...overrides,
});

export const createMockStoreInfo = (overrides: Partial<StoreInfo> = {}): StoreInfo => ({
  name: 'Test Computer Store',
  address: '123 Test Street, Test City, TC 12345',
  phone: '(*************',
  email: '<EMAIL>',
  ...overrides,
});

export const createMockCustomerInfo = (overrides: Partial<CustomerInfo> = {}): CustomerInfo => ({
  name: 'John Doe',
  phone: '(*************',
  email: '<EMAIL>',
  ...overrides,
});

// Mock data arrays
export const mockReceiptItems: ReceiptItem[] = [
  createMockReceiptItem({
    id: '1',
    name: 'Intel Core i7-12700K',
    category: 'hardware',
    price: 399.99,
    quantity: 1,
  }),
  createMockReceiptItem({
    id: '2',
    name: 'ASUS ROG Strix B660-F',
    category: 'hardware',
    price: 249.99,
    quantity: 1,
  }),
  createMockReceiptItem({
    id: '3',
    name: 'System Assembly Service',
    category: 'service',
    price: 75.00,
    quantity: 1,
  }),
];

// Helper functions for testing
export const waitForLoadingToFinish = () => 
  new Promise(resolve => setTimeout(resolve, 0));

// Mock implementations
export const mockPrintFunction = vi.fn();
export const mockPDFGeneration = vi.fn();

// Setup global mocks
Object.defineProperty(window, 'print', {
  value: mockPrintFunction,
  writable: true,
});

// Mock html2pdf
vi.mock('html2pdf.js', () => ({
  default: () => ({
    set: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    save: mockPDFGeneration,
  }),
}));