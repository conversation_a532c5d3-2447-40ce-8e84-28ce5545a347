import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { SavePDFButton } from './SavePDFButton';

// Mock html2pdf.js
vi.mock('html2pdf.js', () => ({
  default: vi.fn(() => ({
    set: vi.fn(() => ({
      from: vi.fn(() => ({
        save: vi.fn()
      }))
    }))
  }))
}));

describe('SavePDFButton', () => {
  it('renders with default text', () => {
    render(<SavePDFButton targetElementId="test-element" />);
    expect(screen.getByRole('button', { name: '儲存 PDF' })).toBeInTheDocument();
  });

  it('renders with custom text', () => {
    render(<SavePDFButton targetElementId="test-element">自訂 PDF 文字</SavePDFButton>);
    expect(screen.getByRole('button', { name: '自訂 PDF 文字' })).toBeInTheDocument();
  });

  it('passes through button props correctly', () => {
    render(
      <SavePDFButton 
        targetElementId="test-element"
        variant="primary" 
        size="large" 
        disabled 
        data-testid="pdf-btn"
      />
    );
    
    const button = screen.getByTestId('pdf-btn');
    expect(button).toBeDisabled();
    // Check that the button has the expected CSS module classes
    expect(button.className).toContain('primary');
    expect(button.className).toContain('large');
  });
});