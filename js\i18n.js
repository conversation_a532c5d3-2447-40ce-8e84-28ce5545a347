/**
 * Internationalization (i18n)
 * 國際化支持
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

'use strict';

// 語言包
const translations = {
    'zh-TW': {
        // 應用程序標題
        'app-title': 'KMS PC Receipt Maker',
        'app-subtitle': '電腦收據生成器',
        
        // 導航和界面
        'skip-to-content': '跳到主要內容',
        'loading': '載入中...',
        'receipt-generator': '收據生成器',
        'receipt-preview': '收據預覽',
        'clear-form': '清空表單',
        'load-template': '載入模板',
        'print-receipt': '列印收據',
        'save-pdf': '儲存PDF',
        'preview-empty': '請填寫表單以查看收據預覽',
        
        // 表單標籤
        'store-info': '商店資訊',
        'store-name': '商店名稱',
        'select-store': '請選擇商店',
        'receipt-number': '收據編號',
        'receipt-date': '收據日期',
        'tax-rate': '稅率 (%)',
        'generate': '生成',
        
        'customer-info': '客戶資訊',
        'customer-search': '搜尋客戶',
        'search-customer-placeholder': '輸入姓名或電話搜尋',
        'new-customer': '新客戶',
        'customer-name': '客戶姓名',
        'customer-phone': '客戶電話',
        'customer-email': '客戶郵箱',
        'customer-address': '客戶地址',
        
        'receipt-items': '收據項目',
        'item-category': '項目分類',
        'select-category': '請選擇分類',
        'item-name': '項目名稱',
        'item-price': '單價',
        'item-quantity': '數量',
        'add-item': '新增項目',
        'no-items': '尚未新增任何項目',
        
        // 按鈕和操作
        'save-draft': '儲存草稿',
        'generate-receipt': '生成收據',
        'edit': '編輯',
        'delete': '刪除',
        'cancel': '取消',
        'confirm': '確認',
        'save': '儲存',
        'close': '關閉',
        
        // 收據內容
        'receipt': '收據',
        'invoice': '發票',
        'bill-to': '客戶資訊',
        'item': '項目',
        'category': '分類',
        'price': '單價',
        'quantity': '數量',
        'total': '小計',
        'subtotal': '小計',
        'tax': '稅額',
        'grand-total': '總計',
        'notes': '備註',
        
        // 狀態
        'draft': '草稿',
        'completed': '已完成',
        'cancelled': '已取消',
        
        // 驗證消息
        'required-field': '此欄位為必填項',
        'invalid-email': '請輸入有效的電子郵件地址',
        'invalid-phone': '請輸入有效的電話號碼',
        'invalid-number': '請輸入有效的數字',
        'invalid-date': '請輸入有效的日期',
        'min-value': '值不能小於 {min}',
        'max-value': '值不能大於 {max}',
        
        // 成功消息
        'receipt-created': '收據創建成功',
        'receipt-updated': '收據更新成功',
        'receipt-deleted': '收據刪除成功',
        'data-saved': '數據保存成功',
        
        // 錯誤消息
        'error-occurred': '發生錯誤',
        'network-error': '網絡連接錯誤',
        'server-error': '服務器錯誤',
        'validation-error': '驗證錯誤',
        'not-found': '未找到',
        'permission-denied': '權限不足',
        
        // 確認對話框
        'confirm-delete': '確定要刪除嗎？',
        'confirm-clear': '確定要清空表單嗎？',
        'unsaved-changes': '有未保存的更改，確定要離開嗎？',
        
        // 頁腳
        'footer-copyright': '© 2025 KMS PC Store. 版權所有。',
        'footer-version': '版本 1.0.0',

        // 分類類型
        'hardware': '硬件',
        'service': '服務'
    },
    
    'en-US': {
        // Application title
        'app-title': 'KMS PC Receipt Maker',
        'app-subtitle': 'Computer Receipt Generator',
        
        // Navigation and interface
        'skip-to-content': 'Skip to main content',
        'loading': 'Loading...',
        'receipt-generator': 'Receipt Generator',
        'receipt-preview': 'Receipt Preview',
        'clear-form': 'Clear Form',
        'load-template': 'Load Template',
        'print-receipt': 'Print Receipt',
        'save-pdf': 'Save PDF',
        'preview-empty': 'Please fill out the form to view receipt preview',
        
        // Form labels
        'store-info': 'Store Information',
        'store-name': 'Store Name',
        'select-store': 'Please select store',
        'receipt-number': 'Receipt Number',
        'receipt-date': 'Receipt Date',
        'tax-rate': 'Tax Rate (%)',
        'generate': 'Generate',
        
        'customer-info': 'Customer Information',
        'customer-search': 'Search Customer',
        'search-customer-placeholder': 'Enter name or phone to search',
        'new-customer': 'New Customer',
        'customer-name': 'Customer Name',
        'customer-phone': 'Customer Phone',
        'customer-email': 'Customer Email',
        'customer-address': 'Customer Address',
        
        'receipt-items': 'Receipt Items',
        'item-category': 'Item Category',
        'select-category': 'Please select category',
        'item-name': 'Item Name',
        'item-price': 'Unit Price',
        'item-quantity': 'Quantity',
        'add-item': 'Add Item',
        'no-items': 'No items added yet',
        
        // Buttons and actions
        'save-draft': 'Save Draft',
        'generate-receipt': 'Generate Receipt',
        'edit': 'Edit',
        'delete': 'Delete',
        'cancel': 'Cancel',
        'confirm': 'Confirm',
        'save': 'Save',
        'close': 'Close',
        
        // Receipt content
        'receipt': 'Receipt',
        'invoice': 'Invoice',
        'bill-to': 'Bill To',
        'item': 'Item',
        'category': 'Category',
        'price': 'Price',
        'quantity': 'Qty',
        'total': 'Total',
        'subtotal': 'Subtotal',
        'tax': 'Tax',
        'grand-total': 'Grand Total',
        'notes': 'Notes',
        
        // Status
        'draft': 'Draft',
        'completed': 'Completed',
        'cancelled': 'Cancelled',
        
        // Validation messages
        'required-field': 'This field is required',
        'invalid-email': 'Please enter a valid email address',
        'invalid-phone': 'Please enter a valid phone number',
        'invalid-number': 'Please enter a valid number',
        'invalid-date': 'Please enter a valid date',
        'min-value': 'Value cannot be less than {min}',
        'max-value': 'Value cannot be greater than {max}',
        
        // Success messages
        'receipt-created': 'Receipt created successfully',
        'receipt-updated': 'Receipt updated successfully',
        'receipt-deleted': 'Receipt deleted successfully',
        'data-saved': 'Data saved successfully',
        
        // Error messages
        'error-occurred': 'An error occurred',
        'network-error': 'Network connection error',
        'server-error': 'Server error',
        'validation-error': 'Validation error',
        'not-found': 'Not found',
        'permission-denied': 'Permission denied',
        
        // Confirmation dialogs
        'confirm-delete': 'Are you sure you want to delete?',
        'confirm-clear': 'Are you sure you want to clear the form?',
        'unsaved-changes': 'You have unsaved changes. Are you sure you want to leave?',
        
        // Footer
        'footer-copyright': '© 2025 KMS PC Store. All rights reserved.',
        'footer-version': 'Version 1.0.0',

        // Category types
        'hardware': 'Hardware',
        'service': 'Service'
    }
};

// 國際化類
class I18n {
    constructor() {
        this.currentLanguage = this.detectLanguage();
        this.translations = translations;
        this.init();
    }

    /**
     * 檢測語言
     * Detect language
     */
    detectLanguage() {
        // 從本地存儲獲取
        const saved = Utils.storage.get('kms-language');
        if (saved && this.isValidLanguage(saved)) {
            return saved;
        }

        // 從瀏覽器語言檢測
        const browserLang = navigator.language || navigator.userLanguage;
        if (browserLang.startsWith('zh')) {
            return 'zh-TW';
        }
        
        return 'en-US';
    }

    /**
     * 檢查是否為有效語言
     * Check if valid language
     */
    isValidLanguage(lang) {
        return Object.keys(this.translations).includes(lang);
    }

    /**
     * 初始化
     * Initialize
     */
    init() {
        this.updatePageLanguage();
        this.bindEvents();
    }

    /**
     * 綁定事件
     * Bind events
     */
    bindEvents() {
        const languageToggle = Utils.dom.find('#language-toggle');
        if (languageToggle) {
            Utils.dom.on(languageToggle, 'click', () => {
                this.toggleLanguage();
            });
        }
    }

    /**
     * 切換語言
     * Toggle language
     */
    toggleLanguage() {
        const newLang = this.currentLanguage === 'zh-TW' ? 'en-US' : 'zh-TW';
        this.setLanguage(newLang);
    }

    /**
     * 設置語言
     * Set language
     */
    setLanguage(lang) {
        if (!this.isValidLanguage(lang)) {
            console.warn(`Invalid language: ${lang}`);
            return;
        }

        this.currentLanguage = lang;
        Utils.storage.set('kms-language', lang);
        this.updatePageLanguage();
        
        // 觸發語言變更事件
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: lang }
        }));
    }

    /**
     * 更新頁面語言
     * Update page language
     */
    updatePageLanguage() {
        // 更新HTML lang屬性
        document.documentElement.lang = this.currentLanguage;

        // 更新所有帶有data-i18n屬性的元素
        const elements = Utils.dom.findAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);
            if (translation) {
                element.textContent = translation;
            }
        });

        // 更新placeholder屬性
        const placeholderElements = Utils.dom.findAll('[data-i18n-placeholder]');
        placeholderElements.forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            const translation = this.t(key);
            if (translation) {
                element.placeholder = translation;
            }
        });

        // 更新title屬性
        const titleElements = Utils.dom.findAll('[data-i18n-title]');
        titleElements.forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            const translation = this.t(key);
            if (translation) {
                element.title = translation;
            }
        });

        // 更新語言切換按鈕文本
        const currentLanguageSpan = Utils.dom.find('#current-language');
        if (currentLanguageSpan) {
            currentLanguageSpan.textContent = this.currentLanguage === 'zh-TW' ? '中文' : 'English';
        }
    }

    /**
     * 翻譯文本
     * Translate text
     */
    t(key, params = {}) {
        const langTranslations = this.translations[this.currentLanguage] || this.translations['zh-TW'];
        let translation = langTranslations[key] || key;

        // 替換參數
        Object.keys(params).forEach(param => {
            translation = translation.replace(`{${param}}`, params[param]);
        });

        return translation;
    }

    /**
     * 獲取當前語言
     * Get current language
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * 獲取可用語言列表
     * Get available languages
     */
    getAvailableLanguages() {
        return Object.keys(this.translations);
    }
}

// 創建全局實例
window.i18n = new I18n();

// 全局翻譯函數
window.t = (key, params) => window.i18n.t(key, params);
