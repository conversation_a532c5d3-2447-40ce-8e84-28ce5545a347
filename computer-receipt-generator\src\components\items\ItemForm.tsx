import React, { useState, useEffect } from 'react';
import { Button, Input, Select } from '../ui';
import { PresetItemSelector } from './PresetItemSelector';
import { useReceipt } from '../../hooks/useReceipt';
import { useValidation } from '../../hooks/useValidation';
import type { ReceiptItem } from '../../types';
import type { PresetItem } from '../../data/presetItems';
import styles from './ItemForm.module.css';

interface ItemFormProps {
  editingItem?: ReceiptItem | null;
  onCancel?: () => void;
}

const categoryOptions = [
  { value: 'PC Case', label: 'PC Case' },
  { value: 'CPU', label: 'CPU' },
  { value: 'CPU Cooler', label: 'CPU Cooler' },
  { value: 'GPU', label: 'GPU' },
  { value: 'RAM', label: 'RAM' },
  { value: 'SSD', label: 'SSD' },
  { value: 'Motherboard', label: 'Motherboard' },
  { value: 'PSU', label: 'PSU' },
  { value: 'RGB', label: 'RGB' },
  { value: 'Other', label: 'Other' },
  { value: 'Labor Fee', label: 'Labor Fee' },
  { value: 'OS', label: 'OS' },
  { value: 'OC', label: 'OC' },
  { value: 'Package', label: 'Package' },
  { value: 'Other Services', label: 'Other Services' },
];

export const ItemForm: React.FC<ItemFormProps> = ({ editingItem, onCancel }) => {
  const { addItem, updateItem, stopEditing } = useReceipt();
  const { 
    validateItemForm, 
    getFieldError, 
    clearErrors 
  } = useValidation();

  const [formData, setFormData] = useState({
    name: '',
    category: 'PC Case' as ReceiptItem['category'],
    price: '',
    originalPrice: '',
    quantity: '1',
    hidePriceOnReceipt: false,
  });

  const isEditing = Boolean(editingItem);

  // 當編輯項目改變時更新表單資料
  useEffect(() => {
    if (editingItem) {
      setFormData({
        name: editingItem.name,
        category: editingItem.category,
        price: editingItem.price.toString(),
        originalPrice: editingItem.originalPrice?.toString() || '',
        quantity: editingItem.quantity.toString(),
        hidePriceOnReceipt: editingItem.hidePriceOnReceipt || false,
      });
    } else {
      setFormData({
        name: '',
        category: 'PC Case',
        price: '',
        originalPrice: '',
        quantity: '1',
        hidePriceOnReceipt: false,
      });
    }
    clearErrors();
  }, [editingItem, clearErrors]);

  const handleInputChange = (field: keyof typeof formData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // 處理預設項目選擇
  const handlePresetItemSelect = (presetItem: PresetItem) => {
    setFormData(prev => ({
      ...prev,
      name: presetItem.name,
      category: presetItem.category,
      price: presetItem.price.toString(),
      originalPrice: presetItem.originalPrice?.toString() || '',
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const isValid = validateItemForm({
      name: formData.name,
      price: formData.price,
      quantity: formData.quantity,
    });

    if (!isValid) {
      return;
    }

    const itemData = {
      name: formData.name.trim(),
      category: formData.category,
      price: parseFloat(formData.price),
      originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,
      quantity: parseInt(formData.quantity, 10),
      hidePriceOnReceipt: formData.hidePriceOnReceipt,
    };

    if (isEditing && editingItem) {
      updateItem(editingItem.id, itemData);
      stopEditing();
    } else {
      addItem(itemData);
      // 重置表單
      setFormData({
        name: '',
        category: 'PC Case',
        price: '',
        originalPrice: '',
        quantity: '1',
        hidePriceOnReceipt: false,
      });
      clearErrors();
    }
  };

  const handleCancel = () => {
    if (isEditing) {
      stopEditing();
    } else if (onCancel) {
      onCancel();
    }
    
    setFormData({
      name: '',
      category: 'PC Case',
      price: '',
      originalPrice: '',
      quantity: '1',
      hidePriceOnReceipt: false,
    });
    clearErrors();
  };

  return (
    <form onSubmit={handleSubmit} className={styles.form}>
      <div className={styles.formHeader}>
        <h3>{isEditing ? 'Edit Item' : 'Add Item'}</h3>
      </div>

      {/* 預設項目選擇器 - 只在新增模式顯示 */}
      {!isEditing && (
        <PresetItemSelector
          onSelectItem={handlePresetItemSelect}
          className={styles.presetSelector}
        />
      )}

      <div className={styles.formGrid}>
        <Input
          label="Item Name"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          error={getFieldError('itemName')}
          placeholder="Enter item name"
          required
          fullWidth
        />

        <Select
          label="Category"
          value={formData.category}
          onChange={(e) => handleInputChange('category', e.target.value as 'hardware' | 'service')}
          options={categoryOptions}
          fullWidth
        />

        <Input
          label="Price"
          type="number"
          value={formData.price}
          onChange={(e) => handleInputChange('price', e.target.value)}
          error={getFieldError('itemPrice')}
          placeholder="0.00"
          min="0"
          step="0.01"
          required
          fullWidth
        />

        <Input
          label="Original Price (Optional)"
          type="number"
          value={formData.originalPrice}
          onChange={(e) => handleInputChange('originalPrice', e.target.value)}
          placeholder="0.00"
          min="0"
          step="0.01"
          fullWidth
        />

        <Input
          label="Quantity"
          type="number"
          value={formData.quantity}
          onChange={(e) => handleInputChange('quantity', e.target.value)}
          error={getFieldError('itemQuantity')}
          placeholder="1"
          min="1"
          step="1"
          required
          fullWidth
        />
      </div>

      <div className={styles.checkboxGroup}>
        <label className={styles.checkboxLabel}>
          <input
            type="checkbox"
            checked={formData.hidePriceOnReceipt}
            onChange={(e) => handleInputChange('hidePriceOnReceipt', e.target.checked)}
            className={styles.checkbox}
          />
          <span className={styles.checkboxText}>
            Hide price on receipt (price will still be included in total)
          </span>
        </label>
      </div>

      <div className={styles.formActions}>
        <Button
          type="button"
          variant="secondary"
          onClick={handleCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
        >
          {isEditing ? 'Update' : 'Add'}
        </Button>
      </div>
    </form>
  );
};