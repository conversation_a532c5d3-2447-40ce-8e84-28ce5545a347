<?php
/**
 * Receipt Item Model
 * 收據項目模型類
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

// 防止直接訪問
if (!defined('KMS_RECEIPT_MAKER')) {
    die('Direct access not permitted');
}

require_once 'BaseModel.php';

/**
 * 收據項目模型類
 * Receipt Item Model Class
 */
class ReceiptItem extends BaseModel {
    protected $table = 'receipt_items';
    protected $timestamps = false; // 收據項目不需要 updated_at
    protected $fillable = [
        'receipt_id',
        'category_id',
        'name',
        'description',
        'price',
        'original_price',
        'quantity',
        'hide_price',
        'sort_order'
    ];

    /**
     * 根據收據ID獲取項目
     * Get items by receipt ID
     * 
     * @param int $receiptId 收據ID
     * @return array
     */
    public function getByReceiptId($receiptId) {
        $sql = "
            SELECT 
                ri.*,
                cat.name_zh as category_name_zh,
                cat.name_en as category_name_en,
                cat.type as category_type,
                (ri.price * ri.quantity) as line_total
            FROM receipt_items ri
            LEFT JOIN categories cat ON ri.category_id = cat.id
            WHERE ri.receipt_id = :receipt_id
            ORDER BY ri.sort_order ASC
        ";
        
        return $this->db->fetchAll($sql, ['receipt_id' => $receiptId]);
    }

    /**
     * 根據收據ID刪除所有項目
     * Delete all items by receipt ID
     * 
     * @param int $receiptId 收據ID
     * @return int 影響的行數
     */
    public function deleteByReceiptId($receiptId) {
        $sql = "DELETE FROM `{$this->table}` WHERE `receipt_id` = :receipt_id";
        return $this->db->delete($sql, ['receipt_id' => $receiptId]);
    }

    /**
     * 批量創建項目
     * Batch create items
     * 
     * @param array $items 項目數組
     * @return array 結果
     */
    public function batchCreate($items) {
        if (empty($items)) {
            return [
                'success' => false,
                'errors' => ['items' => '項目數組不能為空 / Items array cannot be empty']
            ];
        }

        $this->beginTransaction();

        try {
            $createdItems = [];
            
            foreach ($items as $index => $item) {
                // 驗證項目數據
                $errors = validateReceiptItemData($item);
                if (!empty($errors)) {
                    $this->rollback();
                    return [
                        'success' => false,
                        'errors' => ["item_{$index}" => $errors]
                    ];
                }

                $item['sort_order'] = $index + 1;
                $itemId = $this->create($item);
                $createdItems[] = $this->findById($itemId);
            }

            $this->commit();

            return [
                'success' => true,
                'data' => $createdItems,
                'message' => '項目批量創建成功 / Items batch created successfully'
            ];

        } catch (Exception $e) {
            $this->rollback();
            logMessage("Batch create items failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '批量創建項目失敗 / Failed to batch create items']
            ];
        }
    }

    /**
     * 更新項目排序
     * Update items sort order
     * 
     * @param array $itemIds 項目ID數組（按新順序排列）
     * @return array 結果
     */
    public function updateSortOrder($itemIds) {
        if (empty($itemIds)) {
            return [
                'success' => false,
                'errors' => ['items' => '項目ID數組不能為空 / Item IDs array cannot be empty']
            ];
        }

        $this->beginTransaction();

        try {
            foreach ($itemIds as $index => $itemId) {
                $this->update($itemId, ['sort_order' => $index + 1]);
            }

            $this->commit();

            return [
                'success' => true,
                'message' => '項目排序更新成功 / Items sort order updated successfully'
            ];

        } catch (Exception $e) {
            $this->rollback();
            logMessage("Update sort order failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '更新排序失敗 / Failed to update sort order']
            ];
        }
    }

    /**
     * 計算項目小計
     * Calculate item subtotal
     * 
     * @param float $price 單價
     * @param int $quantity 數量
     * @return float 小計
     */
    public function calculateSubtotal($price, $quantity) {
        return round($price * $quantity, 2);
    }

    /**
     * 獲取項目統計信息
     * Get item statistics
     * 
     * @param int $receiptId 收據ID
     * @return array
     */
    public function getItemStats($receiptId) {
        $sql = "
            SELECT 
                COUNT(*) as total_items,
                SUM(quantity) as total_quantity,
                SUM(price * quantity) as subtotal,
                AVG(price) as avg_price,
                MAX(price) as max_price,
                MIN(price) as min_price
            FROM receipt_items
            WHERE receipt_id = :receipt_id
        ";

        $result = $this->db->fetchRow($sql, ['receipt_id' => $receiptId]);
        
        if (!$result) {
            return [
                'total_items' => 0,
                'total_quantity' => 0,
                'subtotal' => 0,
                'avg_price' => 0,
                'max_price' => 0,
                'min_price' => 0
            ];
        }

        return $result;
    }

    /**
     * 根據分類獲取項目統計
     * Get item statistics by category
     * 
     * @param int $receiptId 收據ID
     * @return array
     */
    public function getItemStatsByCategory($receiptId) {
        $sql = "
            SELECT 
                cat.name_zh as category_name,
                cat.type as category_type,
                COUNT(ri.id) as item_count,
                SUM(ri.quantity) as total_quantity,
                SUM(ri.price * ri.quantity) as category_subtotal
            FROM receipt_items ri
            LEFT JOIN categories cat ON ri.category_id = cat.id
            WHERE ri.receipt_id = :receipt_id
            GROUP BY ri.category_id, cat.name_zh, cat.type
            ORDER BY category_subtotal DESC
        ";

        return $this->db->fetchAll($sql, ['receipt_id' => $receiptId]);
    }

    /**
     * 複製項目到另一個收據
     * Copy items to another receipt
     * 
     * @param int $sourceReceiptId 源收據ID
     * @param int $targetReceiptId 目標收據ID
     * @return array 結果
     */
    public function copyItemsToReceipt($sourceReceiptId, $targetReceiptId) {
        $sourceItems = $this->getByReceiptId($sourceReceiptId);
        
        if (empty($sourceItems)) {
            return [
                'success' => false,
                'errors' => ['source' => '源收據沒有項目 / Source receipt has no items']
            ];
        }

        $this->beginTransaction();

        try {
            foreach ($sourceItems as $item) {
                // 移除不需要的字段
                unset($item['id'], $item['created_at'], $item['category_name_zh'], $item['category_name_en'], $item['category_type'], $item['line_total']);
                
                // 設置新的收據ID
                $item['receipt_id'] = $targetReceiptId;
                
                $this->create($item);
            }

            $this->commit();

            return [
                'success' => true,
                'message' => '項目複製成功 / Items copied successfully'
            ];

        } catch (Exception $e) {
            $this->rollback();
            logMessage("Copy items failed: " . $e->getMessage(), 'ERROR');
            return [
                'success' => false,
                'errors' => ['general' => '複製項目失敗 / Failed to copy items']
            ];
        }
    }

    /**
     * 搜索項目
     * Search items
     * 
     * @param string $keyword 關鍵詞
     * @param array $filters 過濾條件
     * @param int $limit 限制數量
     * @return array
     */
    public function searchItems($keyword, $filters = [], $limit = 20) {
        $whereConditions = ["ri.name LIKE :keyword"];
        $params = ['keyword' => '%' . $keyword . '%'];

        if (!empty($filters['category_id'])) {
            $whereConditions[] = "ri.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }

        if (!empty($filters['price_min'])) {
            $whereConditions[] = "ri.price >= :price_min";
            $params['price_min'] = $filters['price_min'];
        }

        if (!empty($filters['price_max'])) {
            $whereConditions[] = "ri.price <= :price_max";
            $params['price_max'] = $filters['price_max'];
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "
            SELECT 
                ri.name,
                ri.price,
                cat.name_zh as category_name,
                COUNT(*) as usage_count,
                AVG(ri.price) as avg_price
            FROM receipt_items ri
            LEFT JOIN categories cat ON ri.category_id = cat.id
            WHERE {$whereClause}
            GROUP BY ri.name, cat.name_zh
            ORDER BY usage_count DESC, ri.name ASC
            LIMIT {$limit}
        ";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * 獲取熱門項目
     * Get popular items
     * 
     * @param int $limit 限制數量
     * @param int $days 天數範圍
     * @return array
     */
    public function getPopularItems($limit = 10, $days = 30) {
        $sql = "
            SELECT 
                ri.name,
                ri.price,
                cat.name_zh as category_name,
                COUNT(*) as order_count,
                SUM(ri.quantity) as total_quantity,
                SUM(ri.price * ri.quantity) as total_revenue
            FROM receipt_items ri
            LEFT JOIN categories cat ON ri.category_id = cat.id
            LEFT JOIN receipts r ON ri.receipt_id = r.id
            WHERE r.receipt_date >= CURDATE() - INTERVAL :days DAY
            AND r.status = 'completed'
            GROUP BY ri.name, ri.price, cat.name_zh
            ORDER BY order_count DESC, total_revenue DESC
            LIMIT {$limit}
        ";

        return $this->db->fetchAll($sql, ['days' => $days]);
    }
}
?>
