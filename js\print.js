/**
 * Print and PDF Functions
 * 打印和PDF功能
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

'use strict';

// 打印管理類
class PrintManager {
    constructor() {
        this.init();
    }

    /**
     * 初始化
     * Initialize
     */
    init() {
        this.setupPrintStyles();
        this.bindEvents();
    }

    /**
     * 設置打印樣式
     * Setup print styles
     */
    setupPrintStyles() {
        // 確保打印樣式已載入
        if (!document.querySelector('link[href*="print.css"]')) {
            const printStyleLink = Utils.dom.create('link', {
                rel: 'stylesheet',
                href: 'css/print.css',
                media: 'print'
            });
            document.head.appendChild(printStyleLink);
        }
    }

    /**
     * 綁定事件
     * Bind events
     */
    bindEvents() {
        // 監聽鍵盤快捷鍵
        Utils.dom.on(document, 'keydown', (e) => {
            // Ctrl+P 打印
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.printReceipt();
            }
        });

        // 監聽打印事件
        window.addEventListener('beforeprint', () => {
            this.beforePrint();
        });

        window.addEventListener('afterprint', () => {
            this.afterPrint();
        });
    }

    /**
     * 打印前處理
     * Before print handler
     */
    beforePrint() {
        // 隱藏不需要打印的元素
        const hideElements = Utils.dom.findAll('.print-hide');
        hideElements.forEach(el => {
            el.style.display = 'none';
        });

        // 顯示需要打印的元素
        const showElements = Utils.dom.findAll('.print-show');
        showElements.forEach(el => {
            el.style.display = 'block';
        });

        // 添加打印類
        document.body.classList.add('printing');
    }

    /**
     * 打印後處理
     * After print handler
     */
    afterPrint() {
        // 恢復元素顯示狀態
        const hideElements = Utils.dom.findAll('.print-hide');
        hideElements.forEach(el => {
            el.style.display = '';
        });

        const showElements = Utils.dom.findAll('.print-show');
        showElements.forEach(el => {
            el.style.display = '';
        });

        // 移除打印類
        document.body.classList.remove('printing');
    }

    /**
     * 打印收據
     * Print receipt
     */
    printReceipt() {
        const previewContainer = Utils.dom.find('#receipt-preview');
        if (!previewContainer || !previewContainer.innerHTML.trim()) {
            alert(t('preview-empty'));
            return;
        }

        // 檢查是否有收據內容
        const receiptDocument = previewContainer.querySelector('.receipt-document');
        if (!receiptDocument) {
            alert(t('preview-empty'));
            return;
        }

        // 使用瀏覽器打印功能
        window.print();
    }

    /**
     * 創建打印窗口
     * Create print window
     */
    createPrintWindow(content, title = 'Receipt') {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        if (!printWindow) {
            alert('Please allow popups to print the receipt');
            return null;
        }

        const printHtml = this.generatePrintHtml(content, title);
        printWindow.document.write(printHtml);
        printWindow.document.close();

        return printWindow;
    }

    /**
     * 生成打印HTML
     * Generate print HTML
     */
    generatePrintHtml(content, title) {
        return `
            <!DOCTYPE html>
            <html lang="${i18n.getCurrentLanguage()}">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${Utils.sanitizeHtml(title)}</title>
                <style>
                    ${this.getPrintStyles()}
                </style>
            </head>
            <body class="print-body">
                ${content}
                <script>
                    window.onload = function() {
                        setTimeout(function() {
                            window.print();
                            window.close();
                        }, 500);
                    };
                </script>
            </body>
            </html>
        `;
    }

    /**
     * 獲取打印樣式
     * Get print styles
     */
    getPrintStyles() {
        return `
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Times New Roman', serif;
                font-size: 12pt;
                line-height: 1.4;
                color: #000;
                background: #fff;
            }
            
            .receipt-document {
                width: 100%;
                max-width: none;
                margin: 0;
                padding: 0;
                background: #fff;
                color: #000;
            }
            
            .receipt-header {
                padding: 0 0 12pt 0;
                border-bottom: 2pt solid #000;
                page-break-inside: avoid;
            }
            
            .receipt-store-name {
                font-size: 18pt;
                font-weight: bold;
                margin-bottom: 6pt;
            }
            
            .receipt-table {
                width: 100%;
                border-collapse: collapse;
                font-size: 10pt;
            }
            
            .receipt-table th,
            .receipt-table td {
                padding: 4pt;
                border: 0.5pt solid #ccc;
                text-align: left;
            }
            
            .receipt-table th {
                background: #f0f0f0;
                font-weight: bold;
                border: 1pt solid #000;
            }
            
            .receipt-summary {
                padding: 12pt 0 0 0;
                border-top: 2pt solid #000;
                page-break-inside: avoid;
            }
            
            .receipt-summary-table {
                width: 100%;
                max-width: 200pt;
                margin-left: auto;
            }
            
            .receipt-summary-total {
                border-top: 2pt solid #000;
                background: #f0f0f0;
                font-weight: bold;
            }
            
            @page {
                size: A4;
                margin: 0.5in;
            }
        `;
    }

    /**
     * 保存為PDF（使用瀏覽器功能）
     * Save as PDF (using browser functionality)
     */
    savePdf() {
        // 提示用戶使用瀏覽器的打印功能保存為PDF
        if (confirm('請使用瀏覽器的打印功能，選擇"保存為PDF"選項。\n\nPlease use browser print function and select "Save as PDF" option.')) {
            this.printReceipt();
        }
    }

    /**
     * 檢查打印支持
     * Check print support
     */
    checkPrintSupport() {
        return typeof window.print === 'function';
    }

    /**
     * 獲取打印設置
     * Get print settings
     */
    getPrintSettings() {
        return {
            orientation: 'portrait',
            paperSize: 'A4',
            margins: {
                top: '0.5in',
                right: '0.5in',
                bottom: '0.5in',
                left: '0.5in'
            },
            scale: 100,
            printBackground: true
        };
    }

    /**
     * 應用打印設置
     * Apply print settings
     */
    applyPrintSettings(settings) {
        // 這裡可以設置CSS打印樣式
        const style = document.createElement('style');
        style.textContent = `
            @page {
                size: ${settings.paperSize};
                margin: ${settings.margins.top} ${settings.margins.right} ${settings.margins.bottom} ${settings.margins.left};
            }
            
            @media print {
                body {
                    transform: scale(${settings.scale / 100});
                    transform-origin: top left;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// PDF生成器類（使用html2pdf.js或jsPDF）
class PDFGenerator {
    constructor() {
        this.isLibraryLoaded = false;
        this.loadLibrary();
    }

    /**
     * 載入PDF庫
     * Load PDF library
     */
    async loadLibrary() {
        try {
            // 這裡可以動態載入PDF生成庫
            // 例如：html2pdf.js 或 jsPDF
            // 暫時標記為已載入
            this.isLibraryLoaded = true;
        } catch (error) {
            console.warn('PDF library not available:', error);
        }
    }

    /**
     * 生成PDF
     * Generate PDF
     */
    async generatePdf(element, options = {}) {
        if (!this.isLibraryLoaded) {
            throw new Error('PDF library not loaded');
        }

        const defaultOptions = {
            margin: 10,
            filename: 'receipt.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2 },
            jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        };

        const finalOptions = { ...defaultOptions, ...options };

        // 這裡應該使用實際的PDF生成庫
        // 例如：return html2pdf().set(finalOptions).from(element).save();
        
        // 暫時使用瀏覽器打印功能
        window.print();
    }

    /**
     * 檢查PDF支持
     * Check PDF support
     */
    isPdfSupported() {
        return this.isLibraryLoaded;
    }
}

// 創建全局實例
window.printManager = new PrintManager();
window.pdfGenerator = new PDFGenerator();

// 導出到全局作用域
window.Print = {
    printReceipt: () => window.printManager.printReceipt(),
    savePdf: () => window.printManager.savePdf(),
    isSupported: () => window.printManager.checkPrintSupport()
};
