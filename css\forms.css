/**
 * Form Styles
 * 表單樣式文件
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

/* Form Container */
.kms-receipt-form {
    display: flex;
    flex-direction: column;
    gap: var(--kms-spacing-6);
}

/* Fieldset Styles */
.form-fieldset {
    border: 1px solid var(--kms-border-color);
    border-radius: var(--kms-radius-lg);
    padding: var(--kms-spacing-6);
    margin: 0;
    background-color: var(--kms-bg-secondary);
}

.form-legend {
    font-size: var(--kms-font-size-lg);
    font-weight: var(--kms-font-weight-semibold);
    color: var(--kms-text-primary);
    padding: 0 var(--kms-spacing-3);
    margin-bottom: var(--kms-spacing-4);
    background-color: var(--kms-bg-primary);
    border-radius: var(--kms-radius-md);
}

/* Form Layout */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--kms-spacing-4);
    margin-bottom: var(--kms-spacing-4);
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-row.form-row-single {
    grid-template-columns: 1fr;
}

.form-row.form-row-triple {
    grid-template-columns: 1fr 1fr 1fr;
}

/* Form Group */
.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--kms-spacing-2);
}

.form-group.form-group-inline {
    flex-direction: row;
    align-items: center;
    gap: var(--kms-spacing-3);
}

.form-group.form-group-inline .form-label {
    margin-bottom: 0;
    white-space: nowrap;
}

/* Form Labels */
.form-label {
    display: block;
    font-size: var(--kms-font-size-sm);
    font-weight: var(--kms-font-weight-medium);
    color: var(--kms-text-primary);
    margin-bottom: var(--kms-spacing-2);
}

.form-label.form-label-required::after {
    content: ' *';
    color: var(--kms-color-error);
}

.form-label-optional {
    font-weight: var(--kms-font-weight-normal);
    color: var(--kms-text-secondary);
}

/* Form Inputs */
.form-input {
    display: block;
    width: 100%;
    padding: var(--kms-spacing-3) var(--kms-spacing-4);
    font-family: inherit;
    font-size: var(--kms-font-size-sm);
    line-height: var(--kms-line-height-normal);
    color: var(--kms-text-primary);
    background-color: var(--kms-bg-primary);
    border: 1px solid var(--kms-border-color);
    border-radius: var(--kms-radius-md);
    transition: border-color var(--kms-transition-fast), box-shadow var(--kms-transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--kms-color-primary);
    box-shadow: 0 0 0 3px var(--kms-color-primary-light);
}

.form-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: var(--kms-bg-tertiary);
}

.form-input::placeholder {
    color: var(--kms-text-muted);
}

.form-input.form-input-error {
    border-color: var(--kms-color-error);
}

.form-input.form-input-error:focus {
    border-color: var(--kms-color-error);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* Select Styles */
.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--kms-spacing-3) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: var(--kms-spacing-10);
}

/* Textarea Styles */
.form-textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

/* Input Groups */
.form-input-group {
    display: flex;
    gap: var(--kms-spacing-2);
}

.form-input-group .form-input {
    flex: 1;
}

.form-input-group .kms-btn {
    flex-shrink: 0;
}

/* Checkbox and Radio */
.form-checkbox,
.form-radio {
    display: flex;
    align-items: center;
    gap: var(--kms-spacing-2);
    cursor: pointer;
}

.form-checkbox input[type="checkbox"],
.form-radio input[type="radio"] {
    width: 16px;
    height: 16px;
    margin: 0;
    cursor: pointer;
}

.form-checkbox-label,
.form-radio-label {
    font-size: var(--kms-font-size-sm);
    color: var(--kms-text-primary);
    cursor: pointer;
}

/* Form Validation */
.form-validation-message {
    font-size: var(--kms-font-size-xs);
    color: var(--kms-color-error);
    margin-top: var(--kms-spacing-1);
    min-height: 1.2em;
}

.form-validation-message:empty {
    display: none;
}

.form-validation-success {
    color: var(--kms-color-success);
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--kms-spacing-3);
    padding-top: var(--kms-spacing-6);
    border-top: 1px solid var(--kms-border-color);
    margin-top: var(--kms-spacing-6);
}

.form-actions.form-actions-center {
    justify-content: center;
}

.form-actions.form-actions-between {
    justify-content: space-between;
}

/* Form Suggestions (Autocomplete) */
.form-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--kms-bg-primary);
    border: 1px solid var(--kms-border-color);
    border-top: none;
    border-radius: 0 0 var(--kms-radius-md) var(--kms-radius-md);
    box-shadow: var(--kms-shadow-lg);
    z-index: var(--kms-z-dropdown);
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.form-suggestions.form-suggestions-show {
    display: block;
}

.form-suggestion-item {
    padding: var(--kms-spacing-3) var(--kms-spacing-4);
    cursor: pointer;
    border-bottom: 1px solid var(--kms-border-color);
    transition: background-color var(--kms-transition-fast);
}

.form-suggestion-item:last-child {
    border-bottom: none;
}

.form-suggestion-item:hover,
.form-suggestion-item.form-suggestion-active {
    background-color: var(--kms-bg-tertiary);
}

.form-suggestion-name {
    font-weight: var(--kms-font-weight-medium);
    color: var(--kms-text-primary);
}

.form-suggestion-details {
    font-size: var(--kms-font-size-xs);
    color: var(--kms-text-secondary);
    margin-top: var(--kms-spacing-1);
}

/* Add Item Form */
.form-add-item {
    background-color: var(--kms-bg-primary);
    border: 1px solid var(--kms-border-color);
    border-radius: var(--kms-radius-md);
    padding: var(--kms-spacing-4);
    margin-bottom: var(--kms-spacing-4);
}

/* Items List */
.form-items-list {
    border: 1px solid var(--kms-border-color);
    border-radius: var(--kms-radius-md);
    overflow: hidden;
}

.form-items-empty {
    padding: var(--kms-spacing-8);
    text-align: center;
    color: var(--kms-text-muted);
    font-style: italic;
}

.form-item {
    display: grid;
    grid-template-columns: 1fr auto auto auto auto;
    align-items: center;
    gap: var(--kms-spacing-3);
    padding: var(--kms-spacing-4);
    border-bottom: 1px solid var(--kms-border-color);
    background-color: var(--kms-bg-primary);
    transition: background-color var(--kms-transition-fast);
}

.form-item:last-child {
    border-bottom: none;
}

.form-item:hover {
    background-color: var(--kms-bg-secondary);
}

.form-item-info {
    display: flex;
    flex-direction: column;
    gap: var(--kms-spacing-1);
}

.form-item-name {
    font-weight: var(--kms-font-weight-medium);
    color: var(--kms-text-primary);
}

.form-item-category {
    font-size: var(--kms-font-size-xs);
    color: var(--kms-text-secondary);
}

.form-item-price,
.form-item-quantity,
.form-item-total {
    font-size: var(--kms-font-size-sm);
    font-weight: var(--kms-font-weight-medium);
    color: var(--kms-text-primary);
    text-align: right;
}

.form-item-actions {
    display: flex;
    gap: var(--kms-spacing-1);
}

/* Form Summary */
.form-summary {
    background-color: var(--kms-bg-tertiary);
    border-radius: var(--kms-radius-md);
    padding: var(--kms-spacing-4);
    margin-top: var(--kms-spacing-4);
}

.form-summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--kms-spacing-2) 0;
}

.form-summary-row:not(:last-child) {
    border-bottom: 1px solid var(--kms-border-color);
}

.form-summary-label {
    font-size: var(--kms-font-size-sm);
    color: var(--kms-text-secondary);
}

.form-summary-value {
    font-size: var(--kms-font-size-sm);
    font-weight: var(--kms-font-weight-medium);
    color: var(--kms-text-primary);
}

.form-summary-total .form-summary-label,
.form-summary-total .form-summary-value {
    font-size: var(--kms-font-size-lg);
    font-weight: var(--kms-font-weight-bold);
    color: var(--kms-color-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--kms-spacing-3);
    }
    
    .form-row.form-row-triple {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
        gap: var(--kms-spacing-2);
    }
    
    .form-item {
        grid-template-columns: 1fr;
        gap: var(--kms-spacing-2);
        text-align: left;
    }
    
    .form-item-price,
    .form-item-quantity,
    .form-item-total {
        text-align: left;
    }
}
