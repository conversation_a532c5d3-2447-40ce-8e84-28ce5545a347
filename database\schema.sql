-- KMS PC Receipt Maker Database Schema
-- 數據庫架構文件
-- Created: 2025-01-14

-- 設置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 創建數據庫（如果不存在）
CREATE DATABASE IF NOT EXISTS `kms_receipt_maker` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE `kms_receipt_maker`;

-- 商店表
-- Stores table
DROP TABLE IF EXISTS `stores`;
CREATE TABLE `stores` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '商店名稱',
  `address` text NOT NULL COMMENT '商店地址',
  `phone` varchar(50) NOT NULL COMMENT '電話號碼',
  `email` varchar(255) NOT NULL COMMENT '郵箱地址',
  `logo_url` varchar(500) DEFAULT NULL COMMENT 'Logo URL',
  `tax_id` varchar(50) DEFAULT NULL COMMENT '統一編號',
  `website` varchar(255) DEFAULT NULL COMMENT '網站',
  `description` text DEFAULT NULL COMMENT '描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否啟用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '創建時間',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stores_email` (`email`),
  KEY `idx_stores_active` (`is_active`),
  KEY `idx_stores_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商店信息表';

-- 客戶表
-- Customers table
DROP TABLE IF EXISTS `customers`;
CREATE TABLE `customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '客戶姓名',
  `phone` varchar(50) NOT NULL COMMENT '電話號碼',
  `email` varchar(255) DEFAULT NULL COMMENT '郵箱地址',
  `address` text DEFAULT NULL COMMENT '地址',
  `company` varchar(255) DEFAULT NULL COMMENT '公司名稱',
  `tax_id` varchar(50) DEFAULT NULL COMMENT '統一編號',
  `notes` text DEFAULT NULL COMMENT '備註',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否啟用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '創建時間',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customers_phone` (`phone`),
  UNIQUE KEY `uk_customers_email` (`email`),
  KEY `idx_customers_active` (`is_active`),
  KEY `idx_customers_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客戶信息表';

-- 分類表
-- Categories table
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name_en` varchar(255) NOT NULL COMMENT '英文名稱',
  `name_zh` varchar(255) NOT NULL COMMENT '中文名稱',
  `type` enum('hardware','service') NOT NULL COMMENT '類型：hardware=硬件，service=服務',
  `description_en` text DEFAULT NULL COMMENT '英文描述',
  `description_zh` text DEFAULT NULL COMMENT '中文描述',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序順序',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否啟用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '創建時間',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  PRIMARY KEY (`id`),
  KEY `idx_categories_type` (`type`),
  KEY `idx_categories_active` (`is_active`),
  KEY `idx_categories_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='項目分類表';

-- 收據表
-- Receipts table
DROP TABLE IF EXISTS `receipts`;
CREATE TABLE `receipts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `receipt_number` varchar(100) NOT NULL COMMENT '收據編號',
  `store_id` int(11) NOT NULL COMMENT '商店ID',
  `customer_id` int(11) DEFAULT NULL COMMENT '客戶ID（可為空，支持臨時客戶）',
  `customer_name` varchar(255) NOT NULL COMMENT '客戶姓名',
  `customer_phone` varchar(50) NOT NULL COMMENT '客戶電話',
  `customer_email` varchar(255) DEFAULT NULL COMMENT '客戶郵箱',
  `customer_address` text DEFAULT NULL COMMENT '客戶地址',
  `receipt_date` date NOT NULL COMMENT '收據日期',
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 5.00 COMMENT '稅率（百分比）',
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '小計',
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '稅額',
  `total` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '總計',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '折扣金額',
  `notes` text DEFAULT NULL COMMENT '備註',
  `status` enum('draft','completed','cancelled') NOT NULL DEFAULT 'draft' COMMENT '狀態',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '創建時間',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_receipts_number` (`receipt_number`),
  KEY `idx_receipts_store` (`store_id`),
  KEY `idx_receipts_customer` (`customer_id`),
  KEY `idx_receipts_date` (`receipt_date`),
  KEY `idx_receipts_status` (`status`),
  CONSTRAINT `fk_receipts_store` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_receipts_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收據主表';

-- 收據項目表
-- Receipt items table
DROP TABLE IF EXISTS `receipt_items`;
CREATE TABLE `receipt_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `receipt_id` int(11) NOT NULL COMMENT '收據ID',
  `category_id` int(11) DEFAULT NULL COMMENT '分類ID',
  `name` varchar(255) NOT NULL COMMENT '項目名稱',
  `description` text DEFAULT NULL COMMENT '項目描述',
  `price` decimal(10,2) NOT NULL COMMENT '單價',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原價（用於折扣顯示）',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '數量',
  `hide_price` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否隱藏價格',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序順序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '創建時間',
  PRIMARY KEY (`id`),
  KEY `idx_receipt_items_receipt` (`receipt_id`),
  KEY `idx_receipt_items_category` (`category_id`),
  KEY `idx_receipt_items_sort` (`sort_order`),
  CONSTRAINT `fk_receipt_items_receipt` FOREIGN KEY (`receipt_id`) REFERENCES `receipts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_receipt_items_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收據項目明細表';

-- 插入默認數據
-- Insert default data

-- 默認商店
INSERT INTO `stores` (`name`, `address`, `phone`, `email`, `description`) VALUES
('KMS PC Store', '123 Tech Street, Taipei City, Taiwan 10001', '+886-2-1234-5678', '<EMAIL>', 'Professional computer hardware store');

-- 默認分類
INSERT INTO `categories` (`name_en`, `name_zh`, `type`, `description_en`, `description_zh`, `sort_order`) VALUES
('CPU', 'CPU處理器', 'hardware', 'Central Processing Unit', '中央處理器', 1),
('Memory', '記憶體', 'hardware', 'RAM Memory', '隨機存取記憶體', 2),
('Storage', '儲存裝置', 'hardware', 'Hard Drive / SSD', '硬碟 / 固態硬碟', 3),
('Graphics Card', '顯示卡', 'hardware', 'Graphics Processing Unit', '圖形處理器', 4),
('Motherboard', '主機板', 'hardware', 'Motherboard', '主機板', 5),
('Power Supply', '電源供應器', 'hardware', 'Power Supply Unit', '電源供應器', 6),
('Case', '機殼', 'hardware', 'Computer Case', '電腦機殼', 7),
('Cooling', '散熱器', 'hardware', 'CPU Cooler / Case Fan', 'CPU散熱器 / 機殼風扇', 8),
('Peripherals', '周邊設備', 'hardware', 'Keyboard, Mouse, Monitor', '鍵盤、滑鼠、螢幕', 9),
('Installation', '安裝服務', 'service', 'Hardware Installation Service', '硬體安裝服務', 10),
('Maintenance', '維護服務', 'service', 'Computer Maintenance Service', '電腦維護服務', 11),
('Repair', '維修服務', 'service', 'Computer Repair Service', '電腦維修服務', 12),
('Consultation', '諮詢服務', 'service', 'Technical Consultation', '技術諮詢', 13);

-- 默認客戶（測試用）
INSERT INTO `customers` (`name`, `phone`, `email`, `address`, `company`) VALUES
('張小明', '0912-345-678', '<EMAIL>', '台北市信義區信義路五段7號', '科技公司'),
('李小華', '0987-654-321', '<EMAIL>', '台北市大安區敦化南路二段216號', '設計工作室');

-- 設置外鍵檢查
SET FOREIGN_KEY_CHECKS = 1;

-- 創建索引以提高性能
-- Create indexes for better performance

-- 收據相關索引
CREATE INDEX `idx_receipts_customer_phone` ON `receipts` (`customer_phone`);
CREATE INDEX `idx_receipts_created_at` ON `receipts` (`created_at`);

-- 收據項目相關索引
CREATE INDEX `idx_receipt_items_name` ON `receipt_items` (`name`);
CREATE INDEX `idx_receipt_items_price` ON `receipt_items` (`price`);

-- 客戶相關索引
CREATE INDEX `idx_customers_company` ON `customers` (`company`);

-- 分類相關索引
CREATE INDEX `idx_categories_name_zh` ON `categories` (`name_zh`);
CREATE INDEX `idx_categories_name_en` ON `categories` (`name_en`);

-- 完成
SELECT 'Database schema created successfully!' as message;
