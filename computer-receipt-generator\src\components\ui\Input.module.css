/* 容器樣式 */
.container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.fullWidth {
  width: 100%;
}

/* 標籤樣式 */
.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

/* 輸入框包裝器 */
.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

/* 輸入框樣式 */
.input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  line-height: 1.5;
  color: #111827;
  background-color: #ffffff;
  transition: all 0.2s ease-in-out;
}

.input:focus {
  outline: none;
  border-color: #646cff;
  box-shadow: 0 0 0 3px rgba(100, 108, 255, 0.1);
}

.input:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.input::placeholder {
  color: #9ca3af;
}

/* 錯誤狀態 */
.error {
  border-color: #dc3545;
}

.error:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* 圖示樣式 */
.hasStartIcon {
  padding-left: 2.5rem;
}

.hasEndIcon {
  padding-right: 2.5rem;
}

.startIcon,
.endIcon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  color: #6b7280;
  pointer-events: none;
}

.startIcon {
  left: 0.75rem;
}

.endIcon {
  right: 0.75rem;
}

/* 輔助文字樣式 */
.helperText {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.errorText {
  font-size: 0.75rem;
  color: #dc3545;
  margin-top: 0.25rem;
}