<?php
/**
 * Customers API
 * 客戶API接口
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

require_once 'init.php';

$customerModel = new Customer();

// 記錄API請求
logApiRequest('/customers', $_SERVER['REQUEST_METHOD'], getRequestData());

// 路由定義
$router->get('/customers', function() use ($customerModel) {
    $data = getCleanRequestData();
    $pagination = handlePagination($data);
    
    // 搜索功能
    if (!empty($data['search'])) {
        $customers = $customerModel->searchCustomers($data['search'], $pagination['per_page']);
        sendSuccess($customers, 'Customers retrieved successfully');
        return;
    }
    
    // 獲取所有客戶（分頁）
    $result = $customerModel->paginate($pagination['page'], $pagination['per_page'], ['is_active' => 1], 'name ASC');
    sendSuccess($result, 'Customers retrieved successfully');
});

$router->get('/customers/{id}', function($id) use ($customerModel) {
    $id = validateNumericParam($id, 'id');
    
    $customer = $customerModel->findById($id);
    if (!$customer) {
        sendError('Customer not found', 404);
    }
    
    // 獲取客戶統計信息
    $stats = $customerModel->getCustomerStats($id);
    $customer['stats'] = $stats;
    
    sendSuccess($customer, 'Customer retrieved successfully');
});

$router->post('/customers', function() use ($customerModel) {
    $data = getCleanRequestData();
    
    // 驗證必需參數
    if (!validateRequiredParams($data, ['name', 'phone'])) {
        return;
    }
    
    $result = $customerModel->createCustomer($data);
    
    if ($result['success']) {
        sendSuccess($result['data'], $result['message']);
    } else {
        sendError('Failed to create customer', 400, $result['errors']);
    }
});

$router->put('/customers/{id}', function($id) use ($customerModel) {
    $id = validateNumericParam($id, 'id');
    $data = getCleanRequestData();
    
    // 驗證必需參數
    if (!validateRequiredParams($data, ['name', 'phone'])) {
        return;
    }
    
    $result = $customerModel->updateCustomer($id, $data);
    
    if ($result['success']) {
        sendSuccess($result['data'], $result['message']);
    } else {
        sendError('Failed to update customer', 400, $result['errors']);
    }
});

$router->delete('/customers/{id}', function($id) use ($customerModel) {
    $id = validateNumericParam($id, 'id');
    
    $result = $customerModel->deleteCustomer($id);
    
    if ($result['success']) {
        sendSuccess(null, $result['message']);
    } else {
        sendError('Failed to delete customer', 400, $result['errors']);
    }
});

// 獲取客戶的收據列表
$router->get('/customers/{id}/receipts', function($id) use ($customerModel) {
    $id = validateNumericParam($id, 'id');
    $data = getCleanRequestData();
    $pagination = handlePagination($data);
    
    // 檢查客戶是否存在
    $customer = $customerModel->findById($id);
    if (!$customer) {
        sendError('Customer not found', 404);
    }
    
    $result = $customerModel->getCustomerReceipts($id, $pagination['page'], $pagination['per_page']);
    sendSuccess($result, 'Customer receipts retrieved successfully');
});

// 獲取客戶統計信息
$router->get('/customers/{id}/stats', function($id) use ($customerModel) {
    $id = validateNumericParam($id, 'id');
    
    // 檢查客戶是否存在
    $customer = $customerModel->findById($id);
    if (!$customer) {
        sendError('Customer not found', 404);
    }
    
    $stats = $customerModel->getCustomerStats($id);
    sendSuccess($stats, 'Customer statistics retrieved successfully');
});

// 檢查電話是否可用
$router->post('/customers/check-phone', function() use ($customerModel) {
    $data = getCleanRequestData();
    
    if (!validateRequiredParams($data, ['phone'])) {
        return;
    }
    
    $excludeId = isset($data['exclude_id']) ? (int)$data['exclude_id'] : null;
    $exists = $customerModel->phoneExists($data['phone'], $excludeId);
    
    sendSuccess([
        'available' => !$exists,
        'message' => $exists ? 
            '此電話號碼已被使用 / This phone number is already in use' : 
            '此電話號碼可以使用 / This phone number is available'
    ], 'Phone availability checked');
});

// 批量操作
$router->post('/customers/batch', function() use ($customerModel) {
    $data = getCleanRequestData();
    
    if (!validateRequiredParams($data, ['action', 'ids'])) {
        return;
    }
    
    $action = $data['action'];
    $ids = $data['ids'];
    
    if (!is_array($ids) || empty($ids)) {
        sendError('IDs must be a non-empty array', 400);
    }
    
    $results = [];
    $errors = [];
    
    switch ($action) {
        case 'delete':
            foreach ($ids as $id) {
                $result = $customerModel->deleteCustomer($id);
                if ($result['success']) {
                    $results[] = $id;
                } else {
                    $errors[$id] = $result['errors'];
                }
            }
            break;
            
        case 'activate':
            foreach ($ids as $id) {
                $result = $customerModel->update($id, ['is_active' => 1]);
                if ($result > 0) {
                    $results[] = $id;
                } else {
                    $errors[$id] = 'Failed to activate customer';
                }
            }
            break;
            
        case 'deactivate':
            foreach ($ids as $id) {
                $result = $customerModel->update($id, ['is_active' => 0]);
                if ($result > 0) {
                    $results[] = $id;
                } else {
                    $errors[$id] = 'Failed to deactivate customer';
                }
            }
            break;
            
        default:
            sendError('Invalid action', 400);
            return;
    }
    
    $response = [
        'processed' => $results,
        'errors' => $errors,
        'total_processed' => count($results),
        'total_errors' => count($errors)
    ];
    
    if (empty($errors)) {
        sendSuccess($response, 'Batch operation completed successfully');
    } else {
        sendSuccess($response, 'Batch operation completed with some errors');
    }
});

// 執行路由
$router->run();
?>
