.tableContainer {
  margin: 1.5rem 0;
  overflow-x: auto;
}

.itemTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
  background-color: #fff;
  border: 1px solid #ddd;
}

.headerRow {
  background-color: #f8f9fa;
  border-bottom: 2px solid #000000;
}

.headerRow th {
  padding: 0.75rem 0.5rem;
  text-align: left;
  font-weight: 600;
  color: #000000;
  border-right: 1px solid #ddd;
}

.headerRow th:last-child {
  border-right: none;
}

.nameHeader {
  width: 35%;
  min-width: 150px;
}

.categoryHeader {
  width: 15%;
  min-width: 80px;
  text-align: center;
}

.quantityHeader {
  width: 10%;
  min-width: 60px;
  text-align: center;
}

.priceHeader {
  width: 20%;
  min-width: 100px;
  text-align: right;
}

.subtotalHeader {
  width: 20%;
  min-width: 100px;
  text-align: right;
}

.itemRow {
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;
}

.itemRow:hover {
  background-color: #f8f9fa;
}

.evenRow {
  background-color: #fff;
}

.oddRow {
  background-color: #fafafa;
}

.itemRow td {
  padding: 0.75rem 0.5rem;
  vertical-align: middle;
  border-right: 1px solid #eee;
}

.itemRow td:last-child {
  border-right: none;
}

.nameCell {
  font-weight: 500;
}

.itemName {
  color: #000000;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.discountBadge {
  background: #dc2626;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  white-space: nowrap;
}

.priceContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.currentPrice {
  font-weight: 600;
  color: #059669;
}

.originalPrice {
  font-size: 0.8rem;
  color: #9ca3af;
  text-decoration: line-through;
  font-weight: 400;
}

.categoryCell {
  text-align: center;
}

.categoryBadge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.categoryhardware {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.categoryservice {
  background-color: #f3e5f5;
  color: #7b1fa2;
  border: 1px solid #e1bee7;
}

.quantityCell {
  text-align: center;
}

.quantity {
  font-weight: 500;
  color: #000000;
}

.priceCell {
  text-align: right;
}

.price {
  font-weight: 500;
  color: #000000;
}

.subtotalCell {
  text-align: right;
}

.subtotal {
  font-weight: 600;
  color: #2e7d32;
}

.emptyState {
  text-align: center;
  padding: 3rem 1rem;
  background-color: #f8f9fa;
  border: 2px dashed #ddd;
  border-radius: 8px;
  margin: 1.5rem 0;
}

.emptyMessage {
  color: #666;
  font-size: 1rem;
  margin: 0;
  font-style: italic;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .itemTable {
    font-size: 0.8rem;
  }
  
  .headerRow th,
  .itemRow td {
    padding: 0.5rem 0.25rem;
  }
  
  .nameHeader {
    width: 40%;
    min-width: 120px;
  }
  
  .categoryHeader {
    width: 20%;
    min-width: 70px;
  }
  
  .quantityHeader {
    width: 15%;
    min-width: 50px;
  }
  
  .priceHeader,
  .subtotalHeader {
    width: 25%;
    min-width: 80px;
  }
  
  .categoryBadge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 480px) {
  .tableContainer {
    margin: 1rem -0.5rem;
  }
  
  .itemTable {
    font-size: 0.75rem;
  }
  
  .headerRow th,
  .itemRow td {
    padding: 0.4rem 0.2rem;
  }
  
  .itemName {
    font-size: 0.8rem;
    line-height: 1.3;
  }
}

/* 列印樣式 */
@media print {
  .tableContainer {
    margin: 12pt 0;
    page-break-inside: avoid;
  }
  
  .itemTable {
    font-size: 10pt;
    border: 1px solid #000;
  }
  
  .headerRow {
    background-color: #f0f0f0 !important;
    border-bottom: 2px solid #000;
  }
  
  .headerRow th {
    border-right: 1px solid #000;
    font-size: 10pt;
    font-weight: bold;
  }
  
  .itemRow {
    border-bottom: 1px solid #000;
  }
  
  .itemRow:hover {
    background-color: transparent;
  }
  
  .evenRow,
  .oddRow {
    background-color: transparent;
  }
  
  .itemRow td {
    border-right: 1px solid #000;
    font-size: 9pt;
  }
  
  .categoryBadge {
    background-color: transparent !important;
    border: 1px solid #000 !important;
    color: #000 !important;
  }
  
  .emptyState {
    background-color: transparent;
    border: 1px dashed #000;
  }
}