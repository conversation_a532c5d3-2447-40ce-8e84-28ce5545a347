.container {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.titleSection {
  flex: 1;
}

.title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.toggleButton {
  flex-shrink: 0;
  margin-left: 1rem;
}

.content {
  padding: 1.5rem;
  animation: slideDown 0.3s ease-out;
}

.saveSection {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.templatesSection {
  /* No additional styles needed */
}

.sectionTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.saveForm {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
  margin-bottom: 0.5rem;
}

.templateNameInput {
  flex: 1;
  min-width: 200px;
}

.saveButton {
  flex-shrink: 0;
}

.saveHint {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.emptyState {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.emptyIcon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.emptyText {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.emptyHint {
  font-size: 0.8rem;
  color: #9ca3af;
}

.templatesList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.templateCard {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.templateCard:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.templateHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.templateInfo {
  flex: 1;
}

.templateName {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.templateName:hover {
  background-color: #f3f4f6;
}

.templateNameEdit {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.templateNameInput {
  font-size: 0.9rem;
  font-weight: 600;
}

.templateNameActions {
  display: flex;
  gap: 0.5rem;
}

.templateMeta {
  font-size: 0.8rem;
  color: #6b7280;
}

.templateActions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.loadButton {
  /* Inherits from Button component */
}

.deleteButton {
  /* Inherits from Button component */
}

.templateItems {
  border-top: 1px solid #e5e7eb;
  padding-top: 0.75rem;
}

.templateItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
  font-size: 0.8rem;
}

.itemName {
  color: #374151;
  flex: 1;
  margin-right: 0.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.itemPrice {
  color: #059669;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.originalPrice {
  color: #9ca3af;
  text-decoration: line-through;
  font-weight: 400;
  font-size: 0.75rem;
}

.moreItems {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
  padding: 0.25rem 0;
  text-align: center;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .toggleButton {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .saveForm {
    flex-direction: column;
    align-items: stretch;
  }
  
  .templateNameInput {
    min-width: auto;
  }
  
  .templateHeader {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .templateActions {
    align-self: flex-start;
  }
}

/* 動畫效果 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 載入狀態 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 焦點狀態 */
.templateCard:focus-within {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* 高對比模式支援 */
@media (prefers-contrast: high) {
  .templateCard {
    border-width: 2px;
  }
  
  .templateCard:hover {
    border-width: 3px;
  }
}
