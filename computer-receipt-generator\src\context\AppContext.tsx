import React, { createContext, useContext, useReducer } from 'react';
import type { ReactNode } from 'react';
import type { AppState, AppAction, ReceiptData } from '../types';
import { generateReceiptNumber } from '../utils/calculations';

// 初始狀態
const initialReceiptData: ReceiptData = {
  receiptNumber: generateReceiptNumber(),
  date: null as Date | null,
  logoSettings: {
    imageUrl: null,
    position: { x: 10, y: 10 },
    size: { width: 20, height: 15 },
    opacity: 1,
  },
  storeInfo: {
    name: '',
    address: '',
    phone: '',
    email: '',
  },
  customerInfo: {
    name: '',
    phone: '',
    email: '',
    address: '',
  },
  items: [],
  taxRate: 0, // 預設 0% 稅率
  subtotal: 0,
  taxAmount: 0,
  total: 0,
};

const initialState: AppState = {
  receiptData: initialReceiptData,
  isEditing: false,
  editingItemId: null,
};

// Reducer 函數
export const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'ADD_ITEM':
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          items: [...state.receiptData.items, action.payload],
        },
      };

    case 'UPDATE_ITEM':
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          items: state.receiptData.items.map(item =>
            item.id === action.payload.id
              ? { ...item, ...action.payload.item }
              : item
          ),
        },
      };

    case 'DELETE_ITEM':
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          items: state.receiptData.items.filter(item => item.id !== action.payload),
        },
      };

    case 'MOVE_ITEM_UP': {
      const items = [...state.receiptData.items];
      const index = items.findIndex(item => item.id === action.payload);
      if (index > 0) {
        [items[index], items[index - 1]] = [items[index - 1], items[index]];
      }
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          items,
        },
      };
    }

    case 'MOVE_ITEM_DOWN': {
      const items = [...state.receiptData.items];
      const index = items.findIndex(item => item.id === action.payload);
      if (index < items.length - 1) {
        [items[index], items[index + 1]] = [items[index + 1], items[index]];
      }
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          items,
        },
      };
    }

    case 'UPDATE_STORE_INFO':
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          storeInfo: {
            ...state.receiptData.storeInfo,
            ...action.payload,
          },
        },
      };

    case 'UPDATE_CUSTOMER_INFO':
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          customerInfo: {
            ...state.receiptData.customerInfo,
            ...action.payload,
          },
        },
      };

    case 'SET_TAX_RATE':
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          taxRate: action.payload,
        },
      };

    case 'SET_RECEIPT_DATE':
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          date: action.payload,
        },
      };

    case 'SET_RECEIPT_NUMBER':
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          receiptNumber: action.payload,
        },
      };

    case 'UPDATE_LOGO_SETTINGS':
      return {
        ...state,
        receiptData: {
          ...state.receiptData,
          logoSettings: action.payload,
        },
      };

    case 'SET_EDITING':
      return {
        ...state,
        isEditing: action.payload.isEditing,
        editingItemId: action.payload.itemId || null,
      };

    default:
      return state;
  }
};

// Context 類型定義
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
}

// 建立 Context
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider 組件
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

// 自訂 Hook 來使用 Context
export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};