import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../../App';

// Mock html2pdf to avoid issues in tests
vi.mock('html2pdf.js', () => ({
  default: () => ({
    set: () => ({
      from: () => ({
        save: vi.fn().mockResolvedValue(undefined)
      })
    })
  })
}));

describe('Receipt Generation Flow - End to End', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
  });

  const clickAddItemButton = async () => {
    const addItemButtons = screen.getAllByRole('button', { name: /新增項目/i });
    await user.click(addItemButtons[0]);
  };

  it('completes the full receipt creation workflow', async () => {
    render(<App />);

    // 步驟 1: 驗證初始狀態
    expect(screen.getByText('電腦收據生成器')).toBeInTheDocument();
    expect(screen.getByText('請先新增項目來查看收據預覽')).toBeInTheDocument();
    
    // 驗證按鈕初始為禁用狀態
    const printButton = screen.getByRole('button', { name: /列印收據/i });
    const pdfButton = screen.getByRole('button', { name: /儲存 PDF/i });
    expect(printButton).toBeDisabled();
    expect(pdfButton).toBeDisabled();

    // 步驟 2: 填寫商店資訊
    const storeNameInput = screen.getByLabelText(/商店名稱/i);
    const storeAddressInput = screen.getByLabelText(/商店地址/i);
    const storePhoneInput = screen.getByLabelText(/商店電話/i);
    const storeEmailInput = screen.getByLabelText(/商店電子郵件/i);

    await user.type(storeNameInput, '科技電腦專賣店');
    await user.type(storeAddressInput, '台北市信義區信義路五段7號');
    await user.type(storePhoneInput, '02-2345-6789');
    await user.type(storeEmailInput, '<EMAIL>');

    // 步驟 3: 填寫客戶資訊
    const customerNameInput = screen.getByLabelText(/客戶姓名/i);
    const customerPhoneInput = screen.getByLabelText(/客戶電話/i);
    const customerEmailInput = screen.getByLabelText(/客戶電子郵件/i);

    await user.type(customerNameInput, '王小明');
    await user.type(customerPhoneInput, '0912-345-678');
    await user.type(customerEmailInput, '<EMAIL>');

    // 步驟 4: 新增第一個項目 (硬體)
    await clickAddItemButton();

    // 填寫項目資訊
    const itemNameInput = screen.getByLabelText(/項目名稱/i);
    const itemCategorySelect = screen.getByLabelText(/項目類別/i);
    const itemPriceInput = screen.getByLabelText(/單價/i);
    const itemQuantityInput = screen.getByLabelText(/數量/i);

    await user.type(itemNameInput, 'Intel Core i7-13700K');
    await user.selectOptions(itemCategorySelect, 'hardware');
    await user.type(itemPriceInput, '12500');
    await user.type(itemQuantityInput, '1');

    // 儲存項目
    const saveItemButton = screen.getByRole('button', { name: /儲存項目/i });
    await user.click(saveItemButton);

    // 步驟 5: 新增第二個項目 (服務)
    await clickAddItemButton();

    const itemNameInput2 = screen.getByLabelText(/項目名稱/i);
    const itemCategorySelect2 = screen.getByLabelText(/項目類別/i);
    const itemPriceInput2 = screen.getByLabelText(/單價/i);
    const itemQuantityInput2 = screen.getByLabelText(/數量/i);

    await user.type(itemNameInput2, '系統組裝服務');
    await user.selectOptions(itemCategorySelect2, 'service');
    await user.type(itemPriceInput2, '1500');
    await user.type(itemQuantityInput2, '1');

    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 步驟 6: 設定稅率
    const taxRateInput = screen.getByLabelText(/稅率/i);
    await user.clear(taxRateInput);
    await user.type(taxRateInput, '5');

    // 步驟 7: 驗證收據預覽內容
    await waitFor(() => {
      // 檢查商店資訊是否顯示
      expect(screen.getByText('科技電腦專賣店')).toBeInTheDocument();
      expect(screen.getByText('台北市信義區信義路五段7號')).toBeInTheDocument();
      
      // 檢查客戶資訊是否顯示
      expect(screen.getByText('王小明')).toBeInTheDocument();
      
      // 檢查項目是否顯示
      expect(screen.getByText('Intel Core i7-13700K')).toBeInTheDocument();
      expect(screen.getByText('系統組裝服務')).toBeInTheDocument();
    });

    // 步驟 8: 驗證計算邏輯
    await waitFor(() => {
      // 小計應該是 12500 + 1500 = 14000
      expect(screen.getByText('NT$ 14,000')).toBeInTheDocument();
      
      // 稅額應該是 14000 * 0.05 = 700
      expect(screen.getByText('NT$ 700')).toBeInTheDocument();
      
      // 總計應該是 14000 + 700 = 14700
      expect(screen.getByText('NT$ 14,700')).toBeInTheDocument();
    });

    // 步驟 9: 驗證按鈕現在已啟用
    await waitFor(() => {
      expect(printButton).not.toBeDisabled();
      expect(pdfButton).not.toBeDisabled();
    });

    // 步驟 10: 測試項目編輯功能
    const editButtons = screen.getAllByRole('button', { name: /編輯/i });
    await user.click(editButtons[0]);

    // 修改價格
    const editPriceInput = screen.getByDisplayValue('12500');
    await user.clear(editPriceInput);
    await user.type(editPriceInput, '11500');

    // 儲存修改
    const saveEditButton = screen.getByRole('button', { name: /儲存項目/i });
    await user.click(saveEditButton);

    // 驗證計算更新
    await waitFor(() => {
      // 新的小計應該是 11500 + 1500 = 13000
      expect(screen.getByText('NT$ 13,000')).toBeInTheDocument();
      
      // 新的稅額應該是 13000 * 0.05 = 650
      expect(screen.getByText('NT$ 650')).toBeInTheDocument();
      
      // 新的總計應該是 13000 + 650 = 13650
      expect(screen.getByText('NT$ 13,650')).toBeInTheDocument();
    });
  });

  it('handles item deletion correctly', async () => {
    render(<App />);

    // 先新增一些基本資訊和項目
    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');

    // 新增項目
    await clickAddItemButton();
    await user.type(screen.getByLabelText(/項目名稱/i), '測試項目');
    await user.type(screen.getByLabelText(/單價/i), '1000');
    await user.type(screen.getByLabelText(/數量/i), '1');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 驗證項目存在
    expect(screen.getByText('測試項目')).toBeInTheDocument();
    expect(screen.getByText('NT$ 1,000')).toBeInTheDocument();

    // 刪除項目
    const deleteButton = screen.getByRole('button', { name: /刪除/i });
    await user.click(deleteButton);

    // 驗證項目已刪除，回到空狀態
    await waitFor(() => {
      expect(screen.queryByText('測試項目')).not.toBeInTheDocument();
      expect(screen.getByText('請先新增項目來查看收據預覽')).toBeInTheDocument();
    });
  });

  it('validates form inputs correctly', async () => {
    render(<App />);

    // 測試項目表單驗證
    await user.click(screen.getByRole('button', { name: /新增項目/i }));

    // 嘗試儲存空表單
    const saveButton = screen.getByRole('button', { name: /儲存項目/i });
    await user.click(saveButton);

    // 應該顯示驗證錯誤
    await waitFor(() => {
      expect(screen.getByText(/項目名稱為必填欄位/i)).toBeInTheDocument();
      expect(screen.getByText(/單價必須大於 0/i)).toBeInTheDocument();
    });

    // 測試無效價格
    await user.type(screen.getByLabelText(/項目名稱/i), '測試項目');
    await user.type(screen.getByLabelText(/單價/i), '-100');
    await user.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText(/單價必須大於 0/i)).toBeInTheDocument();
    });

    // 測試有效輸入
    const priceInput = screen.getByLabelText(/單價/i);
    await user.clear(priceInput);
    await user.type(priceInput, '1000');
    await user.type(screen.getByLabelText(/數量/i), '1');
    await user.click(saveButton);

    // 驗證錯誤消失，項目成功新增
    await waitFor(() => {
      expect(screen.queryByText(/項目名稱為必填欄位/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/單價必須大於 0/i)).not.toBeInTheDocument();
      expect(screen.getByText('測試項目')).toBeInTheDocument();
    });
  });

  it('handles tax rate changes correctly', async () => {
    render(<App />);

    // 新增基本資訊和項目
    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');

    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.type(screen.getByLabelText(/項目名稱/i), '測試項目');
    await user.type(screen.getByLabelText(/單價/i), '1000');
    await user.type(screen.getByLabelText(/數量/i), '1');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 測試不同稅率
    const taxRateInput = screen.getByLabelText(/稅率/i);
    
    // 設定 10% 稅率
    await user.clear(taxRateInput);
    await user.type(taxRateInput, '10');

    await waitFor(() => {
      expect(screen.getByText('NT$ 1,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 100')).toBeInTheDocument();   // 稅額 (1000 * 0.1)
      expect(screen.getByText('NT$ 1,100')).toBeInTheDocument(); // 總計
    });

    // 設定 0% 稅率
    await user.clear(taxRateInput);
    await user.type(taxRateInput, '0');

    await waitFor(() => {
      expect(screen.getByText('NT$ 1,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 0')).toBeInTheDocument();     // 稅額
      expect(screen.getByText('NT$ 1,000')).toBeInTheDocument(); // 總計 (與小計相同)
    });
  });

  it('maintains receipt number consistency', async () => {
    render(<App />);

    // 新增基本資訊和項目
    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');

    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.type(screen.getByLabelText(/項目名稱/i), '測試項目');
    await user.type(screen.getByLabelText(/單價/i), '1000');
    await user.type(screen.getByLabelText(/數量/i), '1');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 找到收據編號
    const receiptNumberElement = screen.getByText(/RCP-/);
    const receiptNumber = receiptNumberElement.textContent;

    // 進行一些操作後，收據編號應該保持不變
    await user.type(screen.getByLabelText(/商店地址/i), '測試地址');
    
    // 驗證收據編號沒有改變
    expect(screen.getByText(receiptNumber!)).toBeInTheDocument();
  });

  it('shows proper status indicators', async () => {
    render(<App />);

    // 初始狀態 - 所有指示器應該是未完成
    expect(screen.getAllByText('⚠️')).toHaveLength(3); // 商店、客戶、項目都未完成

    // 填寫商店資訊
    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    
    await waitFor(() => {
      const completeIndicators = screen.getAllByText('✅');
      expect(completeIndicators).toHaveLength(1); // 只有商店資訊完成
    });

    // 填寫客戶資訊
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');
    
    await waitFor(() => {
      const completeIndicators = screen.getAllByText('✅');
      expect(completeIndicators).toHaveLength(2); // 商店和客戶資訊完成
    });

    // 新增項目
    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.type(screen.getByLabelText(/項目名稱/i), '測試項目');
    await user.type(screen.getByLabelText(/單價/i), '1000');
    await user.type(screen.getByLabelText(/數量/i), '1');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    await waitFor(() => {
      const completeIndicators = screen.getAllByText('✅');
      expect(completeIndicators).toHaveLength(3); // 所有資訊都完成
    });
  });
});