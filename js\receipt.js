/**
 * Receipt Management
 * 收據管理模塊
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

'use strict';

// 收據管理類
class ReceiptManager {
    constructor() {
        this.currentReceipt = null;
        this.previewContainer = Utils.dom.find('#receipt-preview');
        this.init();
    }

    /**
     * 初始化
     * Initialize
     */
    init() {
        this.bindEvents();
    }

    /**
     * 綁定事件
     * Bind events
     */
    bindEvents() {
        // 監聽計算更新事件
        window.addEventListener('calculationsUpdated', (e) => {
            this.updatePreview(e.detail);
        });

        // 監聽收據創建事件
        window.addEventListener('receiptCreated', (e) => {
            this.displayReceipt(e.detail.receipt);
        });

        // 打印按鈕
        const printBtn = Utils.dom.find('#print-receipt-btn');
        if (printBtn) {
            Utils.dom.on(printBtn, 'click', () => {
                this.printReceipt();
            });
        }

        // 保存PDF按鈕
        const savePdfBtn = Utils.dom.find('#save-pdf-btn');
        if (savePdfBtn) {
            Utils.dom.on(savePdfBtn, 'click', () => {
                this.savePdf();
            });
        }
    }

    /**
     * 更新預覽
     * Update preview
     */
    updatePreview(data) {
        if (!this.previewContainer) return;

        // 獲取表單數據
        const formData = this.getFormData();
        
        // 構建收據數據
        const receiptData = {
            ...formData,
            ...data,
            status: 'draft'
        };

        // 渲染預覽
        this.renderPreview(receiptData);
        
        // 啟用操作按鈕
        this.enableActionButtons(data.items.length > 0);
    }

    /**
     * 獲取表單數據
     * Get form data
     */
    getFormData() {
        const form = Utils.dom.find('#receipt-form');
        if (!form) return {};

        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        return data;
    }

    /**
     * 渲染預覽
     * Render preview
     */
    renderPreview(receiptData) {
        const html = this.generateReceiptHtml(receiptData);
        this.previewContainer.innerHTML = html;
    }

    /**
     * 生成收據HTML
     * Generate receipt HTML
     */
    generateReceiptHtml(data) {
        const storeInfo = this.getStoreInfo(data.store_id);
        const customerInfo = this.getCustomerInfo(data);
        
        return `
            <div class="receipt-document">
                ${this.generateHeader(storeInfo, data)}
                ${this.generateCustomerSection(customerInfo)}
                ${this.generateItemsSection(data.items || [])}
                ${this.generateSummarySection(data)}
                ${this.generateFooter()}
            </div>
        `;
    }

    /**
     * 生成收據頭部
     * Generate receipt header
     */
    generateHeader(storeInfo, data) {
        return `
            <div class="receipt-header">
                <div class="receipt-header-content">
                    <div class="receipt-store-info">
                        ${storeInfo.logo_url ? `<img src="${storeInfo.logo_url}" alt="Store Logo" class="receipt-logo">` : ''}
                        <div class="receipt-store-name">${Utils.sanitizeHtml(storeInfo.name || 'KMS PC Store')}</div>
                        <div class="receipt-store-details">
                            ${Utils.sanitizeHtml(storeInfo.address || '')}<br>
                            ${Utils.sanitizeHtml(storeInfo.phone || '')}<br>
                            ${Utils.sanitizeHtml(storeInfo.email || '')}
                        </div>
                    </div>
                    <div class="receipt-meta">
                        <div class="receipt-number">${Utils.sanitizeHtml(data.receipt_number || '')}</div>
                        <div class="receipt-date">${Utils.formatDate(data.receipt_date)}</div>
                        <div class="receipt-status receipt-status-${data.status}">${t(data.status)}</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成客戶信息部分
     * Generate customer section
     */
    generateCustomerSection(customerInfo) {
        return `
            <div class="receipt-customer">
                <div class="receipt-customer-title" data-i18n="bill-to">${t('bill-to')}</div>
                <div class="receipt-customer-details">
                    <div class="receipt-customer-field">
                        <div class="receipt-customer-label" data-i18n="customer-name">${t('customer-name')}</div>
                        <div class="receipt-customer-value">${Utils.sanitizeHtml(customerInfo.name || '')}</div>
                    </div>
                    <div class="receipt-customer-field">
                        <div class="receipt-customer-label" data-i18n="customer-phone">${t('customer-phone')}</div>
                        <div class="receipt-customer-value">${Utils.sanitizeHtml(customerInfo.phone || '')}</div>
                    </div>
                    ${customerInfo.email ? `
                    <div class="receipt-customer-field">
                        <div class="receipt-customer-label" data-i18n="customer-email">${t('customer-email')}</div>
                        <div class="receipt-customer-value">${Utils.sanitizeHtml(customerInfo.email)}</div>
                    </div>
                    ` : ''}
                    ${customerInfo.address ? `
                    <div class="receipt-customer-field">
                        <div class="receipt-customer-label" data-i18n="customer-address">${t('customer-address')}</div>
                        <div class="receipt-customer-value">${Utils.sanitizeHtml(customerInfo.address)}</div>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 生成項目部分
     * Generate items section
     */
    generateItemsSection(items) {
        if (!items || items.length === 0) {
            return `
                <div class="receipt-items">
                    <div class="receipt-items-title" data-i18n="receipt-items">${t('receipt-items')}</div>
                    <div class="receipt-items-empty" data-i18n="no-items">${t('no-items')}</div>
                </div>
            `;
        }

        const itemsHtml = items.map(item => {
            const total = Utils.calc.calculateItemSubtotal(item.price, item.quantity);
            return `
                <tr>
                    <td>
                        <div class="receipt-item-name">${Utils.sanitizeHtml(item.name)}</div>
                        ${item.category_name ? `<div class="receipt-item-category">${Utils.sanitizeHtml(item.category_name)}</div>` : ''}
                    </td>
                    <td class="receipt-item-price">${Utils.formatCurrency(item.price)}</td>
                    <td class="receipt-item-quantity">${item.quantity}</td>
                    <td class="receipt-item-total">${Utils.formatCurrency(total)}</td>
                </tr>
            `;
        }).join('');

        return `
            <div class="receipt-items">
                <div class="receipt-items-title" data-i18n="receipt-items">${t('receipt-items')}</div>
                <table class="receipt-table">
                    <thead>
                        <tr>
                            <th data-i18n="item">${t('item')}</th>
                            <th data-i18n="price">${t('price')}</th>
                            <th data-i18n="quantity">${t('quantity')}</th>
                            <th data-i18n="total">${t('total')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${itemsHtml}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * 生成摘要部分
     * Generate summary section
     */
    generateSummarySection(data) {
        return `
            <div class="receipt-summary">
                <table class="receipt-summary-table">
                    <tr class="receipt-summary-subtotal">
                        <td class="receipt-summary-label" data-i18n="subtotal">${t('subtotal')}</td>
                        <td class="receipt-summary-value">${Utils.formatCurrency(data.subtotal || 0)}</td>
                    </tr>
                    <tr class="receipt-summary-tax">
                        <td class="receipt-summary-label">${t('tax')} (${data.tax_rate || 0}%)</td>
                        <td class="receipt-summary-value">${Utils.formatCurrency(data.taxAmount || 0)}</td>
                    </tr>
                    <tr class="receipt-summary-total">
                        <td class="receipt-summary-label" data-i18n="grand-total">${t('grand-total')}</td>
                        <td class="receipt-summary-value">${Utils.formatCurrency(data.total || 0)}</td>
                    </tr>
                </table>
            </div>
        `;
    }

    /**
     * 生成頁腳
     * Generate footer
     */
    generateFooter() {
        return `
            <div class="receipt-footer">
                <div class="receipt-footer-text">
                    ${t('footer-copyright')}<br>
                    ${t('footer-version')}
                </div>
                <div class="receipt-footer-signature">
                    <div class="receipt-signature-section">
                        <div class="receipt-signature-line"></div>
                        <div class="receipt-signature-label">Customer Signature</div>
                    </div>
                    <div class="receipt-signature-section">
                        <div class="receipt-signature-line"></div>
                        <div class="receipt-signature-label">Store Representative</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 獲取商店信息
     * Get store info
     */
    getStoreInfo(storeId) {
        // 嘗試從 formHandler 獲取商店數據
        if (window.formHandler && window.formHandler.stores && storeId) {
            const store = window.formHandler.stores.find(s => s.id == storeId);
            if (store) {
                return store;
            }
        }

        // 返回默認值
        return {
            name: 'KMS PC Store',
            address: '123 Tech Street, Taipei City, Taiwan',
            phone: '+886-2-1234-5678',
            email: '<EMAIL>',
            logo_url: ''
        };
    }

    /**
     * 獲取客戶信息
     * Get customer info
     */
    getCustomerInfo(data) {
        return {
            name: data.customer_name || '',
            phone: data.customer_phone || '',
            email: data.customer_email || '',
            address: data.customer_address || ''
        };
    }

    /**
     * 顯示收據
     * Display receipt
     */
    displayReceipt(receipt) {
        this.currentReceipt = receipt;
        this.renderPreview(receipt);
        this.enableActionButtons(true);
    }

    /**
     * 啟用操作按鈕
     * Enable action buttons
     */
    enableActionButtons(enabled) {
        const printBtn = Utils.dom.find('#print-receipt-btn');
        const savePdfBtn = Utils.dom.find('#save-pdf-btn');

        if (printBtn) {
            printBtn.disabled = !enabled;
        }
        if (savePdfBtn) {
            savePdfBtn.disabled = !enabled;
        }
    }

    /**
     * 打印收據
     * Print receipt
     */
    printReceipt() {
        if (!this.previewContainer) return;

        // 創建打印窗口
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
            alert('Please allow popups to print the receipt');
            return;
        }

        // 構建打印HTML
        const printHtml = `
            <!DOCTYPE html>
            <html lang="${i18n.getCurrentLanguage()}">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Receipt - ${this.currentReceipt?.receipt_number || ''}</title>
                <link rel="stylesheet" href="css/main.css">
                <link rel="stylesheet" href="css/receipt.css">
                <link rel="stylesheet" href="css/print.css">
            </head>
            <body class="print-show">
                <div class="kms-main">
                    <div class="kms-preview-section">
                        ${this.previewContainer.innerHTML}
                    </div>
                </div>
            </body>
            </html>
        `;

        printWindow.document.write(printHtml);
        printWindow.document.close();

        // 等待樣式載入後打印
        printWindow.onload = () => {
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);
        };
    }

    /**
     * 保存PDF
     * Save PDF
     */
    savePdf() {
        // 這裡可以集成PDF生成庫，如jsPDF或html2pdf
        // 暫時使用瀏覽器的打印功能
        this.printReceipt();
    }
}

// 創建全局收據管理器實例
window.receiptManager = null;

// 在DOM載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    window.receiptManager = new ReceiptManager();
});
