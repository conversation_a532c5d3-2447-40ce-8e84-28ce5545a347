import React from 'react';
import html2pdf from 'html2pdf.js';
import { Button, type ButtonProps } from './Button';

interface SavePDFButtonProps extends Omit<ButtonProps, 'onClick' | 'children' | 'onError'> {
  targetElementId: string;
  filename?: string;
  onBeforeSave?: () => void;
  onAfterSave?: () => void;
  onError?: (error: Error) => void;
  children?: React.ReactNode;
}

export const SavePDFButton: React.FC<SavePDFButtonProps> = ({
  targetElementId,
  filename = 'receipt.pdf',
  onBeforeSave,
  onAfterSave,
  onError,
  children = '儲存 PDF',
  variant = 'secondary',
  loading,
  ...props
}) => {
  const [isGenerating, setIsGenerating] = React.useState(false);

  const handleSavePDF = async () => {
    try {
      setIsGenerating(true);
      onBeforeSave?.();

      const targetElement = document.getElementById(targetElementId);
      if (!targetElement) {
        throw new Error(`找不到 ID 為 "${targetElementId}" 的元素`);
      }

      // PDF 生成選項，針對 Letter 紙張尺寸最佳化，支持自動分頁
      const options = {
        margin: [0.5, 0.5, 0.5, 0.5], // 上、右、下、左邊距 (英吋)
        filename: filename,
        image: {
          type: 'jpeg',
          quality: 0.98
        },
        html2canvas: {
          scale: 2, // 提高解析度
          useCORS: true,
          letterRendering: true,
          allowTaint: false,
          backgroundColor: '#ffffff',
          logging: false,
          scrollX: 0,
          scrollY: 0,
          height: null, // 允許自動高度以支持多頁
          windowWidth: 816,
          windowHeight: 1056
        },
        jsPDF: {
          unit: 'in',
          format: 'letter', // Letter 紙張格式 (8.5" x 11")
          orientation: 'portrait',
          compress: true,
          putOnlyUsedFonts: true,
          floatPrecision: 16
        },
        pagebreak: {
          mode: ['css', 'legacy'], // 移除 avoid-all 以允許分頁
          before: '.page-break-before',
          after: '.page-break-after',
          avoid: '.page-break-avoid'
        },
        enableLinks: false
      };

      // 生成 PDF
      await html2pdf()
        .set(options)
        .from(targetElement)
        .save();

      onAfterSave?.();
    } catch (error) {
      console.error('PDF 生成失敗:', error);
      const errorMessage = error instanceof Error ? error : new Error('PDF 生成失敗');
      onError?.(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Button
      variant={variant}
      loading={loading || isGenerating}
      onClick={handleSavePDF}
      {...props}
    >
      {children}
    </Button>
  );
};