// 預設項目數據
export interface PresetItem {
  id: string;
  name: string;
  category: 'PC Case' | 'CPU' | 'CPU Cooler' | 'GPU' | 'RAM' | 'SSD' | 'Motherboard' | 'PSU' | 'RGB' | 'Other' | 'Labor Fee' | 'OS' | 'OC' | 'Package' | 'Other Services';
  price: number;
  originalPrice?: number;
  description?: string;
  isCustom?: boolean; // 標記是否為用戶自定義項目
  order?: number; // 用於排序
}

// 自動生成 ID 的工具函數
const generateId = (name: string): string => {
  return name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
};

// 原始項目數據（不含 ID）
const rawPresetItems: Omit<PresetItem, 'id'>[] = [
  // PC Cases
  {
    name: 'Premium PC Case White',
    category: 'PC Case' as const,
    price: 199.99,
    originalPrice: 249.99,
    description: 'High-quality white PC case with tempered glass',
    order: 1
  },

  // CPUs
  {
    name: 'Intel Ultra 265K',
    category: 'CPU' as const,
    price: 399.99,
    originalPrice: 449.99,
    description: 'Intel Ultra 265K processor',
    order: 2
  },
  {
    name: 'Intel Ultra 285K',
    category: 'CPU' as const,
    price: 599.99,
    originalPrice: 649.99,
    description: 'Intel Ultra 285K processor',
    order: 3
  },
  {
    name: 'AMD 9800X3D',
    category: 'CPU' as const,
    price: 479.99,
    originalPrice: 529.99,
    description: 'AMD Ryzen 9800X3D processor',
    order: 4
  },
  {
    name: 'AMD 9950X3D',
    category: 'CPU' as const,
    price: 699.99,
    originalPrice: 749.99,
    description: 'AMD Ryzen 9950X3D processor',
    order: 5
  },

  // CPU Coolers
  {
    name: 'Premium CPU AIO Cooler',
    category: 'CPU Cooler' as const,
    price: 149.99,
    originalPrice: 179.99,
    description: 'All-in-one liquid CPU cooler',
    order: 6
  },
  {
    name: 'CPU Extra Cool',
    category: 'CPU Cooler' as const,
    price: 89.99,
    originalPrice: 109.99,
    description: 'High-performance air CPU cooler',
    order: 7
  },

  // GPUs
  {
    name: 'Premium GPU 5060Ti 16GB',
    category: 'GPU' as const,
    price: 799.99,
    originalPrice: 899.99,
    description: 'High-end graphics card with 16GB VRAM',
    order: 8
  },
  {
    name: 'Premium GPU 5070',
    category: 'GPU' as const,
    price: 999.99,
    originalPrice: 1099.99,
    description: 'Premium graphics card',
    order: 9
  },
  {
    name: 'Premium GPU 5070Ti',
    category: 'GPU' as const,
    price: 1199.99,
    originalPrice: 1299.99,
    description: 'Premium Ti graphics card',
    order: 10
  },
  {
    name: 'Premium GPU 5080',
    category: 'GPU' as const,
    price: 1599.99,
    originalPrice: 1699.99,
    description: 'High-end graphics card',
    order: 11
  },
  {
    name: 'Premium GPU 5090',
    category: 'GPU' as const,
    price: 2199.99,
    originalPrice: 2399.99,
    description: 'Top-tier graphics card',
    order: 12
  },
  {
    name: 'GPU PowerAdapter',
    category: 'GPU' as const,
    price: 29.99,
    originalPrice: 39.99,
    description: 'GPU power adapter cable',
    order: 13
  },

  // Memory
  {
    name: 'DDR5 RAM 32GB',
    category: 'RAM' as const,
    price: 299.99,
    originalPrice: 349.99,
    description: '32GB DDR5 memory kit',
    order: 14
  },
  {
    name: 'DDR5 RAM 48GB',
    category: 'RAM' as const,
    price: 399.99,
    originalPrice: 449.99,
    description: '48GB DDR5 memory kit',
    order: 15
  },
  {
    name: 'DDR5 RAM 64GB',
    category: 'RAM' as const,
    price: 599.99,
    originalPrice: 649.99,
    description: '64GB DDR5 memory kit',
    order: 16
  },
  {
    name: 'DDR5 RAM 96GB',
    category: 'RAM' as const,
    price: 799.99,
    originalPrice: 899.99,
    description: '96GB DDR5 memory kit',
    order: 17
  },
  {
    name: 'DDR5 RAM 128GB',
    category: 'RAM' as const,
    price: 1199.99,
    originalPrice: 1299.99,
    description: '128GB DDR5 memory kit',
    order: 18
  },
  {
    name: 'DDR5 RAM 192GB',
    category: 'RAM' as const,
    price: 1799.99,
    originalPrice: 1999.99,
    description: '192GB DDR5 memory kit',
    order: 19
  },

  // Storage
  {
    name: 'Premium SSD 2TB',
    category: 'SSD' as const,
    price: 299.99,
    originalPrice: 349.99,
    description: '2TB premium NVMe SSD',
    order: 20
  },
  {
    name: 'Premium SSD 4TB',
    category: 'SSD' as const,
    price: 599.99,
    originalPrice: 699.99,
    description: '4TB premium NVMe SSD',
    order: 21
  },
  {
    name: 'Ultra SSD 2TB',
    category: 'SSD' as const,
    price: 399.99,
    originalPrice: 449.99,
    description: '2TB ultra-fast NVMe SSD',
    order: 22
  },
  {
    name: 'Ultra SSD 4TB',
    category: 'SSD' as const,
    price: 799.99,
    originalPrice: 899.99,
    description: '4TB ultra-fast NVMe SSD',
    order: 23
  },

  // Motherboards
  {
    name: 'Premium Motherboard White',
    category: 'Motherboard' as const,
    price: 399.99,
    originalPrice: 449.99,
    description: 'Premium white motherboard',
    order: 24
  },

  // Power Supplies
  {
    name: 'Premium PSU 850w',
    category: 'PSU' as const,
    price: 199.99,
    originalPrice: 229.99,
    description: '850W premium power supply',
    order: 25
  },
  {
    name: 'Premium PSU 1000w',
    category: 'PSU' as const,
    price: 249.99,
    originalPrice: 279.99,
    description: '1000W premium power supply',
    order: 26
  },
  {
    name: 'Premium PSU 1200w',
    category: 'PSU' as const,
    price: 299.99,
    originalPrice: 349.99,
    description: '1200W premium power supply',
    order: 27
  },
  {
    name: 'Premium PSU 1300w',
    category: 'PSU' as const,
    price: 349.99,
    originalPrice: 399.99,
    description: '1300W premium power supply',
    order: 28
  },
  {
    name: 'Premium PSU 1600w',
    category: 'PSU' as const,
    price: 449.99,
    originalPrice: 499.99,
    description: '1600W premium power supply',
    order: 29
  },

  // RGB Components
  {
    name: 'MB RGB',
    category: 'RGB' as const,
    price: 49.99,
    originalPrice: 59.99,
    description: 'Motherboard RGB lighting kit',
    order: 30
  },
  {
    name: 'GPU RGB',
    category: 'RGB' as const,
    price: 39.99,
    originalPrice: 49.99,
    description: 'GPU RGB lighting kit',
    order: 31
  },
  {
    name: 'Premium RGB Fan',
    category: 'RGB' as const,
    price: 29.99,
    originalPrice: 39.99,
    description: 'Premium RGB cooling fan',
    order: 32
  },
  {
    name: 'Other',
    category: 'Other' as const,
    price: 0,
    description: 'Other hardware component',
    order: 33
  },

  // Services
  {
    name: 'Labor Fee',
    category: 'Labor Fee' as const,
    price: 99.99,
    originalPrice: 129.99,
    description: 'Professional assembly labor',
    order: 34
  },
  {
    name: 'Win11 Home',
    category: 'OS' as const,
    price: 139.99,
    originalPrice: 159.99,
    description: 'Windows 11 Home license',
    order: 35
  },
  {
    name: 'Win11 Pro',
    category: 'OS' as const,
    price: 199.99,
    originalPrice: 229.99,
    description: 'Windows 11 Pro license',
    order: 36
  },
  {
    name: 'Win11 Install All in 1',
    category: 'OS' as const,
    price: 79.99,
    originalPrice: 99.99,
    description: 'Complete Windows 11 installation service',
    order: 37
  },
  {
    name: 'CPU OC',
    category: 'OC' as const,
    price: 49.99,
    originalPrice: 69.99,
    description: 'CPU overclocking service',
    order: 38
  },
  {
    name: 'GPU OC',
    category: 'OC' as const,
    price: 59.99,
    originalPrice: 79.99,
    description: 'GPU overclocking service',
    order: 39
  },
  {
    name: 'Package Basic',
    category: 'Package' as const,
    price: 149.99,
    originalPrice: 179.99,
    description: 'Basic service package',
    order: 40
  },
  {
    name: 'Package Extra',
    category: 'Package' as const,
    price: 249.99,
    originalPrice: 299.99,
    description: 'Premium service package',
    order: 41
  },
  {
    name: 'Shipping',
    category: 'Other Services' as const,
    price: 29.99,
    originalPrice: 39.99,
    description: 'Shipping and handling',
    order: 42
  },
  {
    name: 'On-site computer service',
    category: 'Other Services' as const,
    price: 199.99,
    originalPrice: 249.99,
    description: 'On-site computer service',
    order: 43
  },
  {
    name: 'Three years warranty',
    category: 'Other Services' as const,
    price: 199.99,
    originalPrice: 249.99,
    description: 'Extended 3-year warranty',
    order: 44
  },
  {
    name: 'Useful softwares Install',
    category: 'Other Services' as const,
    price: 49.99,
    originalPrice: 69.99,
    description: 'Essential software installation',
    order: 45
  }
];

// 自動為所有項目添加 ID
export const presetItems: PresetItem[] = rawPresetItems.map(item => ({
  ...item,
  id: generateId(item.name)
}));

// 按類別分組預設項目
export const presetItemsByCategory = {
  'PC Case': presetItems.filter(item => item.category === 'PC Case'),
  'CPU': presetItems.filter(item => item.category === 'CPU'),
  'CPU Cooler': presetItems.filter(item => item.category === 'CPU Cooler'),
  'GPU': presetItems.filter(item => item.category === 'GPU'),
  'RAM': presetItems.filter(item => item.category === 'RAM'),
  'SSD': presetItems.filter(item => item.category === 'SSD'),
  'Motherboard': presetItems.filter(item => item.category === 'Motherboard'),
  'PSU': presetItems.filter(item => item.category === 'PSU'),
  'RGB': presetItems.filter(item => item.category === 'RGB'),
  'Other': presetItems.filter(item => item.category === 'Other'),
  'Labor Fee': presetItems.filter(item => item.category === 'Labor Fee'),
  'OS': presetItems.filter(item => item.category === 'OS'),
  'OC': presetItems.filter(item => item.category === 'OC'),
  'Package': presetItems.filter(item => item.category === 'Package'),
  'Other Services': presetItems.filter(item => item.category === 'Other Services'),
};

// 獲取折扣百分比
export const getDiscountPercentage = (price: number, originalPrice?: number): number => {
  if (!originalPrice || originalPrice <= price) return 0;
  return Math.round(((originalPrice - price) / originalPrice) * 100);
};
