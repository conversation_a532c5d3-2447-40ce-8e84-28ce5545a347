import { describe, it, expect } from 'vitest';
import { createMockReceiptItem, createMockStoreInfo, createMockCustomerInfo } from './utils';

describe('Custom Test Matchers', () => {
  describe('toBeValidPrice', () => {
    it('should pass for valid prices', () => {
      expect(99.99).toBeValidPrice();
      expect(1.00).toBeValidPrice();
      expect(1000.50).toBeValidPrice();
    });

    it('should fail for invalid prices', () => {
      expect(() => expect(-10).toBeValidPrice()).toThrow();
      expect(() => expect(0).toBeValidPrice()).toThrow();
      expect(() => expect(NaN).toBeValidPrice()).toThrow();
      expect(() => expect(Infinity).toBeValidPrice()).toThrow();
    });
  });

  describe('toBeValidQuantity', () => {
    it('should pass for valid quantities', () => {
      expect(1).toBeValidQuantity();
      expect(5).toBeValidQuantity();
      expect(100).toBeValidQuantity();
    });

    it('should fail for invalid quantities', () => {
      expect(() => expect(0).toBeValidQuantity()).toThrow();
      expect(() => expect(-1).toBeValidQuantity()).toThrow();
      expect(() => expect(1.5).toBeValidQuantity()).toThrow();
      expect(() => expect(NaN).toBeValidQuantity()).toThrow();
    });
  });

  describe('toHaveValidReceiptStructure', () => {
    it('should pass for valid receipt structure', () => {
      const validReceipt = {
        receiptNumber: 'RCP-123',
        date: new Date(),
        storeInfo: createMockStoreInfo(),
        customerInfo: createMockCustomerInfo(),
        items: [createMockReceiptItem()],
        taxRate: 0.08,
        subtotal: 100.00,
        taxAmount: 8.00,
        total: 108.00,
      };
      
      expect(validReceipt).toHaveValidReceiptStructure();
    });

    it('should fail for invalid receipt structure', () => {
      const invalidReceipt = {
        receiptNumber: 'RCP-123',
        // missing required fields
      };
      
      expect(() => expect(invalidReceipt).toHaveValidReceiptStructure()).toThrow();
    });
  });

  describe('toBeFormattedCurrency', () => {
    it('should pass for properly formatted currency', () => {
      expect('$99.99').toBeFormattedCurrency();
      expect('$1,234.56').toBeFormattedCurrency();
      expect('$0.01').toBeFormattedCurrency();
    });

    it('should fail for improperly formatted currency', () => {
      expect(() => expect('99.99').toBeFormattedCurrency()).toThrow();
      expect(() => expect('$99.9').toBeFormattedCurrency()).toThrow();
      expect(() => expect('$99').toBeFormattedCurrency()).toThrow();
      expect(() => expect('99.99$').toBeFormattedCurrency()).toThrow();
    });
  });
});