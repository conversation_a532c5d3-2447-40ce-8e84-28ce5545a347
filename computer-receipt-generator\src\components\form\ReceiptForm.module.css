.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
  text-align: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 0.5rem 0;
}

.subtitle {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.formSections {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section {
  position: relative;
}

.section:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -1.5rem;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, #e0e0e0 20%, #e0e0e0 80%, transparent);
}

/* 稅率設定區塊樣式 */
.taxSection {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.taxHeader {
  margin-bottom: 1.5rem;
}

.taxHeader h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 0.5rem 0;
}

.description {
  font-size: 0.95rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.taxInput {
  max-width: 200px;
  margin-bottom: 1rem;
}

.taxPreview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  max-width: fit-content;
}

.previewLabel {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.previewValue {
  font-size: 1rem;
  color: #007bff;
  font-weight: 600;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
    margin: 1rem;
    border-radius: 0;
    box-shadow: none;
  }

  .header h1 {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .formSections {
    gap: 2rem;
  }

  .taxSection {
    padding: 1.5rem;
  }

  .section:not(:last-child)::after {
    bottom: -1rem;
  }
}

@media (max-width: 480px) {
  .container {
    margin: 0.5rem;
  }

  .header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .header h1 {
    font-size: 1.75rem;
  }

  .taxSection {
    padding: 1rem;
  }
}