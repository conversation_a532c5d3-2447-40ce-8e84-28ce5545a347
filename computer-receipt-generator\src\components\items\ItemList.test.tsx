import { describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { ItemList } from './ItemList';
import { AppProvider } from '../../context/AppContext';

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <AppProvider>
      {component}
    </AppProvider>
  );
};

describe('ItemList', () => {
  it('應該顯示空狀態當沒有項目時', () => {
    renderWithProvider(<ItemList />);
    
    expect(screen.getByText('尚未新增任何項目')).toBeInTheDocument();
    expect(screen.getByText('點擊「新增項目」按鈕來新增第一個收據項目')).toBeInTheDocument();
    expect(screen.getAllByRole('button', { name: '新增項目' })).toHaveLength(2); // 標題和空狀態各一個
  });

  it('應該顯示正確的標題', () => {
    renderWithProvider(<ItemList />);
    
    expect(screen.getByText('收據項目')).toBeInTheDocument();
  });

  it('應該在點擊新增項目時顯示表單', () => {
    renderWithProvider(<ItemList />);
    
    const addButton = screen.getAllByRole('button', { name: '新增項目' })[0];
    fireEvent.click(addButton);
    
    expect(screen.getByText('新增項目')).toBeInTheDocument();
    expect(screen.getByLabelText('項目名稱')).toBeInTheDocument();
  });

  it('應該在顯示表單時隱藏新增按鈕', () => {
    renderWithProvider(<ItemList />);
    
    const addButton = screen.getAllByRole('button', { name: '新增項目' })[0];
    fireEvent.click(addButton);
    
    // 表單顯示後，標題區域的新增按鈕應該被隱藏
    expect(screen.queryByRole('button', { name: '新增項目' })).not.toBeInTheDocument();
  });

  it('應該顯示表格標題', () => {
    renderWithProvider(<ItemList />);
    
    // 先新增一個項目來顯示表格
    const addButton = screen.getAllByRole('button', { name: '新增項目' })[0];
    fireEvent.click(addButton);
    
    // 填寫表單
    const nameInput = screen.getByLabelText('項目名稱');
    const priceInput = screen.getByLabelText('價格');
    
    fireEvent.change(nameInput, { target: { value: 'Test Item' } });
    fireEvent.change(priceInput, { target: { value: '100' } });
    
    const submitButton = screen.getByRole('button', { name: '新增' });
    fireEvent.click(submitButton);
    
    // 檢查表格標題（使用 role 來區分）
    expect(screen.getByRole('columnheader', { name: '項目名稱' })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: '單價' })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: '數量' })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: '小計' })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: '操作' })).toBeInTheDocument();
  });

  it('應該在有項目時隱藏空狀態', () => {
    renderWithProvider(<ItemList />);
    
    // 新增一個項目
    const addButton = screen.getAllByRole('button', { name: '新增項目' })[0];
    fireEvent.click(addButton);
    
    const nameInput = screen.getByLabelText('項目名稱');
    const priceInput = screen.getByLabelText('價格');
    
    fireEvent.change(nameInput, { target: { value: 'Test Item' } });
    fireEvent.change(priceInput, { target: { value: '100' } });
    
    const submitButton = screen.getByRole('button', { name: '新增' });
    fireEvent.click(submitButton);
    
    // 空狀態應該被隱藏
    expect(screen.queryByText('尚未新增任何項目')).not.toBeInTheDocument();
  });

  it('應該正確處理表單取消', () => {
    renderWithProvider(<ItemList />);
    
    // 顯示表單
    const addButton = screen.getAllByRole('button', { name: '新增項目' })[0];
    fireEvent.click(addButton);
    
    expect(screen.getByText('新增項目')).toBeInTheDocument();
    
    // 取消表單
    const cancelButton = screen.getByRole('button', { name: '取消' });
    fireEvent.click(cancelButton);
    
    // 表單標題應該被隱藏，新增按鈕應該重新出現
    expect(screen.queryByRole('heading', { name: '新增項目' })).not.toBeInTheDocument();
    expect(screen.getAllByRole('button', { name: '新增項目' })).toHaveLength(2); // 標題和空狀態各一個
  });

  it('應該顯示空狀態圖示', () => {
    renderWithProvider(<ItemList />);
    
    expect(screen.getByText('📝')).toBeInTheDocument();
  });

  it('應該在編輯模式時隱藏新增按鈕', () => {
    renderWithProvider(<ItemList />);
    
    // 先新增一個項目
    const addButton = screen.getAllByRole('button', { name: '新增項目' })[0];
    fireEvent.click(addButton);
    
    const nameInput = screen.getByLabelText('項目名稱');
    const priceInput = screen.getByLabelText('價格');
    
    fireEvent.change(nameInput, { target: { value: 'Test Item' } });
    fireEvent.change(priceInput, { target: { value: '100' } });
    
    const submitButton = screen.getByRole('button', { name: '新增' });
    fireEvent.click(submitButton);
    
    // 點擊編輯按鈕
    const editButton = screen.getByRole('button', { name: '編輯' });
    fireEvent.click(editButton);
    
    // 新增按鈕應該被隱藏
    expect(screen.queryByRole('button', { name: '新增項目' })).not.toBeInTheDocument();
    expect(screen.getByText('編輯項目')).toBeInTheDocument();
  });
});