import { useState, useCallback, useMemo } from 'react';
import type { ValidationError } from '../types';
import {
  validatePrice,
  validateRequired,
  validateEmail,
  validatePhone,
  validateQuantity,
  validateTaxRate,
  getPriceErrorMessage,
  getRequiredErrorMessage,
  getEmailErrorMessage,
  getPhoneErrorMessage,
  getQuantityErrorMessage,
  getTaxRateErrorMessage,
} from '../utils/validation';

/**
 * 表單驗證的自訂 Hook
 */
export const useValidation = () => {
  const [errors, setErrors] = useState<ValidationError[]>([]);

  // 清除所有錯誤
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  // 清除特定欄位的錯誤
  const clearFieldError = useCallback((field: string) => {
    setErrors(prev => prev.filter(error => error.field !== field));
  }, []);

  // 新增錯誤
  const addError = useCallback((field: string, message: string) => {
    setErrors(prev => {
      // 移除該欄位的舊錯誤，然後新增新錯誤
      const filteredErrors = prev.filter(error => error.field !== field);
      return [...filteredErrors, { field, message }];
    });
  }, []);

  // 驗證價格
  const validatePriceField = useCallback((field: string, value: number | string) => {
    clearFieldError(field);
    
    if (!validatePrice(value)) {
      const message = getPriceErrorMessage(value);
      addError(field, message);
      return false;
    }
    
    return true;
  }, [clearFieldError, addError]);

  // 驗證必填欄位
  const validateRequiredField = useCallback((field: string, value: string | number | undefined | null, fieldName: string) => {
    clearFieldError(field);
    
    if (!validateRequired(value)) {
      const message = getRequiredErrorMessage(fieldName);
      addError(field, message);
      return false;
    }
    
    return true;
  }, [clearFieldError, addError]);

  // 驗證電子郵件
  const validateEmailField = useCallback((field: string, value: string) => {
    clearFieldError(field);
    
    // 如果是空值，不驗證（因為電子郵件通常是選填）
    if (!value || value.trim() === '') {
      return true;
    }
    
    if (!validateEmail(value)) {
      const message = getEmailErrorMessage();
      addError(field, message);
      return false;
    }
    
    return true;
  }, [clearFieldError, addError]);

  // 驗證電話號碼
  const validatePhoneField = useCallback((field: string, value: string) => {
    clearFieldError(field);
    
    // 如果是空值，不驗證（因為電話號碼可能是選填）
    if (!value || value.trim() === '') {
      return true;
    }
    
    if (!validatePhone(value)) {
      const message = getPhoneErrorMessage();
      addError(field, message);
      return false;
    }
    
    return true;
  }, [clearFieldError, addError]);

  // 驗證數量
  const validateQuantityField = useCallback((field: string, value: number | string) => {
    clearFieldError(field);
    
    if (!validateQuantity(value)) {
      const message = getQuantityErrorMessage(value);
      addError(field, message);
      return false;
    }
    
    return true;
  }, [clearFieldError, addError]);

  // 驗證稅率
  const validateTaxRateField = useCallback((field: string, value: number | string) => {
    clearFieldError(field);
    
    if (!validateTaxRate(value)) {
      const message = getTaxRateErrorMessage(value);
      addError(field, message);
      return false;
    }
    
    return true;
  }, [clearFieldError, addError]);

  // 取得特定欄位的錯誤
  const getFieldError = useCallback((field: string): string | undefined => {
    const error = errors.find(error => error.field === field);
    return error?.message;
  }, [errors]);

  // 檢查特定欄位是否有錯誤
  const hasFieldError = useCallback((field: string): boolean => {
    return errors.some(error => error.field === field);
  }, [errors]);

  // 檢查是否有任何錯誤
  const hasErrors = useMemo(() => {
    return errors.length > 0;
  }, [errors]);

  // 取得所有錯誤欄位
  const errorFields = useMemo(() => {
    return errors.map(error => error.field);
  }, [errors]);

  // 驗證項目表單
  const validateItemForm = useCallback((item: {
    name: string;
    price: number | string;
    quantity: number | string;
  }) => {
    let isValid = true;

    // 驗證項目名稱
    if (!validateRequiredField('itemName', item.name, '項目名稱')) {
      isValid = false;
    }

    // 驗證價格
    if (!validatePriceField('itemPrice', item.price)) {
      isValid = false;
    }

    // 驗證數量
    if (!validateQuantityField('itemQuantity', item.quantity)) {
      isValid = false;
    }

    return isValid;
  }, [validateRequiredField, validatePriceField, validateQuantityField]);

  // 驗證商店資訊表單
  const validateStoreInfoForm = useCallback((storeInfo: {
    name: string;
    address: string;
    phone: string;
    email: string;
  }) => {
    let isValid = true;

    // 所有欄位都是選填，只驗證格式
    // 驗證電話號碼
    if (!validatePhoneField('storePhone', storeInfo.phone)) {
      isValid = false;
    }

    // 驗證電子郵件
    if (!validateEmailField('storeEmail', storeInfo.email)) {
      isValid = false;
    }

    return isValid;
  }, [validatePhoneField, validateEmailField]);

  // 驗證客戶資訊表單
  const validateCustomerInfoForm = useCallback((customerInfo: {
    name: string;
    phone: string;
    email: string;
  }) => {
    let isValid = true;

    // 所有欄位都是選填，只驗證格式
    // 驗證電話號碼
    if (!validatePhoneField('customerPhone', customerInfo.phone)) {
      isValid = false;
    }

    // 驗證電子郵件
    if (!validateEmailField('customerEmail', customerInfo.email)) {
      isValid = false;
    }

    return isValid;
  }, [validatePhoneField, validateEmailField]);

  return {
    // 狀態
    errors,
    hasErrors,
    errorFields,
    
    // 基本操作
    clearErrors,
    clearFieldError,
    addError,
    getFieldError,
    hasFieldError,
    
    // 欄位驗證
    validatePriceField,
    validateRequiredField,
    validateEmailField,
    validatePhoneField,
    validateQuantityField,
    validateTaxRateField,
    
    // 表單驗證
    validateItemForm,
    validateStoreInfoForm,
    validateCustomerInfoForm,
  };
};