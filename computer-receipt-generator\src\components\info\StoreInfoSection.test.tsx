import { describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { StoreInfoSection } from './StoreInfoSection';
import { AppProvider } from '../../context/AppContext';

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <AppProvider>
      {component}
    </AppProvider>
  );
};

describe('StoreInfoSection', () => {
  it('應該正確渲染商店資訊表單', () => {
    renderWithProvider(<StoreInfoSection />);
    
    expect(screen.getByText('商店資訊')).toBeInTheDocument();
    expect(screen.getByLabelText('商店名稱')).toBeInTheDocument();
    expect(screen.getByLabelText('商店地址')).toBeInTheDocument();
    expect(screen.getByLabelText('聯絡電話')).toBeInTheDocument();
    expect(screen.getByLabelText('電子郵件')).toBeInTheDocument();
  });

  it('應該顯示描述文字', () => {
    renderWithProvider(<StoreInfoSection />);
    
    expect(screen.getByText('請填寫您的商店基本資訊，這些資訊將顯示在收據上')).toBeInTheDocument();
  });

  it('應該正確處理輸入變更', () => {
    renderWithProvider(<StoreInfoSection />);
    
    const nameInput = screen.getByLabelText('商店名稱');
    const addressInput = screen.getByLabelText('商店地址');
    const phoneInput = screen.getByLabelText('聯絡電話');
    const emailInput = screen.getByLabelText('電子郵件');
    
    fireEvent.change(nameInput, { target: { value: 'Test Store' } });
    fireEvent.change(addressInput, { target: { value: 'Test Address' } });
    fireEvent.change(phoneInput, { target: { value: '02-12345678' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    
    expect(nameInput).toHaveValue('Test Store');
    expect(addressInput).toHaveValue('Test Address');
    expect(phoneInput).toHaveValue('02-12345678');
    expect(emailInput).toHaveValue('<EMAIL>');
  });

  it('應該顯示預覽區域', () => {
    renderWithProvider(<StoreInfoSection />);
    
    expect(screen.getByText('預覽')).toBeInTheDocument();
    expect(screen.getAllByText('商店名稱')).toHaveLength(2); // 標籤和預覽各一個
    expect(screen.getAllByText('商店地址')).toHaveLength(2); // 標籤和預覽各一個
  });

  it('應該在預覽中顯示輸入的資訊', () => {
    renderWithProvider(<StoreInfoSection />);
    
    const nameInput = screen.getByLabelText('商店名稱');
    const addressInput = screen.getByLabelText('商店地址');
    const phoneInput = screen.getByLabelText('聯絡電話');
    const emailInput = screen.getByLabelText('電子郵件');
    
    fireEvent.change(nameInput, { target: { value: 'My Store' } });
    fireEvent.change(addressInput, { target: { value: 'My Address' } });
    fireEvent.change(phoneInput, { target: { value: '**********' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    
    expect(screen.getByText('My Store')).toBeInTheDocument();
    expect(screen.getByText('My Address')).toBeInTheDocument();
    expect(screen.getByText('電話: **********')).toBeInTheDocument();
    expect(screen.getByText('信箱: <EMAIL>')).toBeInTheDocument();
  });

  it('應該顯示輔助文字', () => {
    renderWithProvider(<StoreInfoSection />);
    
    expect(screen.getByText('支援多種格式：02-12345678、0912-345-678')).toBeInTheDocument();
    expect(screen.getByText('選填，用於客戶聯絡')).toBeInTheDocument();
  });

  it('應該正確標示必填欄位', () => {
    renderWithProvider(<StoreInfoSection />);
    
    const nameInput = screen.getByLabelText('商店名稱');
    const addressInput = screen.getByLabelText('商店地址');
    const phoneInput = screen.getByLabelText('聯絡電話');
    const emailInput = screen.getByLabelText('電子郵件');
    
    expect(nameInput).toBeRequired();
    expect(addressInput).toBeRequired();
    expect(phoneInput).not.toBeRequired();
    expect(emailInput).not.toBeRequired();
  });

  it('應該有正確的輸入類型', () => {
    renderWithProvider(<StoreInfoSection />);
    
    const phoneInput = screen.getByLabelText('聯絡電話');
    const emailInput = screen.getByLabelText('電子郵件');
    
    expect(phoneInput).toHaveAttribute('type', 'tel');
    expect(emailInput).toHaveAttribute('type', 'email');
  });

  it('應該顯示正確的佔位符文字', () => {
    renderWithProvider(<StoreInfoSection />);
    
    expect(screen.getByPlaceholderText('輸入商店名稱')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('輸入商店地址')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('例如：02-12345678')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('例如：<EMAIL>')).toBeInTheDocument();
  });

  it('應該在沒有選填資訊時不顯示在預覽中', () => {
    renderWithProvider(<StoreInfoSection />);
    
    const nameInput = screen.getByLabelText('商店名稱');
    const addressInput = screen.getByLabelText('商店地址');
    
    fireEvent.change(nameInput, { target: { value: 'Test Store' } });
    fireEvent.change(addressInput, { target: { value: 'Test Address' } });
    
    // 電話和郵件沒有輸入，不應該在預覽中顯示
    expect(screen.queryByText(/電話:/)).not.toBeInTheDocument();
    expect(screen.queryByText(/信箱:/)).not.toBeInTheDocument();
  });
});