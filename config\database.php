<?php
/**
 * Database Configuration
 * 數據庫配置文件
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

// 防止直接訪問
if (!defined('KMS_RECEIPT_MAKER')) {
    die('Direct access not permitted');
}

// 數據庫配置常量
define('DB_HOST', 'localhost');
define('DB_NAME', 'kms_receipt_maker');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// MySQL 路徑配置（根據用戶要求）
define('MYSQL_PATH', 'D:\\xampp\\mysql\\bin');

/**
 * 數據庫連接類
 * Database Connection Class
 */
class Database {
    private static $instance = null;
    private $connection;
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;

    /**
     * 私有構造函數（單例模式）
     * Private constructor for singleton pattern
     */
    private function __construct() {
        $this->connect();
    }

    /**
     * 獲取數據庫實例
     * Get database instance
     * 
     * @return Database
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 建立數據庫連接
     * Establish database connection
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE {$this->charset}_unicode_ci"
            ];

            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
            // 設置時區
            $this->connection->exec("SET time_zone = '+08:00'");
            
        } catch (PDOException $e) {
            $this->handleConnectionError($e);
        }
    }

    /**
     * 處理連接錯誤
     * Handle connection errors
     * 
     * @param PDOException $e
     */
    private function handleConnectionError($e) {
        error_log("Database Connection Error: " . $e->getMessage());
        
        // 根據環境顯示不同的錯誤信息
        if (defined('DEBUG') && DEBUG === true) {
            die("Database Connection Error: " . $e->getMessage());
        } else {
            die("Database connection failed. Please try again later.");
        }
    }

    /**
     * 獲取PDO連接對象
     * Get PDO connection object
     * 
     * @return PDO
     */
    public function getConnection() {
        return $this->connection;
    }

    /**
     * 執行查詢
     * Execute query
     * 
     * @param string $sql SQL語句
     * @param array $params 參數數組
     * @return PDOStatement
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Query Error: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Query execution failed");
        }
    }

    /**
     * 獲取單行數據
     * Fetch single row
     * 
     * @param string $sql SQL語句
     * @param array $params 參數數組
     * @return array|false
     */
    public function fetchRow($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * 獲取多行數據
     * Fetch multiple rows
     * 
     * @param string $sql SQL語句
     * @param array $params 參數數組
     * @return array
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * 插入數據並返回ID
     * Insert data and return ID
     * 
     * @param string $sql SQL語句
     * @param array $params 參數數組
     * @return string 最後插入的ID
     */
    public function insert($sql, $params = []) {
        $this->query($sql, $params);
        return $this->connection->lastInsertId();
    }

    /**
     * 更新數據
     * Update data
     * 
     * @param string $sql SQL語句
     * @param array $params 參數數組
     * @return int 影響的行數
     */
    public function update($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * 刪除數據
     * Delete data
     * 
     * @param string $sql SQL語句
     * @param array $params 參數數組
     * @return int 影響的行數
     */
    public function delete($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * 開始事務
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * 提交事務
     * Commit transaction
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * 回滾事務
     * Rollback transaction
     */
    public function rollback() {
        return $this->connection->rollback();
    }

    /**
     * 檢查表是否存在
     * Check if table exists
     * 
     * @param string $tableName 表名
     * @return bool
     */
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE :table_name";
        $result = $this->fetchRow($sql, ['table_name' => $tableName]);
        return !empty($result);
    }

    /**
     * 獲取表結構
     * Get table structure
     * 
     * @param string $tableName 表名
     * @return array
     */
    public function getTableStructure($tableName) {
        $sql = "DESCRIBE `{$tableName}`";
        return $this->fetchAll($sql);
    }

    /**
     * 防止克隆
     * Prevent cloning
     */
    private function __clone() {}

    /**
     * 防止反序列化
     * Prevent unserialization
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * 獲取數據庫連接的便捷函數
 * Convenience function to get database connection
 * 
 * @return Database
 */
function getDB() {
    return Database::getInstance();
}

/**
 * 數據庫初始化檢查
 * Database initialization check
 */
function checkDatabaseConnection() {
    try {
        $db = Database::getInstance();
        $result = $db->fetchRow("SELECT 1 as test");
        return $result['test'] === 1;
    } catch (Exception $e) {
        error_log("Database check failed: " . $e->getMessage());
        return false;
    }
}
?>
