import React from 'react';
import type { ReceiptData } from '../../types';
import { calculateReceiptTotals } from '../../utils/calculations';
import { ReceiptHeader } from './ReceiptHeader';
import { ItemTable } from './ItemTable';
import { ReceiptFooter } from './ReceiptFooter';
import { LogoDisplay } from '../logo/LogoDisplay';
import styles from './Receipt.module.css';

interface ReceiptProps {
  receiptData: ReceiptData;
  className?: string;
  isPrintPreview?: boolean;
}

export const Receipt: React.FC<ReceiptProps> = ({ 
  receiptData, 
  className = '',
  isPrintPreview = false 
}) => {
  // 計算收據總額
  const totals = calculateReceiptTotals(receiptData.items, receiptData.taxRate);

  const containerClasses = [
    styles.receiptContainer,
    isPrintPreview ? styles.printPreview : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {/* Logo 顯示 */}
      <LogoDisplay
        logoSettings={receiptData.logoSettings}
        isDraggable={false}
      />

      <div className={styles.receiptContent}>
        {/* 收據標題區塊 */}
        <div className={styles.receiptHeader}>
          <ReceiptHeader
            storeInfo={receiptData.storeInfo}
            customerInfo={receiptData.customerInfo}
            receiptNumber={receiptData.receiptNumber}
            date={receiptData.date}
          />
        </div>

        {/* 收據內容區塊 */}
        <div className={styles.receiptBody}>
          <ItemTable items={receiptData.items} />
        </div>

        {/* 收據頁腳區塊 */}
        <div className={styles.receiptFooter}>
          <ReceiptFooter
            subtotal={totals.subtotal}
            taxAmount={totals.taxAmount}
            total={totals.total}
            taxRate={receiptData.taxRate}
            date={receiptData.date}
          />
        </div>
      </div>
    </div>
  );
};