import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { ReceiptPreview } from './ReceiptPreview';
import { AppProvider } from '../../context/AppContext';
import { ReceiptItem } from '../../types';

// Mock the Receipt component
vi.mock('../receipt/Receipt', () => ({
  Receipt: ({ receiptData, className }: any) => (
    <div data-testid="receipt-component" className={className}>
      Receipt with {receiptData.items.length} items
    </div>
  )
}));

// Mock the useReceipt hook
const mockUseReceipt = vi.fn();
vi.mock('../../hooks/useReceipt', () => ({
  useReceipt: () => mockUseReceipt()
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <AppProvider>
      {component}
    </AppProvider>
  );
};

const mockReceiptData = {
  receiptNumber: 'RCP-123',
  date: new Date('2024-01-01'),
  storeInfo: {
    name: '測試電腦店',
    address: '台北市信義區',
    phone: '02-1234-5678',
    email: '<EMAIL>'
  },
  customerInfo: {
    name: '張三',
    phone: '0912-345-678',
    email: '<EMAIL>'
  },
  items: [
    {
      id: '1',
      name: 'CPU',
      category: 'hardware' as const,
      price: 10000,
      quantity: 1
    }
  ] as ReceiptItem[],
  taxRate: 5,
  subtotal: 10000,
  taxAmount: 500,
  total: 10500
};

const mockTotals = {
  subtotal: 10000,
  taxAmount: 500,
  total: 10500
};

describe('ReceiptPreview', () => {
  beforeEach(() => {
    mockUseReceipt.mockReturnValue({
      receiptData: mockReceiptData,
      totals: mockTotals,
      isReceiptValid: true
    });
  });

  it('renders receipt preview with all components when data is available', () => {
    renderWithProvider(<ReceiptPreview />);
    
    // 檢查預覽標題
    expect(screen.getByText('收據預覽')).toBeInTheDocument();
    
    // 檢查狀態指示器
    expect(screen.getByText('商店資訊')).toBeInTheDocument();
    expect(screen.getByText('客戶資訊')).toBeInTheDocument();
    expect(screen.getByText('收據項目')).toBeInTheDocument();
    
    // 檢查即時更新指示器
    expect(screen.getByText('即時更新')).toBeInTheDocument();
    
    // 檢查收據組件有被渲染
    expect(screen.getByTestId('receipt-component')).toBeInTheDocument();
  });

  it('shows empty state when no items are present', () => {
    mockUseReceipt.mockReturnValue({
      receiptData: { ...mockReceiptData, items: [] },
      totals: { subtotal: 0, taxAmount: 0, total: 0 },
      isReceiptValid: false
    });

    renderWithProvider(<ReceiptPreview />);
    
    // 檢查空狀態內容
    expect(screen.getByText('收據預覽')).toBeInTheDocument();
    expect(screen.getByText('請先新增項目來查看收據預覽')).toBeInTheDocument();
    
    // 檢查提示步驟
    expect(screen.getByText('填寫商店和客戶資訊')).toBeInTheDocument();
    expect(screen.getByText('新增收據項目')).toBeInTheDocument();
    expect(screen.getByText('設定適當的稅率')).toBeInTheDocument();
    
    // 收據組件不應該被渲染
    expect(screen.queryByTestId('receipt-component')).not.toBeInTheDocument();
  });

  it('displays correct status indicators based on data completeness', () => {
    renderWithProvider(<ReceiptPreview />);
    
    // 所有狀態應該是完整的（因為 mock 資料都有填寫）
    const statusItems = screen.getAllByText(/✅/);
    expect(statusItems).toHaveLength(3); // 商店資訊、客戶資訊、收據項目
  });

  it('shows incomplete status when store info is missing', () => {
    mockUseReceipt.mockReturnValue({
      receiptData: {
        ...mockReceiptData,
        storeInfo: { name: '', address: '', phone: '', email: '' }
      },
      totals: mockTotals,
      isReceiptValid: false
    });

    renderWithProvider(<ReceiptPreview />);
    
    // 商店資訊應該顯示警告圖示
    const incompleteItems = screen.getAllByText(/⚠️/);
    expect(incompleteItems.length).toBeGreaterThan(0);
  });

  it('displays receipt summary with correct calculations', () => {
    renderWithProvider(<ReceiptPreview />);
    
    // 檢查收據摘要
    expect(screen.getByText('收據摘要')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // 項目數量
    expect(screen.getByText('NT$ 10,000')).toBeInTheDocument(); // 小計
    expect(screen.getByText('NT$ 500')).toBeInTheDocument(); // 稅額
    expect(screen.getByText('NT$ 10,500')).toBeInTheDocument(); // 總計
  });

  it('shows validation warning when receipt is invalid', () => {
    mockUseReceipt.mockReturnValue({
      receiptData: {
        ...mockReceiptData,
        storeInfo: { name: '', address: '', phone: '', email: '' },
        customerInfo: { name: '', phone: '', email: '' }
      },
      totals: mockTotals,
      isReceiptValid: false
    });

    renderWithProvider(<ReceiptPreview />);
    
    // 檢查驗證警告
    expect(screen.getByText('收據資訊不完整')).toBeInTheDocument();
    expect(screen.getByText('請填寫商店名稱和地址')).toBeInTheDocument();
    expect(screen.getByText('請填寫客戶姓名')).toBeInTheDocument();
  });

  it('displays action hints', () => {
    renderWithProvider(<ReceiptPreview />);
    
    expect(screen.getByText(/收據會隨著您的輸入即時更新/)).toBeInTheDocument();
  });

  it('applies print styles when showPrintStyles is true', () => {
    renderWithProvider(<ReceiptPreview showPrintStyles={true} />);
    
    // 檢查是否有 printMode 相關的樣式被應用
    const title = screen.getByText('收據預覽');
    expect(title).toBeInTheDocument();
  });

  it('applies custom className when provided', () => {
    renderWithProvider(<ReceiptPreview className="custom-class" />);
    
    // 檢查組件有正確渲染
    const title = screen.getByText('收據預覽');
    expect(title).toBeInTheDocument();
  });

  it('handles multiple items correctly', () => {
    const multipleItemsData = {
      ...mockReceiptData,
      items: [
        ...mockReceiptData.items,
        {
          id: '2',
          name: 'RAM',
          category: 'hardware' as const,
          price: 5000,
          quantity: 2
        }
      ]
    };

    mockUseReceipt.mockReturnValue({
      receiptData: multipleItemsData,
      totals: { subtotal: 20000, taxAmount: 1000, total: 21000 },
      isReceiptValid: true
    });

    renderWithProvider(<ReceiptPreview />);
    
    // 檢查項目數量
    expect(screen.getByText('2')).toBeInTheDocument(); // 項目數量應該是 2
    expect(screen.getByText('NT$ 20,000')).toBeInTheDocument(); // 更新的小計
  });

  it('updates in real-time when data changes', () => {
    const { rerender } = renderWithProvider(<ReceiptPreview />);
    
    // 初始狀態
    expect(screen.getByText('NT$ 10,500')).toBeInTheDocument();
    
    // 更新 mock 資料
    mockUseReceipt.mockReturnValue({
      receiptData: mockReceiptData,
      totals: { subtotal: 15000, taxAmount: 750, total: 15750 },
      isReceiptValid: true
    });
    
    // 重新渲染
    rerender(
      <AppProvider>
        <ReceiptPreview />
      </AppProvider>
    );
    
    // 檢查更新後的總計
    expect(screen.getByText('NT$ 15,750')).toBeInTheDocument();
  });
});