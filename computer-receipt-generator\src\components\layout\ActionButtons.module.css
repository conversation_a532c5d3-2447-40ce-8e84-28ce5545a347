.container {
  background: #f8fafc;
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  position: relative;
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  background: #10b981;
  color: white;
}

.notification.error {
  background: #ef4444;
  color: white;
}

.notificationIcon {
  font-size: 1.2rem;
}

.notificationMessage {
  font-size: 0.9rem;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.buttonsSection {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.buttonsHeader {
  text-align: center;
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.sectionDescription {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

.buttonsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin: 1rem 0;
}

.buttonGroup {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.actionButton {
  width: 100%;
  max-width: 280px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.actionButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.actionButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.buttonIcon {
  font-size: 1.3rem;
}

.buttonText {
  font-size: 1rem;
}

.buttonDescription {
  font-size: 0.9rem;
  color: #64748b;
  text-align: center;
  margin: 0;
  max-width: 280px;
  line-height: 1.4;
}

.validationWarning {
  background: #fef3cd;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.warningIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.warningContent {
  flex: 1;
}

.warningTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #92400e;
  margin: 0 0 0.5rem 0;
}

.warningMessage {
  font-size: 0.9rem;
  color: #92400e;
  margin: 0;
  line-height: 1.5;
}

.tips {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.tipsTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #0c4a6e;
  margin: 0 0 1rem 0;
}

.tipsList {
  margin: 0;
  padding-left: 1.5rem;
  color: #0c4a6e;
}

.tipsList li {
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.tipsList li:last-child {
  margin-bottom: 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .container {
    padding: 1.5rem;
  }
  
  .buttonsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .actionButton {
    max-width: none;
    height: 56px;
  }
  
  .notification {
    top: 10px;
    right: 10px;
    left: 10px;
    padding: 0.75rem 1rem;
  }
  
  .validationWarning,
  .tips {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 1rem;
  }
  
  .sectionTitle {
    font-size: 1.3rem;
  }
  
  .sectionDescription {
    font-size: 0.9rem;
  }
  
  .actionButton {
    height: 52px;
    font-size: 1rem;
  }
  
  .buttonIcon {
    font-size: 1.2rem;
  }
  
  .buttonText {
    font-size: 0.9rem;
  }
}

/* 列印時隱藏 */
@media print {
  .container {
    display: none;
  }
}