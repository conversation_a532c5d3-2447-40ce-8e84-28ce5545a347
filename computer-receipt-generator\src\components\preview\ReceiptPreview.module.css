.previewContainer {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.previewContainer:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* 預覽標題區域 */
.previewHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.headerContent {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.previewTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: white;
}

.statusIndicators {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.statusItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.statusItem.complete {
  background: rgba(34, 197, 94, 0.2);
  color: #dcfce7;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.statusItem.incomplete {
  background: rgba(251, 191, 36, 0.2);
  color: #fef3c7;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.statusIcon {
  font-size: 0.9rem;
}

.statusText {
  white-space: nowrap;
}

.liveIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.liveIcon {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.liveText {
  white-space: nowrap;
}

/* 收據包裝器 */
.receiptWrapper {
  padding: 1rem;
  background: #f8f9fa;
  display: flex;
  justify-content: center;
}

.previewReceipt {
  max-width: 100%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-radius: 8px;
  overflow: hidden;
}

/* 列印模式 */
.printMode .receiptWrapper {
  padding: 0;
  background: white;
}

.printMode .previewReceipt {
  box-shadow: none;
  border-radius: 0;
}

/* 空狀態樣式 */
.emptyState {
  text-align: center;
  padding: 2rem 1rem;
  color: #6b7280;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.emptyTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.emptyMessage {
  font-size: 1rem;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.emptyHints {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 300px;
  margin: 0 auto;
}

.hint {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: #f3f4f6;
  border-radius: 8px;
  text-align: left;
  font-size: 0.9rem;
}

.hintIcon {
  font-size: 1.1rem;
  flex-shrink: 0;
}

/* 預覽資訊區域 */
.previewInfo {
  padding: 1rem;
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
}

.infoSection {
  margin-bottom: 2rem;
}

.infoTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #f9fafb;
  border-radius: 6px;
  border-left: 3px solid #e5e7eb;
}

.summaryLabel {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

.summaryValue {
  font-size: 0.95rem;
  color: #374151;
  font-weight: 600;
}

.totalAmount {
  color: #059669;
  font-size: 1rem;
}

/* 驗證警告 */
.validationWarning {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.warningIcon {
  font-size: 1.2rem;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.warningContent {
  flex: 1;
}

.warningTitle {
  font-size: 0.95rem;
  font-weight: 600;
  color: #92400e;
  margin: 0 0 0.5rem 0;
}

.warningList {
  margin: 0;
  padding-left: 1.2rem;
  color: #92400e;
  font-size: 0.85rem;
}

.warningList li {
  margin-bottom: 0.25rem;
}

/* 操作提示 */
.actionHints {
  padding: 1rem;
  background: #eff6ff;
  border-radius: 8px;
  border-left: 3px solid #3b82f6;
}

.hintText {
  margin: 0;
  font-size: 0.9rem;
  color: #1e40af;
  line-height: 1.5;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .previewHeader {
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
  }

  .headerContent {
    align-items: center;
    text-align: center;
  }

  .statusIndicators {
    justify-content: center;
  }

  .liveIndicator {
    align-self: center;
  }

  .receiptWrapper {
    padding: 1rem;
  }

  .previewInfo {
    padding: 1rem;
  }

  .summaryGrid {
    grid-template-columns: 1fr;
  }

  .emptyState {
    padding: 2rem 1rem;
  }

  .emptyHints {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .statusIndicators {
    flex-direction: column;
    align-items: center;
  }

  .statusItem {
    width: 100%;
    justify-content: center;
  }

  .validationWarning {
    flex-direction: column;
    text-align: center;
  }

  .warningList {
    text-align: left;
  }
}

/* 列印樣式 */
@media print {
  .previewContainer {
    box-shadow: none;
    border-radius: 0;
  }

  .previewHeader,
  .previewInfo {
    display: none;
  }

  .receiptWrapper {
    padding: 0;
    background: white;
  }
}