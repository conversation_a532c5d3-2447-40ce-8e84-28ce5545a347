/**
 * Main Application
 * 主應用程序
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

'use strict';

// 應用程序類
class App {
    constructor() {
        this.isInitialized = false;
        this.theme = 'light';
        this.init();
    }

    /**
     * 初始化應用程序
     * Initialize application
     */
    async init() {
        try {
            // 顯示載入動畫
            this.showLoading();

            // 初始化主題
            this.initTheme();

            // 等待DOM載入完成
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // 初始化各個模塊
            await this.initModules();

            // 綁定全局事件
            this.bindGlobalEvents();

            // 設置錯誤處理
            this.setupErrorHandling();

            // 標記為已初始化
            this.isInitialized = true;

            // 隱藏載入動畫
            this.hideLoading();

            console.log('KMS Receipt Maker initialized successfully');

        } catch (error) {
            console.error('Failed to initialize application:', error);
            this.showError('Failed to initialize application');
        }
    }

    /**
     * 初始化模塊
     * Initialize modules
     */
    async initModules() {
        // 國際化已在i18n.js中自動初始化
        // 表單處理器已在forms.js中自動初始化
        // 收據管理器已在receipt.js中自動初始化
        
        // 等待所有模塊初始化完成
        await this.waitForModules();
    }

    /**
     * 等待模塊載入
     * Wait for modules to load
     */
    async waitForModules() {
        const maxWait = 5000; // 最大等待5秒
        const startTime = Date.now();

        while (Date.now() - startTime < maxWait) {
            if (window.i18n && window.formHandler && window.receiptManager) {
                break;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    /**
     * 初始化主題
     * Initialize theme
     */
    initTheme() {
        // 從本地存儲獲取主題設置
        const savedTheme = Utils.storage.get('kms-theme', 'light');
        this.setTheme(savedTheme);
    }

    /**
     * 設置主題
     * Set theme
     */
    setTheme(theme) {
        this.theme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        Utils.storage.set('kms-theme', theme);

        // 更新主題切換按鈕圖標
        const themeIcon = Utils.dom.find('.kms-theme-icon');
        if (themeIcon) {
            themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
        }
    }

    /**
     * 切換主題
     * Toggle theme
     */
    toggleTheme() {
        const newTheme = this.theme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    /**
     * 綁定全局事件
     * Bind global events
     */
    bindGlobalEvents() {
        // 主題切換
        const themeToggle = Utils.dom.find('#theme-toggle');
        if (themeToggle) {
            Utils.dom.on(themeToggle, 'click', () => {
                this.toggleTheme();
            });
        }

        // 窗口大小變更
        Utils.dom.on(window, 'resize', Utils.throttle(() => {
            this.handleResize();
        }, 250));

        // 頁面可見性變更
        Utils.dom.on(document, 'visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // 在線/離線狀態
        Utils.dom.on(window, 'online', () => {
            this.handleOnlineStatus(true);
        });

        Utils.dom.on(window, 'offline', () => {
            this.handleOnlineStatus(false);
        });

        // 鍵盤快捷鍵
        Utils.dom.on(document, 'keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // API事件
        window.addEventListener('apiError', (e) => {
            this.showError(e.detail.message);
        });

        window.addEventListener('apiSuccess', (e) => {
            this.showSuccess(e.detail.message);
        });

        // 語言變更事件
        window.addEventListener('languageChanged', (e) => {
            this.handleLanguageChange(e.detail.language);
        });
    }

    /**
     * 處理窗口大小變更
     * Handle window resize
     */
    handleResize() {
        // 可以在這裡處理響應式邏輯
        const width = window.innerWidth;
        
        if (width < 768) {
            document.body.classList.add('mobile');
            document.body.classList.remove('tablet', 'desktop');
        } else if (width < 1024) {
            document.body.classList.add('tablet');
            document.body.classList.remove('mobile', 'desktop');
        } else {
            document.body.classList.add('desktop');
            document.body.classList.remove('mobile', 'tablet');
        }
    }

    /**
     * 處理頁面可見性變更
     * Handle visibility change
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // 頁面隱藏時的處理
            console.log('Page hidden');
        } else {
            // 頁面顯示時的處理
            console.log('Page visible');
        }
    }

    /**
     * 處理在線狀態
     * Handle online status
     */
    handleOnlineStatus(isOnline) {
        const statusIndicator = Utils.dom.find('#network-status');
        if (statusIndicator) {
            statusIndicator.textContent = isOnline ? 'Online' : 'Offline';
            statusIndicator.className = isOnline ? 'status-online' : 'status-offline';
        }

        if (!isOnline) {
            this.showWarning('You are currently offline. Some features may not work.');
        }
    }

    /**
     * 處理鍵盤快捷鍵
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + S: 保存草稿
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            this.saveDraft();
        }

        // Ctrl/Cmd + Enter: 生成收據
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            this.generateReceipt();
        }

        // Escape: 關閉模態框
        if (e.key === 'Escape') {
            this.closeModals();
        }
    }

    /**
     * 處理語言變更
     * Handle language change
     */
    handleLanguageChange(language) {
        // 可以在這裡處理語言變更後的邏輯
        console.log('Language changed to:', language);
    }

    /**
     * 設置錯誤處理
     * Setup error handling
     */
    setupErrorHandling() {
        // 全局錯誤處理
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
            this.showError('An unexpected error occurred');
        });

        // Promise錯誤處理
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            this.showError('An unexpected error occurred');
        });
    }

    /**
     * 顯示載入動畫
     * Show loading
     */
    showLoading() {
        const loadingOverlay = Utils.dom.find('#loading-overlay');
        if (loadingOverlay) {
            Utils.dom.addClass(loadingOverlay, 'kms-loading-show');
        }
    }

    /**
     * 隱藏載入動畫
     * Hide loading
     */
    hideLoading() {
        const loadingOverlay = Utils.dom.find('#loading-overlay');
        if (loadingOverlay) {
            Utils.dom.removeClass(loadingOverlay, 'kms-loading-show');
        }
    }

    /**
     * 顯示通知
     * Show notification
     */
    showNotification(message, type = 'info', duration = 5000) {
        const alertContainer = Utils.dom.find('#alert-container');
        if (!alertContainer) return;

        const alert = Utils.dom.create('div', {
            className: `kms-alert kms-alert-${type}`
        }, message);

        alertContainer.appendChild(alert);

        // 自動移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, duration);
    }

    /**
     * 顯示成功消息
     * Show success message
     */
    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    /**
     * 顯示錯誤消息
     * Show error message
     */
    showError(message) {
        this.showNotification(message, 'error', 8000);
    }

    /**
     * 顯示警告消息
     * Show warning message
     */
    showWarning(message) {
        this.showNotification(message, 'warning', 6000);
    }

    /**
     * 保存草稿
     * Save draft
     */
    saveDraft() {
        if (window.formHandler) {
            // 這裡可以實現草稿保存邏輯
            console.log('Saving draft...');
        }
    }

    /**
     * 生成收據
     * Generate receipt
     */
    generateReceipt() {
        const form = Utils.dom.find('#receipt-form');
        if (form) {
            form.dispatchEvent(new Event('submit'));
        }
    }

    /**
     * 關閉模態框
     * Close modals
     */
    closeModals() {
        const modals = Utils.dom.findAll('.kms-modal');
        modals.forEach(modal => {
            Utils.dom.removeClass(modal, 'kms-modal-show');
        });
    }

    /**
     * 獲取應用程序狀態
     * Get application state
     */
    getState() {
        return {
            isInitialized: this.isInitialized,
            theme: this.theme,
            language: window.i18n?.getCurrentLanguage(),
            online: navigator.onLine
        };
    }
}

// 創建全局應用程序實例
window.app = new App();

// 導出到全局作用域
window.KMSApp = window.app;
