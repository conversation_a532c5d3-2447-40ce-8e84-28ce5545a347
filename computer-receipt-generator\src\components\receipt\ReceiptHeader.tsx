import React from 'react';
import type { StoreInfo, CustomerInfo } from '../../types';
import styles from './ReceiptHeader.module.css';

interface ReceiptHeaderProps {
  storeInfo: StoreInfo;
  customerInfo: CustomerInfo;
  receiptNumber: string;
  date: Date | null;
}

export const ReceiptHeader: React.FC<ReceiptHeaderProps> = ({
  storeInfo,
  customerInfo,
  receiptNumber,
  date,
}) => {
  return (
    <div className={styles.header}>
      {/* 商店資訊區塊 */}
      <div className={styles.storeSection}>
        <h1 className={styles.storeName}>{storeInfo.name || 'Store Name'}</h1>
        <div className={styles.storeDetails}>
          {storeInfo.address && (
            <div className={styles.storeAddress}>{storeInfo.address}</div>
          )}
          <div className={styles.storeContact}>
            {storeInfo.phone && <span>Phone: {storeInfo.phone}</span>}
            {storeInfo.phone && storeInfo.email && <span className={styles.separator}> | </span>}
            {storeInfo.email && <span>Email: {storeInfo.email}</span>}
          </div>
        </div>
      </div>

      {/* 收據資訊區塊 */}
      <div className={styles.receiptInfo}>
        <h2 className={styles.receiptTitle}>Sales Receipt</h2>
        <div className={styles.receiptDetails}>
          <div className={styles.receiptNumber}>
            <span className={styles.label}>Receipt Number:</span>
            <span className={styles.value}>{receiptNumber}</span>
          </div>
        </div>
      </div>

      {/* 客戶資訊區塊 */}
      <div className={styles.customerSection}>
        <h3 className={styles.customerTitle}>Customer Information</h3>
        <div className={styles.customerDetails}>
          <div className={styles.customerName}>
            <span className={styles.label}>Name:</span>
            <span className={styles.value}>{customerInfo.name || ''}</span>
          </div>
          <div className={styles.customerPhone}>
            <span className={styles.label}>Contact Phone:</span>
            <span className={styles.value}>{customerInfo.phone || ''}</span>
          </div>
          <div className={styles.customerEmail}>
            <span className={styles.label}>E-mail:</span>
            <span className={styles.value}>{customerInfo.email || ''}</span>
          </div>
          <div className={styles.customerAddress}>
            <span className={styles.label}>Address:</span>
            <span className={styles.value}>{customerInfo.address || ''}</span>
          </div>
        </div>
      </div>
    </div>
  );
};