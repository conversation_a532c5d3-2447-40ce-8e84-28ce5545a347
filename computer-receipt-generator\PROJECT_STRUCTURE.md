# 電腦收據生成器 - 專案結構

## 目錄結構

```
src/
├── components/          # React 組件
├── types/              # TypeScript 類型定義
│   └── index.ts        # 核心介面定義
├── utils/              # 工具函數
├── styles/             # CSS 樣式文件
├── App.tsx             # 主應用程式組件
├── App.css             # 主應用程式樣式
├── main.tsx            # 應用程式入口點
└── index.css           # 全域樣式
```

## 已完成的核心介面

### 資料類型
- `ReceiptItem`: 收據項目
- `StoreInfo`: 商店資訊
- `CustomerInfo`: 客戶資訊
- `ReceiptData`: 完整收據資料

### 狀態管理類型
- `AppState`: 應用程式狀態
- `AppAction`: 狀態更新動作

### 驗證類型
- `ValidationError`: 驗證錯誤
- `ErrorState`: 錯誤狀態

## 技術棧

- **前端框架**: React 18 with TypeScript
- **建置工具**: Vite
- **樣式**: CSS Modules + CSS Grid/Flexbox
- **開發工具**: ESLint, TypeScript

## 開發指令

```bash
npm install          # 安裝依賴
npm run dev         # 開發模式
npm run build       # 建置生產版本
npm run preview     # 預覽建置結果
```