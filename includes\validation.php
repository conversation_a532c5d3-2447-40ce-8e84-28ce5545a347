<?php
/**
 * Validation Functions
 * 數據驗證函數庫
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

// 防止直接訪問
if (!defined('KMS_RECEIPT_MAKER')) {
    die('Direct access not permitted');
}

/**
 * 驗證器類
 * Validator class
 */
class Validator {
    private $errors = [];
    private $data = [];

    /**
     * 構造函數
     * Constructor
     * 
     * @param array $data 要驗證的數據
     */
    public function __construct($data = []) {
        $this->data = $data;
        $this->errors = [];
    }

    /**
     * 驗證必填字段
     * Validate required field
     * 
     * @param string $field 字段名
     * @param string $message 錯誤消息
     * @return self
     */
    public function required($field, $message = null) {
        if (!isset($this->data[$field]) || trim($this->data[$field]) === '') {
            $this->errors[$field] = $message ?? "Field '{$field}' is required";
        }
        return $this;
    }

    /**
     * 驗證電子郵件
     * Validate email
     * 
     * @param string $field 字段名
     * @param string $message 錯誤消息
     * @return self
     */
    public function email($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_EMAIL)) {
                $this->errors[$field] = $message ?? "Field '{$field}' must be a valid email";
            }
        }
        return $this;
    }

    /**
     * 驗證最小長度
     * Validate minimum length
     * 
     * @param string $field 字段名
     * @param int $min 最小長度
     * @param string $message 錯誤消息
     * @return self
     */
    public function minLength($field, $min, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) < $min) {
            $this->errors[$field] = $message ?? "Field '{$field}' must be at least {$min} characters";
        }
        return $this;
    }

    /**
     * 驗證最大長度
     * Validate maximum length
     * 
     * @param string $field 字段名
     * @param int $max 最大長度
     * @param string $message 錯誤消息
     * @return self
     */
    public function maxLength($field, $max, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) > $max) {
            $this->errors[$field] = $message ?? "Field '{$field}' must not exceed {$max} characters";
        }
        return $this;
    }

    /**
     * 驗證數字
     * Validate numeric
     * 
     * @param string $field 字段名
     * @param string $message 錯誤消息
     * @return self
     */
    public function numeric($field, $message = null) {
        if (isset($this->data[$field]) && !is_numeric($this->data[$field])) {
            $this->errors[$field] = $message ?? "Field '{$field}' must be numeric";
        }
        return $this;
    }

    /**
     * 驗證正數
     * Validate positive number
     * 
     * @param string $field 字段名
     * @param string $message 錯誤消息
     * @return self
     */
    public function positive($field, $message = null) {
        if (isset($this->data[$field]) && (float)$this->data[$field] <= 0) {
            $this->errors[$field] = $message ?? "Field '{$field}' must be positive";
        }
        return $this;
    }

    /**
     * 驗證整數
     * Validate integer
     * 
     * @param string $field 字段名
     * @param string $message 錯誤消息
     * @return self
     */
    public function integer($field, $message = null) {
        if (isset($this->data[$field]) && !filter_var($this->data[$field], FILTER_VALIDATE_INT)) {
            $this->errors[$field] = $message ?? "Field '{$field}' must be an integer";
        }
        return $this;
    }

    /**
     * 驗證電話號碼
     * Validate phone number
     * 
     * @param string $field 字段名
     * @param string $message 錯誤消息
     * @return self
     */
    public function phone($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            // 台灣電話號碼格式
            $pattern = '/^(\+886|0)([2-9]\d{7,8}|[2-9]\d{2}-\d{3}-\d{3,4})$/';
            if (!preg_match($pattern, $this->data[$field])) {
                $this->errors[$field] = $message ?? "Field '{$field}' must be a valid phone number";
            }
        }
        return $this;
    }

    /**
     * 驗證日期格式
     * Validate date format
     * 
     * @param string $field 字段名
     * @param string $format 日期格式
     * @param string $message 錯誤消息
     * @return self
     */
    public function date($field, $format = 'Y-m-d', $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $date = DateTime::createFromFormat($format, $this->data[$field]);
            if (!$date || $date->format($format) !== $this->data[$field]) {
                $this->errors[$field] = $message ?? "Field '{$field}' must be a valid date in format {$format}";
            }
        }
        return $this;
    }

    /**
     * 驗證在指定值中
     * Validate in array
     * 
     * @param string $field 字段名
     * @param array $values 允許的值
     * @param string $message 錯誤消息
     * @return self
     */
    public function in($field, $values, $message = null) {
        if (isset($this->data[$field]) && !in_array($this->data[$field], $values)) {
            $this->errors[$field] = $message ?? "Field '{$field}' must be one of: " . implode(', ', $values);
        }
        return $this;
    }

    /**
     * 自定義驗證
     * Custom validation
     * 
     * @param string $field 字段名
     * @param callable $callback 驗證回調函數
     * @param string $message 錯誤消息
     * @return self
     */
    public function custom($field, $callback, $message = null) {
        if (isset($this->data[$field])) {
            $result = call_user_func($callback, $this->data[$field]);
            if (!$result) {
                $this->errors[$field] = $message ?? "Field '{$field}' validation failed";
            }
        }
        return $this;
    }

    /**
     * 檢查是否有錯誤
     * Check if has errors
     * 
     * @return bool
     */
    public function hasErrors() {
        return !empty($this->errors);
    }

    /**
     * 獲取所有錯誤
     * Get all errors
     * 
     * @return array
     */
    public function getErrors() {
        return $this->errors;
    }

    /**
     * 獲取第一個錯誤
     * Get first error
     * 
     * @return string|null
     */
    public function getFirstError() {
        return !empty($this->errors) ? reset($this->errors) : null;
    }

    /**
     * 清除錯誤
     * Clear errors
     * 
     * @return self
     */
    public function clearErrors() {
        $this->errors = [];
        return $this;
    }
}

/**
 * 商店數據驗證
 * Store data validation
 * 
 * @param array $data 商店數據
 * @return array 錯誤數組
 */
function validateStoreData($data) {
    $validator = new Validator($data);
    
    $validator
        ->required('name', '商店名稱為必填項 / Store name is required')
        ->maxLength('name', 255, '商店名稱不能超過255個字符 / Store name cannot exceed 255 characters')
        ->required('address', '商店地址為必填項 / Store address is required')
        ->required('phone', '電話號碼為必填項 / Phone number is required')
        ->phone('phone', '請輸入有效的電話號碼 / Please enter a valid phone number')
        ->required('email', '電子郵件為必填項 / Email is required')
        ->email('email', '請輸入有效的電子郵件地址 / Please enter a valid email address');
    
    return $validator->getErrors();
}

/**
 * 客戶數據驗證
 * Customer data validation
 * 
 * @param array $data 客戶數據
 * @return array 錯誤數組
 */
function validateCustomerData($data) {
    $validator = new Validator($data);
    
    $validator
        ->required('name', '客戶姓名為必填項 / Customer name is required')
        ->maxLength('name', 255, '客戶姓名不能超過255個字符 / Customer name cannot exceed 255 characters')
        ->required('phone', '電話號碼為必填項 / Phone number is required')
        ->phone('phone', '請輸入有效的電話號碼 / Please enter a valid phone number')
        ->email('email', '請輸入有效的電子郵件地址 / Please enter a valid email address');
    
    return $validator->getErrors();
}

/**
 * 收據數據驗證
 * Receipt data validation
 * 
 * @param array $data 收據數據
 * @return array 錯誤數組
 */
function validateReceiptData($data) {
    $validator = new Validator($data);
    
    $validator
        ->required('store_id', '商店ID為必填項 / Store ID is required')
        ->integer('store_id', '商店ID必須為整數 / Store ID must be an integer')
        ->positive('store_id', '商店ID必須為正數 / Store ID must be positive')
        ->date('receipt_date', 'Y-m-d', '請輸入有效的日期格式 (YYYY-MM-DD) / Please enter a valid date format (YYYY-MM-DD)')
        ->numeric('tax_rate', '稅率必須為數字 / Tax rate must be numeric')
        ->custom('tax_rate', function($value) {
            return $value >= 0 && $value <= 100;
        }, '稅率必須在0-100之間 / Tax rate must be between 0-100');
    
    return $validator->getErrors();
}

/**
 * 收據項目數據驗證
 * Receipt item data validation
 * 
 * @param array $data 收據項目數據
 * @return array 錯誤數組
 */
function validateReceiptItemData($data) {
    $validator = new Validator($data);
    
    $validator
        ->required('name', '項目名稱為必填項 / Item name is required')
        ->maxLength('name', 255, '項目名稱不能超過255個字符 / Item name cannot exceed 255 characters')
        ->required('price', '價格為必填項 / Price is required')
        ->numeric('price', '價格必須為數字 / Price must be numeric')
        ->positive('price', '價格必須為正數 / Price must be positive')
        ->required('quantity', '數量為必填項 / Quantity is required')
        ->integer('quantity', '數量必須為整數 / Quantity must be an integer')
        ->positive('quantity', '數量必須為正數 / Quantity must be positive');
    
    return $validator->getErrors();
}
?>
