import { expect } from 'vitest';

// Custom matchers for testing receipt calculations
expect.extend({
  toBeValidPrice(received: number) {
    const pass = typeof received === 'number' && received > 0 && Number.isFinite(received);
    return {
      message: () => `expected ${received} to be a valid price (positive number)`,
      pass,
    };
  },

  toBeValidQuantity(received: number) {
    const pass = typeof received === 'number' && received > 0 && Number.isInteger(received);
    return {
      message: () => `expected ${received} to be a valid quantity (positive integer)`,
      pass,
    };
  },

  toHaveValidReceiptStructure(received: any) {
    const requiredFields = ['receiptNumber', 'date', 'storeInfo', 'customerInfo', 'items', 'taxRate', 'subtotal', 'taxAmount', 'total'];
    const hasAllFields = requiredFields.every(field => field in received);
    
    return {
      message: () => `expected object to have valid receipt structure with fields: ${requiredFields.join(', ')}`,
      pass: hasAllFields,
    };
  },

  toBeFormattedCurrency(received: string) {
    const currencyRegex = /^\$[\d,]+\.\d{2}$/;
    const pass = currencyRegex.test(received);
    
    return {
      message: () => `expected ${received} to be formatted as currency ($X,XXX.XX)`,
      pass,
    };
  },
});

// Type declarations for custom matchers
declare module 'vitest' {
  interface Assertion<T = any> {
    toBeValidPrice(): T;
    toBeValidQuantity(): T;
    toHaveValidReceiptStructure(): T;
    toBeFormattedCurrency(): T;
  }
}