# KMS PC Receipt Maker - Apache Configuration
# Apache配置文件

RewriteEngine On

# API路由重寫
# API route rewriting
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/([^/]+)/?(.*)$ api/$1.php [QSA,L]

# 安全設置
# Security settings

# 防止直接訪問敏感文件
# Prevent direct access to sensitive files
<Files "*.php">
    <RequireAll>
        Require all granted
    </RequireAll>
</Files>

<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql)$">
    Require all denied
</FilesMatch>

# 保護配置目錄
# Protect config directory
<Directory "config">
    Require all denied
</Directory>

# 保護日誌目錄
# Protect logs directory
<Directory "logs">
    Require all denied
</Directory>

# 設置安全頭部
# Set security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # CORS設置（如果需要）
    # CORS settings (if needed)
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>

# 啟用壓縮
# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# 設置緩存
# Set caching
<IfModule mod_expires.c>
    ExpiresActive On
    
    # CSS和JavaScript文件
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # 圖片文件
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # 字體文件
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML文件
    ExpiresByType text/html "access plus 1 hour"
    
    # API響應不緩存
    <FilesMatch "\.(php)$">
        ExpiresActive Off
        Header unset ETag
        Header set Cache-Control "max-age=0, no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "Wed, 11 Jan 1984 05:00:00 GMT"
    </FilesMatch>
</IfModule>

# 錯誤頁面
# Error pages
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# PHP設置
# PHP settings
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value memory_limit 256M
    php_value max_execution_time 30
    php_value max_input_time 30
</IfModule>

# 防止目錄瀏覽
# Prevent directory browsing
Options -Indexes

# 設置默認文檔
# Set default documents
DirectoryIndex index.html index.php

# 強制HTTPS（生產環境使用）
# Force HTTPS (for production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
