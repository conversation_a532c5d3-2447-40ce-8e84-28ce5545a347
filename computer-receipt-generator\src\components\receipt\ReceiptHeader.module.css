.header {
  border-bottom: 2px solid #000000;
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
}

.storeSection {
  text-align: center;
  margin-bottom: 1rem;
}

.storeName {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0 auto 0.5rem auto;
  color: #000000;
  text-align: center;
  padding: 0 20%;
  box-sizing: border-box;
}

.storeDetails {
  font-size: 0.9rem;
  color: #666;
  text-align: center;
}

.storeAddress {
  margin-bottom: 0.25rem;
}

.storeContact {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.separator {
  margin: 0 0.5rem;
  color: #999;
}

.receiptInfo {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding: 0.75rem 0;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
}

.receiptTitle {
  font-size: 1.4rem;
  font-weight: bold;
  margin: 0;
  color: #000000;
}

.receiptDetails {
  text-align: right;
  font-size: 0.9rem;
}

.receiptNumber,
.receiptDate {
  margin-bottom: 0.25rem;
}

.customerSection {
  background-color: #f8f9fa;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.customerTitle {
  font-size: 1rem;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
  color: #000000;
}

.customerDetails {
  font-size: 0.9rem;
}

.customerName,
.customerPhone,
.customerEmail {
  margin-bottom: 0.25rem;
  display: flex;
  justify-content: space-between;
}

.customerName:last-child,
.customerPhone:last-child,
.customerEmail:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #555;
  min-width: 60px;
}

.value {
  color: #000000;
  text-align: right;
  flex: 1;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .receiptInfo {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .receiptDetails {
    text-align: center;
    margin-top: 0.5rem;
  }
  
  .customerName,
  .customerPhone,
  .customerEmail {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .value {
    text-align: left;
    margin-top: 0.125rem;
  }
}

/* 列印樣式 */
@media print {
  .header {
    border-bottom: 2px solid #000;
    page-break-inside: avoid;
  }
  
  .storeName {
    font-size: 18pt;
  }
  
  .receiptTitle {
    font-size: 14pt;
  }
  
  .storeDetails,
  .receiptDetails,
  .customerDetails {
    font-size: 10pt;
  }
}