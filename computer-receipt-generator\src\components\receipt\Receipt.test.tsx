import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { Receipt } from './Receipt';
import { ReceiptData } from '../../types';

describe('Receipt', () => {
  const mockReceiptData: ReceiptData = {
    receiptNumber: 'RCP-TEST-12345',
    date: new Date('2024-01-15T10:30:00'),
    storeInfo: {
      name: '測試電腦店',
      address: '台北市信義區信義路五段7號',
      phone: '02-1234-5678',
      email: '<EMAIL>',
    },
    customerInfo: {
      name: '王小明',
      phone: '0912-345-678',
      email: '<EMAIL>',
    },
    items: [
      {
        id: '1',
        name: 'Intel Core i7-12700K',
        category: 'hardware',
        price: 12000,
        quantity: 1,
      },
      {
        id: '2',
        name: '系統安裝服務',
        category: 'service',
        price: 500,
        quantity: 1,
      },
    ],
    taxRate: 5,
    subtotal: 12500,
    taxAmount: 625,
    total: 13125,
  };

  it('renders complete receipt with all sections', () => {
    render(<Receipt receiptData={mockReceiptData} />);

    // 檢查收據標題區塊
    expect(screen.getByText('測試電腦店')).toBeInTheDocument();
    expect(screen.getByText('銷售收據')).toBeInTheDocument();
    expect(screen.getByText('RCP-TEST-12345')).toBeInTheDocument();

    // 檢查客戶資訊
    expect(screen.getByText('客戶資訊')).toBeInTheDocument();
    expect(screen.getByText('王小明')).toBeInTheDocument();

    // 檢查項目表格
    expect(screen.getByText('項目名稱')).toBeInTheDocument();
    expect(screen.getByText('Intel Core i7-12700K')).toBeInTheDocument();
    expect(screen.getByText('系統安裝服務')).toBeInTheDocument();

    // 檢查收據頁腳
    expect(screen.getByText('感謝您的惠顧！')).toBeInTheDocument();
    expect(screen.getByText('小計:')).toBeInTheDocument();
    expect(screen.getByText('總計:')).toBeInTheDocument();
  });

  it('calculates totals correctly', () => {
    render(<Receipt receiptData={mockReceiptData} />);

    // 檢查計算結果（基於項目價格重新計算）
    expect(screen.getByText('$12,500.00')).toBeInTheDocument(); // 小計
    expect(screen.getByText('$625.00')).toBeInTheDocument(); // 稅額
    expect(screen.getByText('$13,125.00')).toBeInTheDocument(); // 總計
  });

  it('applies correct CSS classes', () => {
    const { container } = render(<Receipt receiptData={mockReceiptData} />);

    const receiptContainer = container.firstChild as HTMLElement;
    expect(receiptContainer.className).toMatch(/receiptContainer/);
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-receipt-class';
    const { container } = render(
      <Receipt receiptData={mockReceiptData} className={customClass} />
    );

    const receiptContainer = container.firstChild as HTMLElement;
    expect(receiptContainer.className).toContain(customClass);
  });

  it('applies print preview class when isPrintPreview is true', () => {
    const { container } = render(
      <Receipt receiptData={mockReceiptData} isPrintPreview={true} />
    );

    const receiptContainer = container.firstChild as HTMLElement;
    expect(receiptContainer.className).toMatch(/printPreview/);
  });

  it('handles empty items list', () => {
    const emptyReceiptData: ReceiptData = {
      ...mockReceiptData,
      items: [],
    };

    render(<Receipt receiptData={emptyReceiptData} />);

    expect(screen.getByText('尚未新增任何項目')).toBeInTheDocument();
    // 會有三個 $0.00：小計、稅額、總計
    expect(screen.getAllByText('$0.00')).toHaveLength(3);
  });

  it('handles different tax rates correctly', () => {
    const differentTaxRateData: ReceiptData = {
      ...mockReceiptData,
      taxRate: 10,
    };

    render(<Receipt receiptData={differentTaxRateData} />);

    expect(screen.getByText('稅額 (10%):')).toBeInTheDocument();
  });

  it('renders all receipt sections in correct structure', () => {
    const { container } = render(<Receipt receiptData={mockReceiptData} />);

    // 檢查主要結構
    expect(container.querySelector('[class*="receiptContainer"]')).toBeInTheDocument();
    expect(container.querySelector('[class*="receiptContent"]')).toBeInTheDocument();
    expect(container.querySelector('[class*="receiptHeader"]')).toBeInTheDocument();
    expect(container.querySelector('[class*="receiptBody"]')).toBeInTheDocument();
    expect(container.querySelector('[class*="receiptFooter"]')).toBeInTheDocument();
  });

  it('maintains proper layout structure', () => {
    const { container } = render(<Receipt receiptData={mockReceiptData} />);

    const receiptContent = container.querySelector('[class*="receiptContent"]');
    expect(receiptContent).toBeInTheDocument();

    // 檢查子元素順序
    const children = receiptContent?.children;
    expect(children).toHaveLength(3);
    
    // 檢查 CSS 類別是否包含相應的樣式（CSS modules 會產生 hash）
    expect(children?.[0]?.className).toMatch(/receiptHeader/);
    expect(children?.[1]?.className).toMatch(/receiptBody/);
    expect(children?.[2]?.className).toMatch(/receiptFooter/);
  });
});