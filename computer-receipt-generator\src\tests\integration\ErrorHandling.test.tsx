import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../../App';

// Mock html2pdf with error scenarios
vi.mock('html2pdf.js', () => ({
  default: () => ({
    set: () => ({
      from: () => ({
        save: vi.fn().mockRejectedValue(new Error('PDF generation failed'))
      })
    })
  })
}));

describe('Error Handling and Edge Cases', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
  });

  it('handles PDF generation errors gracefully', async () => {
    render(<App />);

    // 設定完整的收據資訊
    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');

    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.type(screen.getByLabelText(/項目名稱/i), '測試項目');
    await user.type(screen.getByLabelText(/單價/i), '1000');
    await user.type(screen.getByLabelText(/數量/i), '1');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 嘗試生成 PDF
    const pdfButton = screen.getByRole('button', { name: /儲存 PDF/i });
    await user.click(pdfButton);

    // 應該顯示錯誤通知
    await waitFor(() => {
      expect(screen.getByText(/PDF 生成失敗/i)).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('validates email format correctly', async () => {
    render(<App />);

    // 測試無效的商店電子郵件
    const storeEmailInput = screen.getByLabelText(/商店電子郵件/i);
    await user.type(storeEmailInput, 'invalid-email');
    
    // 點擊其他地方觸發驗證
    await user.click(screen.getByLabelText(/商店名稱/i));

    await waitFor(() => {
      expect(screen.getByText(/請輸入有效的電子郵件格式/i)).toBeInTheDocument();
    });

    // 修正電子郵件格式
    await user.clear(storeEmailInput);
    await user.type(storeEmailInput, '<EMAIL>');
    
    await user.click(screen.getByLabelText(/商店名稱/i));

    await waitFor(() => {
      expect(screen.queryByText(/請輸入有效的電子郵件格式/i)).not.toBeInTheDocument();
    });
  });

  it('validates phone number format correctly', async () => {
    render(<App />);

    // 測試無效的電話號碼
    const phoneInput = screen.getByLabelText(/商店電話/i);
    await user.type(phoneInput, '123');
    
    await user.click(screen.getByLabelText(/商店名稱/i));

    await waitFor(() => {
      expect(screen.getByText(/請輸入有效的電話號碼/i)).toBeInTheDocument();
    });

    // 修正電話號碼
    await user.clear(phoneInput);
    await user.type(phoneInput, '02-2345-6789');
    
    await user.click(screen.getByLabelText(/商店名稱/i));

    await waitFor(() => {
      expect(screen.queryByText(/請輸入有效的電話號碼/i)).not.toBeInTheDocument();
    });
  });

  it('handles extreme price values correctly', async () => {
    render(<App />);

    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');

    // 測試極大的價格值
    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.type(screen.getByLabelText(/項目名稱/i), '昂貴項目');
    await user.type(screen.getByLabelText(/單價/i), '999999.99');
    await user.type(screen.getByLabelText(/數量/i), '1');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 驗證計算正確性
    await waitFor(() => {
      expect(screen.getByText('NT$ 999,999.99')).toBeInTheDocument(); // 小計
    });

    // 測試極小的價格值
    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.type(screen.getByLabelText(/項目名稱/i), '便宜項目');
    await user.type(screen.getByLabelText(/單價/i), '0.01');
    await user.type(screen.getByLabelText(/數量/i), '1');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    await waitFor(() => {
      expect(screen.getByText('NT$ 1,000,000')).toBeInTheDocument(); // 新的小計
    });
  });

  it('handles large quantities correctly', async () => {
    render(<App />);

    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');

    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.type(screen.getByLabelText(/項目名稱/i), '大量項目');
    await user.type(screen.getByLabelText(/單價/i), '10');
    await user.type(screen.getByLabelText(/數量/i), '1000');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 驗證計算正確性 (10 * 1000 = 10000)
    await waitFor(() => {
      expect(screen.getByText('NT$ 10,000')).toBeInTheDocument();
    });
  });

  it('handles decimal quantities correctly', async () => {
    render(<App />);

    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');

    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.type(screen.getByLabelText(/項目名稱/i), '小數數量項目');
    await user.type(screen.getByLabelText(/單價/i), '100');
    await user.type(screen.getByLabelText(/數量/i), '2.5');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 驗證計算正確性 (100 * 2.5 = 250)
    await waitFor(() => {
      expect(screen.getByText('NT$ 250')).toBeInTheDocument();
    });
  });

  it('handles special characters in text fields', async () => {
    render(<App />);

    // 測試特殊字符
    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店 & Co. (總店)');
    await user.type(screen.getByLabelText(/商店地址/i), '台北市信義區信義路五段7號 1F-A');
    await user.type(screen.getByLabelText(/客戶姓名/i), '王小明 Jr.');

    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.type(screen.getByLabelText(/項目名稱/i), 'Intel® Core™ i7-13700K (第13代)');
    await user.type(screen.getByLabelText(/單價/i), '12500');
    await user.type(screen.getByLabelText(/數量/i), '1');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 驗證特殊字符正確顯示
    await waitFor(() => {
      expect(screen.getByText('測試商店 & Co. (總店)')).toBeInTheDocument();
      expect(screen.getByText('台北市信義區信義路五段7號 1F-A')).toBeInTheDocument();
      expect(screen.getByText('王小明 Jr.')).toBeInTheDocument();
      expect(screen.getByText('Intel® Core™ i7-13700K (第13代)')).toBeInTheDocument();
    });
  });

  it('handles empty form submission attempts', async () => {
    render(<App />);

    // 嘗試新增空項目
    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 應該顯示驗證錯誤
    await waitFor(() => {
      expect(screen.getByText(/項目名稱為必填欄位/i)).toBeInTheDocument();
      expect(screen.getByText(/單價必須大於 0/i)).toBeInTheDocument();
    });

    // 取消新增
    await user.click(screen.getByRole('button', { name: /取消/i }));

    // 驗證回到原始狀態
    expect(screen.queryByLabelText(/項目名稱/i)).not.toBeInTheDocument();
  });

  it('maintains data consistency during rapid interactions', async () => {
    render(<App />);

    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');

    // 快速新增多個項目
    for (let i = 1; i <= 3; i++) {
      await user.click(screen.getByRole('button', { name: /新增項目/i }));
      await user.type(screen.getByLabelText(/項目名稱/i), `項目 ${i}`);
      await user.type(screen.getByLabelText(/單價/i), `${i * 1000}`);
      await user.type(screen.getByLabelText(/數量/i), '1');
      await user.click(screen.getByRole('button', { name: /儲存項目/i }));
    }

    // 驗證所有項目都正確新增
    await waitFor(() => {
      expect(screen.getByText('項目 1')).toBeInTheDocument();
      expect(screen.getByText('項目 2')).toBeInTheDocument();
      expect(screen.getByText('項目 3')).toBeInTheDocument();
    });

    // 驗證總計算正確 (1000 + 2000 + 3000 = 6000)
    await waitFor(() => {
      expect(screen.getByText('NT$ 6,000')).toBeInTheDocument();
    });
  });

  it('handles browser refresh simulation', async () => {
    render(<App />);

    // 填寫一些資料
    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');

    // 重新渲染組件 (模擬頁面刷新)
    const { rerender } = render(<App />);
    rerender(<App />);

    // 驗證資料重置為初始狀態
    expect(screen.getByDisplayValue('')).toBeInTheDocument(); // 商店名稱應該為空
    expect(screen.getByText('請先新增項目來查看收據預覽')).toBeInTheDocument();
  });
});