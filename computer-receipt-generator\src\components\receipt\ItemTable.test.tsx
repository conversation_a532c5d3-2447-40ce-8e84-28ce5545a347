import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { ItemTable } from './ItemTable';
import { ReceiptItem } from '../../types';

describe('ItemTable', () => {
  const mockItems: ReceiptItem[] = [
    {
      id: '1',
      name: 'Intel Core i7-12700K',
      category: 'hardware',
      price: 12000,
      quantity: 1,
    },
    {
      id: '2',
      name: '系統安裝服務',
      category: 'service',
      price: 500,
      quantity: 1,
    },
    {
      id: '3',
      name: 'Kingston DDR4 16GB',
      category: 'hardware',
      price: 2500,
      quantity: 2,
    },
  ];

  it('renders table headers correctly', () => {
    render(<ItemTable items={mockItems} />);

    expect(screen.getByText('項目名稱')).toBeInTheDocument();
    expect(screen.getByText('類別')).toBeInTheDocument();
    expect(screen.getByText('數量')).toBeInTheDocument();
    expect(screen.getByText('單價')).toBeInTheDocument();
    expect(screen.getByText('小計')).toBeInTheDocument();
  });

  it('renders all items correctly', () => {
    render(<ItemTable items={mockItems} />);

    // 檢查項目名稱
    expect(screen.getByText('Intel Core i7-12700K')).toBeInTheDocument();
    expect(screen.getByText('系統安裝服務')).toBeInTheDocument();
    expect(screen.getByText('Kingston DDR4 16GB')).toBeInTheDocument();

    // 檢查數量
    expect(screen.getAllByText('1')).toHaveLength(2); // 兩個項目數量為1
    expect(screen.getByText('2')).toBeInTheDocument(); // 一個項目數量為2

    // 檢查價格格式 - 使用 getAllByText 因為價格和小計可能相同
    expect(screen.getAllByText('$12,000.00')).toHaveLength(2); // 單價和小計
    expect(screen.getAllByText('$500.00')).toHaveLength(2); // 單價和小計
    expect(screen.getByText('$2,500.00')).toBeInTheDocument(); // 單價
  });

  it('calculates and displays subtotals correctly', () => {
    render(<ItemTable items={mockItems} />);

    // 檢查小計計算 - 使用 getAllByText 因為價格和小計可能相同
    expect(screen.getAllByText('$12,000.00')).toHaveLength(2); // 12000 * 1 (單價和小計相同)
    expect(screen.getAllByText('$500.00')).toHaveLength(2); // 500 * 1 (單價和小計相同)
    expect(screen.getByText('$5,000.00')).toBeInTheDocument(); // 2500 * 2 (只有小計)
  });

  it('displays category badges correctly', () => {
    render(<ItemTable items={mockItems} />);

    // 檢查類別顯示
    const hardwareBadges = screen.getAllByText('硬體');
    const serviceBadges = screen.getAllByText('服務');

    expect(hardwareBadges).toHaveLength(2); // Intel CPU 和 Kingston 記憶體
    expect(serviceBadges).toHaveLength(1); // 系統安裝服務
  });

  it('applies correct CSS classes for category badges', () => {
    const { container } = render(<ItemTable items={mockItems} />);

    const hardwareBadges = screen.getAllByText('硬體');
    const serviceBadge = screen.getByText('服務');

    // 檢查是否有 categoryBadge 相關的 CSS 類別（CSS modules 會產生 hash）
    hardwareBadges.forEach(badge => {
      expect(badge.className).toMatch(/categoryBadge/);
      expect(badge.className).toMatch(/categoryhardware/);
    });

    expect(serviceBadge.className).toMatch(/categoryBadge/);
    expect(serviceBadge.className).toMatch(/categoryservice/);
  });

  it('renders empty state when no items', () => {
    render(<ItemTable items={[]} />);

    expect(screen.getByText('尚未新增任何項目')).toBeInTheDocument();
    expect(screen.queryByRole('table')).not.toBeInTheDocument();
  });

  it('applies alternating row styles', () => {
    render(<ItemTable items={mockItems} />);

    const rows = screen.getAllByRole('row');
    // 第一行是標題行，所以資料行從索引1開始
    const dataRows = rows.slice(1);

    // 檢查 CSS 類別是否包含相應的樣式（CSS modules 會產生 hash）
    expect(dataRows[0].className).toMatch(/evenRow/);
    expect(dataRows[1].className).toMatch(/oddRow/);
    expect(dataRows[2].className).toMatch(/evenRow/);
  });

  it('handles single item correctly', () => {
    const singleItem: ReceiptItem[] = [
      {
        id: '1',
        name: 'NVIDIA RTX 4080',
        category: 'hardware',
        price: 35000,
        quantity: 1,
      },
    ];

    render(<ItemTable items={singleItem} />);

    expect(screen.getByText('NVIDIA RTX 4080')).toBeInTheDocument();
    expect(screen.getByText('硬體')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
    // 價格和小計相同時會出現兩次
    expect(screen.getAllByText('$35,000.00')).toHaveLength(2);
  });

  it('handles items with decimal prices correctly', () => {
    const itemsWithDecimals: ReceiptItem[] = [
      {
        id: '1',
        name: '清潔服務',
        category: 'service',
        price: 99.99,
        quantity: 1,
      },
    ];

    render(<ItemTable items={itemsWithDecimals} />);

    // 價格和小計相同時會出現兩次
    expect(screen.getAllByText('$99.99')).toHaveLength(2);
  });

  it('handles large quantities correctly', () => {
    const itemWithLargeQuantity: ReceiptItem[] = [
      {
        id: '1',
        name: 'USB 線材',
        category: 'hardware',
        price: 50,
        quantity: 10,
      },
    ];

    render(<ItemTable items={itemWithLargeQuantity} />);

    expect(screen.getByText('10')).toBeInTheDocument();
    expect(screen.getByText('$500.00')).toBeInTheDocument(); // 50 * 10
  });
});