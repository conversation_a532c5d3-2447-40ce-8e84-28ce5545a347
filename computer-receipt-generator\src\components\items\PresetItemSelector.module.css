.container {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.titleSection {
  flex: 1;
}

.title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.toggleButton {
  flex-shrink: 0;
  margin-left: 1rem;
}

.content {
  padding: 1.5rem;
  animation: slideDown 0.3s ease-out;
}

.categoryFilter {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.categoryButton {
  white-space: nowrap;
}

/* List View Styles */
.itemsList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.itemRow {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
}

.itemRow:hover {
  border-color: #6366f1;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
  background: #ffffff;
}

.itemInfo {
  flex: 1;
  min-width: 0;
}

.itemHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.itemName {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.9rem;
  margin-right: 1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.itemMeta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.itemCategory {
  font-size: 0.75rem;
  color: #6b7280;
  background: #f3f4f6;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  white-space: nowrap;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.customBadge {
  background: #6366f1;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

.itemDescription {
  font-size: 0.8rem;
  color: #6b7280;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 0.25rem;
}

.itemPrice {
  text-align: right;
  flex-shrink: 0;
  margin-right: 1rem;
}

.currentPrice {
  font-weight: 600;
  color: #059669;
  font-size: 0.9rem;
  display: block;
}

.originalPriceContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.25rem;
  justify-content: flex-end;
}

.originalPrice {
  font-size: 0.8rem;
  color: #9ca3af;
  text-decoration: line-through;
}

.discountBadge {
  background: #dc2626;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

.itemActions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.editButton,
.deleteButton,
.addButton {
  /* Inherits from Button component */
}

/* Edit Form Styles */
.editForm {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.discountInfo {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 0.5rem;
}

.discountLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #0369a1;
  margin-bottom: 0.5rem;
}

.discountDetails {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.discountAmount {
  font-size: 0.875rem;
  font-weight: 600;
  color: #059669;
}

.discountPercent {
  font-size: 0.75rem;
  font-weight: 500;
  color: #dc2626;
  background: #fee2e2;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .itemRow {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .itemHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .itemName {
    margin-right: 0;
    white-space: normal;
  }
  
  .itemPrice {
    text-align: left;
    margin-right: 0;
  }
  
  .itemActions {
    justify-content: stretch;
  }
  
  .itemActions button {
    flex: 1;
  }
  
  .formRow {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .discountDetails {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* 動畫效果 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滾動條樣式 */
.itemsList::-webkit-scrollbar {
  width: 6px;
}

.itemsList::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.itemsList::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.itemsList::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 高對比模式支援 */
@media (prefers-contrast: high) {
  .itemRow {
    border-width: 2px;
  }
  
  .itemRow:hover {
    border-width: 3px;
  }
  
  .discountInfo {
    border-width: 2px;
  }
}
