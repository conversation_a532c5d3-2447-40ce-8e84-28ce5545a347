.row {
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s ease;
}

.row:hover {
  background-color: #f9fafb;
}

.row:last-child {
  border-bottom: none;
}

.nameCell {
  padding: 1rem;
  vertical-align: top;
}

.itemName {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.itemCategory {
  font-size: 0.875rem;
  color: #6b7280;
  background-color: #f3f4f6;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.discountBadge {
  background: #dc2626;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  white-space: nowrap;
}

.priceContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.currentPrice {
  font-weight: 600;
  color: #059669;
}

.originalPrice {
  font-size: 0.8rem;
  color: #9ca3af;
  text-decoration: line-through;
  font-weight: 400;
}

.hiddenPriceIndicator {
  color: #dc2626;
  font-weight: 500;
  font-size: 0.7rem;
}

.priceCell,
.quantityCell,
.subtotalCell {
  padding: 1rem;
  text-align: right;
  vertical-align: middle;
  font-weight: 500;
  color: #374151;
}

.subtotalCell {
  font-weight: 600;
  color: #059669;
}

.actionsCell {
  padding: 1rem;
  vertical-align: middle;
  width: 200px;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
}

.moveButtons {
  display: flex;
  gap: 0.25rem;
}

.editButtons {
  display: flex;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .row {
    display: block;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 1rem;
    background-color: #ffffff;
  }

  .row:hover {
    background-color: #ffffff;
  }

  .nameCell,
  .priceCell,
  .quantityCell,
  .subtotalCell,
  .actionsCell {
    display: block;
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #f3f4f6;
  }

  .actionsCell {
    border-bottom: none;
  }

  .priceCell::before,
  .quantityCell::before,
  .subtotalCell::before {
    content: attr(data-label);
    font-weight: 600;
    color: #6b7280;
    display: inline-block;
    width: 80px;
  }

  .actions {
    justify-content: flex-start;
  }
}