// Test configuration and constants

export const TEST_TIMEOUT = 5000;

export const TEST_IDS = {
  // Form elements
  STORE_NAME_INPUT: 'store-name-input',
  STORE_ADDRESS_INPUT: 'store-address-input',
  STORE_PHONE_INPUT: 'store-phone-input',
  STORE_EMAIL_INPUT: 'store-email-input',
  CUSTOMER_NAME_INPUT: 'customer-name-input',
  CUSTOMER_PHONE_INPUT: 'customer-phone-input',
  CUSTOMER_EMAIL_INPUT: 'customer-email-input',
  
  // Item form elements
  ITEM_NAME_INPUT: 'item-name-input',
  ITEM_CATEGORY_SELECT: 'item-category-select',
  ITEM_PRICE_INPUT: 'item-price-input',
  ITEM_QUANTITY_INPUT: 'item-quantity-input',
  ADD_ITEM_BUTTON: 'add-item-button',
  SAVE_ITEM_BUTTON: 'save-item-button',
  CANCEL_ITEM_BUTTON: 'cancel-item-button',
  
  // Item list elements
  ITEM_ROW: 'item-row',
  EDIT_ITEM_BUTTON: 'edit-item-button',
  DELETE_ITEM_BUTTON: 'delete-item-button',
  
  // Receipt elements
  RECEIPT_PREVIEW: 'receipt-preview',
  RECEIPT_HEADER: 'receipt-header',
  RECEIPT_BODY: 'receipt-body',
  RECEIPT_FOOTER: 'receipt-footer',
  ITEM_TABLE: 'item-table',
  
  // Action buttons
  PRINT_BUTTON: 'print-button',
  SAVE_PDF_BUTTON: 'save-pdf-button',
  
  // Tax rate
  TAX_RATE_INPUT: 'tax-rate-input',
} as const;

export const TEST_CLASSES = {
  ERROR_MESSAGE: 'error-message',
  SUCCESS_MESSAGE: 'success-message',
  LOADING_SPINNER: 'loading-spinner',
  EMPTY_STATE: 'empty-state',
} as const;

export const VALIDATION_MESSAGES = {
  REQUIRED_FIELD: '此欄位為必填',
  INVALID_EMAIL: '請輸入有效的電子郵件地址',
  INVALID_PHONE: '請輸入有效的電話號碼',
  INVALID_PRICE: '價格必須為正數',
  INVALID_QUANTITY: '數量必須為正整數',
} as const;

export const MOCK_VALUES = {
  VALID_EMAIL: '<EMAIL>',
  INVALID_EMAIL: 'invalid-email',
  VALID_PHONE: '(*************',
  INVALID_PHONE: '123',
  VALID_PRICE: '99.99',
  INVALID_PRICE: '-10',
  VALID_QUANTITY: '2',
  INVALID_QUANTITY: '0',
} as const;