import React from 'react';
import { Input } from '../ui';
import { useReceipt } from '../../hooks/useReceipt';
import { useValidation } from '../../hooks/useValidation';
import styles from './StoreInfoSection.module.css';

export const StoreInfoSection: React.FC = () => {
  const { receiptData, updateStoreInfo } = useReceipt();
  const {
    validateStoreInfoForm,
    getFieldError,
    validatePhoneField,
    validateEmailField
  } = useValidation();

  const handleInputChange = (field: keyof typeof receiptData.storeInfo, value: string) => {
    updateStoreInfo({ [field]: value });

    // 即時驗證 - 所有欄位都是選填
    switch (field) {
      case 'phone':
        validatePhoneField('storePhone', value);
        break;
      case 'email':
        validateEmailField('storeEmail', value);
        break;
    }
  };

  const handleBlur = () => {
    // 當失去焦點時驗證整個表單
    validateStoreInfoForm(receiptData.storeInfo);
  };

  return (
    <div className={styles.section}>
      <div className={styles.header}>
        <h2>Store Information</h2>
        <p className={styles.description}>
          Please fill in your store's basic information, which will be displayed on the receipt
        </p>
      </div>

      <div className={styles.formGrid}>
        <Input
          label="Store Name"
          value={receiptData.storeInfo.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          onBlur={handleBlur}
          error={getFieldError('storeName')}
          placeholder="Enter store name"
          fullWidth
        />

        <Input
          label="Store Address"
          value={receiptData.storeInfo.address}
          onChange={(e) => handleInputChange('address', e.target.value)}
          onBlur={handleBlur}
          error={getFieldError('storeAddress')}
          placeholder="Enter store address"
          fullWidth
        />

        <Input
          label="Contact Phone"
          type="tel"
          value={receiptData.storeInfo.phone}
          onChange={(e) => handleInputChange('phone', e.target.value)}
          onBlur={handleBlur}
          error={getFieldError('storePhone')}
          placeholder="e.g.: 02-12345678"
          helperText="Supports multiple formats: 02-12345678, 0912-345-678"
          fullWidth
        />

        <Input
          label="Email"
          type="email"
          value={receiptData.storeInfo.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          onBlur={handleBlur}
          error={getFieldError('storeEmail')}
          placeholder="e.g.: <EMAIL>"
          helperText="Optional, for customer contact"
          fullWidth
        />
      </div>

      <div className={styles.preview}>
        <h3>Preview</h3>
        <div className={styles.previewCard}>
          <div className={styles.storeName}>
            {receiptData.storeInfo.name || 'Store Name'}
          </div>
          <div className={styles.storeDetails}>
            <div>{receiptData.storeInfo.address || 'Store Address'}</div>
            {receiptData.storeInfo.phone && (
              <div>Phone: {receiptData.storeInfo.phone}</div>
            )}
            {receiptData.storeInfo.email && (
              <div>Email: {receiptData.storeInfo.email}</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};