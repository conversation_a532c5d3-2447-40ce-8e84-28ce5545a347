import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { ReceiptForm } from './ReceiptForm';
import { AppProvider } from '../../context/AppContext';

// Mock the child components
vi.mock('../info/StoreInfoSection', () => ({
  StoreInfoSection: () => <div data-testid="store-info-section">Store Info Section</div>
}));

vi.mock('../info/CustomerInfoSection', () => ({
  CustomerInfoSection: () => <div data-testid="customer-info-section">Customer Info Section</div>
}));

vi.mock('../items/ItemList', () => ({
  ItemList: () => <div data-testid="item-list">Item List</div>
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <AppProvider>
      {component}
    </AppProvider>
  );
};

describe('ReceiptForm', () => {
  it('renders all main sections', () => {
    renderWithProvider(<ReceiptForm />);
    
    // 檢查標題
    expect(screen.getByText('收據生成器')).toBeInTheDocument();
    expect(screen.getByText('填寫以下資訊來生成專業的電腦銷售收據')).toBeInTheDocument();
    
    // 檢查所有區塊都有渲染
    expect(screen.getByTestId('store-info-section')).toBeInTheDocument();
    expect(screen.getByTestId('customer-info-section')).toBeInTheDocument();
    expect(screen.getByTestId('item-list')).toBeInTheDocument();
    
    // 檢查稅率設定區塊
    expect(screen.getByText('稅率設定')).toBeInTheDocument();
    expect(screen.getByLabelText('稅率 (%)')).toBeInTheDocument();
  });

  it('displays tax rate input with correct attributes', () => {
    renderWithProvider(<ReceiptForm />);
    
    const taxRateInput = screen.getByLabelText('稅率 (%)');
    
    expect(taxRateInput).toHaveAttribute('type', 'number');
    expect(taxRateInput).toHaveAttribute('min', '0');
    expect(taxRateInput).toHaveAttribute('max', '100');
    expect(taxRateInput).toHaveAttribute('step', '0.1');
    expect(taxRateInput).toHaveAttribute('placeholder', '例如：5');
  });

  it('shows default tax rate value', () => {
    renderWithProvider(<ReceiptForm />);
    
    const taxRateInput = screen.getByLabelText('稅率 (%)') as HTMLInputElement;
    expect(taxRateInput.value).toBe('5'); // 預設稅率應該是 5%
    
    // 檢查預覽顯示
    expect(screen.getByText('5%')).toBeInTheDocument();
  });

  it('updates tax rate when input changes', async () => {
    renderWithProvider(<ReceiptForm />);
    
    const taxRateInput = screen.getByLabelText('稅率 (%)');
    
    // 更改稅率
    fireEvent.change(taxRateInput, { target: { value: '8.5' } });
    
    await waitFor(() => {
      expect((taxRateInput as HTMLInputElement).value).toBe('8.5');
      expect(screen.getByText('8.5%')).toBeInTheDocument();
    });
  });

  it('handles invalid tax rate input', async () => {
    renderWithProvider(<ReceiptForm />);
    
    const taxRateInput = screen.getByLabelText('稅率 (%)');
    
    // 輸入無效的稅率（負數）
    fireEvent.change(taxRateInput, { target: { value: '-5' } });
    
    await waitFor(() => {
      // 應該顯示錯誤訊息（假設驗證函數會處理）
      expect((taxRateInput as HTMLInputElement).value).toBe('-5');
    });
  });

  it('handles empty tax rate input', async () => {
    renderWithProvider(<ReceiptForm />);
    
    const taxRateInput = screen.getByLabelText('稅率 (%)');
    
    // 清空輸入
    fireEvent.change(taxRateInput, { target: { value: '' } });
    
    await waitFor(() => {
      // 當輸入為空時，parseFloat會返回NaN，然後 || 0 會設為0，所以狀態會變成0
      // 但是輸入框的值會被React重新設定為'0'
      expect((taxRateInput as HTMLInputElement).value).toBe('0');
      // 當輸入為空時，應該顯示 0%
      expect(screen.getByText('0%')).toBeInTheDocument();
    });
  });

  it('displays helper text for tax rate input', () => {
    renderWithProvider(<ReceiptForm />);
    
    expect(screen.getByText('輸入稅率百分比，例如 5 代表 5%')).toBeInTheDocument();
  });

  it('has proper section structure and styling', () => {
    renderWithProvider(<ReceiptForm />);
    
    // 檢查主容器存在
    const title = screen.getByText('收據生成器');
    expect(title).toBeInTheDocument();
    
    // 檢查各個區塊都在 section 元素中
    const sections = document.querySelectorAll('section');
    expect(sections).toHaveLength(4); // 商店資訊、客戶資訊、項目列表、稅率設定
  });

  it('integrates with validation system', async () => {
    renderWithProvider(<ReceiptForm />);
    
    const taxRateInput = screen.getByLabelText('稅率 (%)');
    
    // 輸入超出範圍的值
    fireEvent.change(taxRateInput, { target: { value: '150' } });
    
    await waitFor(() => {
      // 驗證系統應該會被調用（這裡我們只能測試輸入值有被設定）
      expect((taxRateInput as HTMLInputElement).value).toBe('150');
    });
  });

  it('maintains tax rate preview consistency', async () => {
    renderWithProvider(<ReceiptForm />);
    
    const taxRateInput = screen.getByLabelText('稅率 (%)');
    
    // 測試多個不同的稅率值
    const testValues = ['0', '5', '10.5', '15'];
    
    for (const value of testValues) {
      fireEvent.change(taxRateInput, { target: { value } });
      
      await waitFor(() => {
        expect(screen.getByText(`${value}%`)).toBeInTheDocument();
      });
    }
  });

  it('has accessible form structure', () => {
    renderWithProvider(<ReceiptForm />);
    
    // 檢查標題層級結構
    expect(screen.getByRole('heading', { level: 1, name: '收據生成器' })).toBeInTheDocument();
    expect(screen.getByRole('heading', { level: 2, name: '稅率設定' })).toBeInTheDocument();
    
    // 檢查表單標籤關聯
    const taxRateInput = screen.getByLabelText('稅率 (%)');
    expect(taxRateInput).toBeInTheDocument();
  });
});