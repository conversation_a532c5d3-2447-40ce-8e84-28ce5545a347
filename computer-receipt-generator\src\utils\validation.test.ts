import { describe, it, expect } from 'vitest';
import {
  validatePrice,
  validateRequired,
  validateEmail,
  validatePhone,
  validateQuantity,
  validateTaxRate,
  getPriceErrorMessage,
  getRequiredErrorMessage,
  getEmailErrorMessage,
  getPhoneErrorMessage,
  getQuantityErrorMessage,
  getTaxRateErrorMessage,
} from './validation';

describe('validatePrice', () => {
  it('應該接受有效的正數價格', () => {
    expect(validatePrice(10)).toBe(true);
    expect(validatePrice(10.5)).toBe(true);
    expect(validatePrice(10.99)).toBe(true);
    expect(validatePrice('10')).toBe(true);
    expect(validatePrice('10.5')).toBe(true);
  });

  it('應該拒絕無效的價格', () => {
    expect(validatePrice(0)).toBe(false);
    expect(validatePrice(-10)).toBe(false);
    expect(validatePrice(NaN)).toBe(false);
    expect(validatePrice(Infinity)).toBe(false);
    expect(validatePrice('abc')).toBe(false);
    expect(validatePrice('')).toBe(false);
  });

  it('應該拒絕超過兩位小數的價格', () => {
    expect(validatePrice(10.999)).toBe(false);
    expect(validatePrice('10.999')).toBe(false);
  });
});

describe('validateRequired', () => {
  it('應該接受有效的必填值', () => {
    expect(validateRequired('test')).toBe(true);
    expect(validateRequired('  test  ')).toBe(true);
    expect(validateRequired(10)).toBe(true);
    expect(validateRequired(0)).toBe(true);
  });

  it('應該拒絕空值', () => {
    expect(validateRequired('')).toBe(false);
    expect(validateRequired('   ')).toBe(false);
    expect(validateRequired(null)).toBe(false);
    expect(validateRequired(undefined)).toBe(false);
    expect(validateRequired(NaN)).toBe(false);
  });
});

describe('validateEmail', () => {
  it('應該接受有效的電子郵件', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('<EMAIL>')).toBe(true);
  });

  it('應該拒絕無效的電子郵件', () => {
    expect(validateEmail('')).toBe(false);
    expect(validateEmail('invalid')).toBe(false);
    expect(validateEmail('test@')).toBe(false);
    expect(validateEmail('@example.com')).toBe(false);
    expect(validateEmail('test@example')).toBe(false);
    expect(validateEmail('test.example.com')).toBe(false);
  });
});

describe('validatePhone', () => {
  it('應該接受有效的電話號碼', () => {
    expect(validatePhone('0912345678')).toBe(true);
    expect(validatePhone('+886912345678')).toBe(true);
    expect(validatePhone('02-12345678')).toBe(true);
    expect(validatePhone('(02) 1234-5678')).toBe(true);
    expect(validatePhone('************')).toBe(true);
  });

  it('應該拒絕無效的電話號碼', () => {
    expect(validatePhone('')).toBe(false);
    expect(validatePhone('123')).toBe(false);
    expect(validatePhone('abc123')).toBe(false);
    expect(validatePhone('12345678901234567')).toBe(false);
  });
});

describe('validateQuantity', () => {
  it('應該接受有效的數量', () => {
    expect(validateQuantity(1)).toBe(true);
    expect(validateQuantity(10)).toBe(true);
    expect(validateQuantity('5')).toBe(true);
    expect(validateQuantity('100')).toBe(true);
  });

  it('應該拒絕無效的數量', () => {
    expect(validateQuantity(0)).toBe(false);
    expect(validateQuantity(-1)).toBe(false);
    expect(validateQuantity(1.5)).toBe(false);
    expect(validateQuantity('1.5')).toBe(false);
    expect(validateQuantity('abc')).toBe(false);
    expect(validateQuantity('')).toBe(false);
  });
});

describe('validateTaxRate', () => {
  it('應該接受有效的稅率', () => {
    expect(validateTaxRate(0)).toBe(true);
    expect(validateTaxRate(5)).toBe(true);
    expect(validateTaxRate(10.5)).toBe(true);
    expect(validateTaxRate(100)).toBe(true);
    expect(validateTaxRate('5')).toBe(true);
    expect(validateTaxRate('10.5')).toBe(true);
  });

  it('應該拒絕無效的稅率', () => {
    expect(validateTaxRate(-1)).toBe(false);
    expect(validateTaxRate(101)).toBe(false);
    expect(validateTaxRate('abc')).toBe(false);
    expect(validateTaxRate('')).toBe(false);
  });
});

describe('錯誤訊息函數', () => {
  it('getPriceErrorMessage 應該返回正確的錯誤訊息', () => {
    expect(getPriceErrorMessage('abc')).toBe('請輸入有效的數字');
    expect(getPriceErrorMessage(-10)).toBe('價格必須大於 0');
    expect(getPriceErrorMessage(10.999)).toBe('價格最多只能有兩位小數');
    expect(getPriceErrorMessage(10.5)).toBe('');
  });

  it('getRequiredErrorMessage 應該返回正確的錯誤訊息', () => {
    expect(getRequiredErrorMessage('項目名稱')).toBe('項目名稱為必填欄位');
  });

  it('getEmailErrorMessage 應該返回正確的錯誤訊息', () => {
    expect(getEmailErrorMessage()).toBe('請輸入有效的電子郵件格式');
  });

  it('getPhoneErrorMessage 應該返回正確的錯誤訊息', () => {
    expect(getPhoneErrorMessage()).toBe('請輸入有效的電話號碼');
  });

  it('getQuantityErrorMessage 應該返回正確的錯誤訊息', () => {
    expect(getQuantityErrorMessage('abc')).toBe('請輸入有效的數字');
    expect(getQuantityErrorMessage(-1)).toBe('數量必須大於 0');
    expect(getQuantityErrorMessage(1.5)).toBe('數量必須為整數');
    expect(getQuantityErrorMessage(5)).toBe('');
  });

  it('getTaxRateErrorMessage 應該返回正確的錯誤訊息', () => {
    expect(getTaxRateErrorMessage('abc')).toBe('請輸入有效的數字');
    expect(getTaxRateErrorMessage(-1)).toBe('稅率必須在 0-100% 之間');
    expect(getTaxRateErrorMessage(101)).toBe('稅率必須在 0-100% 之間');
    expect(getTaxRateErrorMessage(5)).toBe('');
  });
});