import { describe, it, expect } from 'vitest';
import { appReducer } from './AppContext';
import { AppState, ReceiptItem } from '../types';

// 測試用的初始狀態
const mockInitialState: AppState = {
  receiptData: {
    receiptNumber: 'RCP-TEST-001',
    date: new Date('2024-01-01'),
    storeInfo: {
      name: 'Test Store',
      address: 'Test Address',
      phone: '0912345678',
      email: '<EMAIL>',
    },
    customerInfo: {
      name: 'Test Customer',
      phone: '0987654321',
      email: '<EMAIL>',
    },
    items: [],
    taxRate: 5,
    subtotal: 0,
    taxAmount: 0,
    total: 0,
  },
  isEditing: false,
  editingItemId: null,
};

const mockItem: ReceiptItem = {
  id: '1',
  name: 'Test CPU',
  category: 'hardware',
  price: 10000,
  quantity: 1,
};

describe('appReducer', () => {
  describe('ADD_ITEM', () => {
    it('應該正確新增項目到收據', () => {
      const action = {
        type: 'ADD_ITEM' as const,
        payload: mockItem,
      };

      const newState = appReducer(mockInitialState, action);

      expect(newState.receiptData.items).toHaveLength(1);
      expect(newState.receiptData.items[0]).toEqual(mockItem);
      expect(newState.receiptData.items).not.toBe(mockInitialState.receiptData.items);
    });

    it('應該保持其他狀態不變', () => {
      const action = {
        type: 'ADD_ITEM' as const,
        payload: mockItem,
      };

      const newState = appReducer(mockInitialState, action);

      expect(newState.receiptData.storeInfo).toBe(mockInitialState.receiptData.storeInfo);
      expect(newState.receiptData.customerInfo).toBe(mockInitialState.receiptData.customerInfo);
      expect(newState.isEditing).toBe(mockInitialState.isEditing);
    });
  });

  describe('UPDATE_ITEM', () => {
    it('應該正確更新指定的項目', () => {
      const stateWithItem: AppState = {
        ...mockInitialState,
        receiptData: {
          ...mockInitialState.receiptData,
          items: [mockItem],
        },
      };

      const action = {
        type: 'UPDATE_ITEM' as const,
        payload: {
          id: '1',
          item: { name: 'Updated CPU', price: 12000 },
        },
      };

      const newState = appReducer(stateWithItem, action);

      expect(newState.receiptData.items[0].name).toBe('Updated CPU');
      expect(newState.receiptData.items[0].price).toBe(12000);
      expect(newState.receiptData.items[0].id).toBe('1');
      expect(newState.receiptData.items[0].category).toBe('hardware');
      expect(newState.receiptData.items[0].quantity).toBe(1);
    });

    it('應該只更新指定 ID 的項目', () => {
      const item2: ReceiptItem = {
        id: '2',
        name: 'Test RAM',
        category: 'hardware',
        price: 2000,
        quantity: 2,
      };

      const stateWithItems: AppState = {
        ...mockInitialState,
        receiptData: {
          ...mockInitialState.receiptData,
          items: [mockItem, item2],
        },
      };

      const action = {
        type: 'UPDATE_ITEM' as const,
        payload: {
          id: '1',
          item: { name: 'Updated CPU' },
        },
      };

      const newState = appReducer(stateWithItems, action);

      expect(newState.receiptData.items[0].name).toBe('Updated CPU');
      expect(newState.receiptData.items[1]).toEqual(item2);
    });
  });

  describe('DELETE_ITEM', () => {
    it('應該正確刪除指定的項目', () => {
      const stateWithItem: AppState = {
        ...mockInitialState,
        receiptData: {
          ...mockInitialState.receiptData,
          items: [mockItem],
        },
      };

      const action = {
        type: 'DELETE_ITEM' as const,
        payload: '1',
      };

      const newState = appReducer(stateWithItem, action);

      expect(newState.receiptData.items).toHaveLength(0);
    });

    it('應該只刪除指定 ID 的項目', () => {
      const item2: ReceiptItem = {
        id: '2',
        name: 'Test RAM',
        category: 'hardware',
        price: 2000,
        quantity: 2,
      };

      const stateWithItems: AppState = {
        ...mockInitialState,
        receiptData: {
          ...mockInitialState.receiptData,
          items: [mockItem, item2],
        },
      };

      const action = {
        type: 'DELETE_ITEM' as const,
        payload: '1',
      };

      const newState = appReducer(stateWithItems, action);

      expect(newState.receiptData.items).toHaveLength(1);
      expect(newState.receiptData.items[0]).toEqual(item2);
    });
  });

  describe('UPDATE_STORE_INFO', () => {
    it('應該正確更新商店資訊', () => {
      const action = {
        type: 'UPDATE_STORE_INFO' as const,
        payload: {
          name: 'Updated Store',
          phone: '0911111111',
        },
      };

      const newState = appReducer(mockInitialState, action);

      expect(newState.receiptData.storeInfo.name).toBe('Updated Store');
      expect(newState.receiptData.storeInfo.phone).toBe('0911111111');
      expect(newState.receiptData.storeInfo.address).toBe('Test Address');
      expect(newState.receiptData.storeInfo.email).toBe('<EMAIL>');
    });
  });

  describe('UPDATE_CUSTOMER_INFO', () => {
    it('應該正確更新客戶資訊', () => {
      const action = {
        type: 'UPDATE_CUSTOMER_INFO' as const,
        payload: {
          name: 'Updated Customer',
          email: '<EMAIL>',
        },
      };

      const newState = appReducer(mockInitialState, action);

      expect(newState.receiptData.customerInfo.name).toBe('Updated Customer');
      expect(newState.receiptData.customerInfo.email).toBe('<EMAIL>');
      expect(newState.receiptData.customerInfo.phone).toBe('0987654321');
    });
  });

  describe('SET_TAX_RATE', () => {
    it('應該正確設定稅率', () => {
      const action = {
        type: 'SET_TAX_RATE' as const,
        payload: 10,
      };

      const newState = appReducer(mockInitialState, action);

      expect(newState.receiptData.taxRate).toBe(10);
    });
  });

  describe('SET_EDITING', () => {
    it('應該正確設定編輯狀態', () => {
      const action = {
        type: 'SET_EDITING' as const,
        payload: {
          isEditing: true,
          itemId: '1',
        },
      };

      const newState = appReducer(mockInitialState, action);

      expect(newState.isEditing).toBe(true);
      expect(newState.editingItemId).toBe('1');
    });

    it('應該正確清除編輯狀態', () => {
      const editingState: AppState = {
        ...mockInitialState,
        isEditing: true,
        editingItemId: '1',
      };

      const action = {
        type: 'SET_EDITING' as const,
        payload: {
          isEditing: false,
        },
      };

      const newState = appReducer(editingState, action);

      expect(newState.isEditing).toBe(false);
      expect(newState.editingItemId).toBe(null);
    });
  });

  describe('未知動作', () => {
    it('應該返回原始狀態', () => {
      const unknownAction = {
        type: 'UNKNOWN_ACTION' as any,
        payload: 'test',
      };

      const newState = appReducer(mockInitialState, unknownAction);

      expect(newState).toBe(mockInitialState);
    });
  });
});