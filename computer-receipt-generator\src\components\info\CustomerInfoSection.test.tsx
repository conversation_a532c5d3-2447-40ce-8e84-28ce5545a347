import { describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { CustomerInfoSection } from './CustomerInfoSection';
import { AppProvider } from '../../context/AppContext';

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <AppProvider>
      {component}
    </AppProvider>
  );
};

describe('CustomerInfoSection', () => {
  it('應該正確渲染客戶資訊表單', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    expect(screen.getByText('客戶資訊')).toBeInTheDocument();
    expect(screen.getByLabelText('客戶姓名')).toBeInTheDocument();
    expect(screen.getByLabelText('聯絡電話')).toBeInTheDocument();
    expect(screen.getByLabelText('電子郵件')).toBeInTheDocument();
  });

  it('應該顯示描述文字', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    expect(screen.getByText('請填寫客戶的基本資訊，這些資訊將顯示在收據上')).toBeInTheDocument();
  });

  it('應該正確處理輸入變更', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    const nameInput = screen.getByLabelText('客戶姓名');
    const phoneInput = screen.getByLabelText('聯絡電話');
    const emailInput = screen.getByLabelText('電子郵件');
    
    fireEvent.change(nameInput, { target: { value: 'John Doe' } });
    fireEvent.change(phoneInput, { target: { value: '0912-345-678' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    
    expect(nameInput).toHaveValue('John Doe');
    expect(phoneInput).toHaveValue('0912-345-678');
    expect(emailInput).toHaveValue('<EMAIL>');
  });

  it('應該顯示預覽區域', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    expect(screen.getByText('預覽')).toBeInTheDocument();
    expect(screen.getByText('客戶: 客戶姓名')).toBeInTheDocument(); // 預設預覽文字
  });

  it('應該在預覽中顯示輸入的資訊', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    const nameInput = screen.getByLabelText('客戶姓名');
    const phoneInput = screen.getByLabelText('聯絡電話');
    const emailInput = screen.getByLabelText('電子郵件');
    
    fireEvent.change(nameInput, { target: { value: 'Jane Smith' } });
    fireEvent.change(phoneInput, { target: { value: '**********' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    
    expect(screen.getByText('客戶: Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('電話: **********')).toBeInTheDocument();
    expect(screen.getByText('信箱: <EMAIL>')).toBeInTheDocument();
  });

  it('應該顯示輔助文字', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    expect(screen.getByText('選填，用於後續聯絡')).toBeInTheDocument();
    expect(screen.getByText('選填，用於發送電子收據')).toBeInTheDocument();
  });

  it('應該正確標示必填欄位', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    const nameInput = screen.getByLabelText('客戶姓名');
    const phoneInput = screen.getByLabelText('聯絡電話');
    const emailInput = screen.getByLabelText('電子郵件');
    
    expect(nameInput).toBeRequired();
    expect(phoneInput).not.toBeRequired();
    expect(emailInput).not.toBeRequired();
  });

  it('應該有正確的輸入類型', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    const phoneInput = screen.getByLabelText('聯絡電話');
    const emailInput = screen.getByLabelText('電子郵件');
    
    expect(phoneInput).toHaveAttribute('type', 'tel');
    expect(emailInput).toHaveAttribute('type', 'email');
  });

  it('應該顯示正確的佔位符文字', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    expect(screen.getByPlaceholderText('輸入客戶姓名')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('例如：0912-345-678')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('例如：<EMAIL>')).toBeInTheDocument();
  });

  it('應該在沒有選填資訊時不顯示在預覽中', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    const nameInput = screen.getByLabelText('客戶姓名');
    
    fireEvent.change(nameInput, { target: { value: 'Test Customer' } });
    
    // 電話和郵件沒有輸入，不應該在預覽中顯示
    expect(screen.queryByText(/電話:/)).not.toBeInTheDocument();
    expect(screen.queryByText(/信箱:/)).not.toBeInTheDocument();
  });

  it('應該只在有值時顯示選填資訊', () => {
    renderWithProvider(<CustomerInfoSection />);
    
    const nameInput = screen.getByLabelText('客戶姓名');
    const phoneInput = screen.getByLabelText('聯絡電話');
    
    fireEvent.change(nameInput, { target: { value: 'Test Customer' } });
    fireEvent.change(phoneInput, { target: { value: '**********' } });
    
    // 只有電話有值，應該顯示電話但不顯示郵件
    expect(screen.getByText('電話: **********')).toBeInTheDocument();
    expect(screen.queryByText(/信箱:/)).not.toBeInTheDocument();
  });
});