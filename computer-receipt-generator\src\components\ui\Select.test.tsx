import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Select } from './Select';

const mockOptions = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3', disabled: true },
];

describe('Select', () => {
  it('應該正確渲染基本選擇框', () => {
    render(<Select options={mockOptions} />);
    
    const select = screen.getByRole('combobox');
    expect(select).toBeInTheDocument();
  });

  it('應該顯示所有選項', () => {
    render(<Select options={mockOptions} />);
    
    const option1 = screen.getByRole('option', { name: 'Option 1' });
    const option2 = screen.getByRole('option', { name: 'Option 2' });
    const option3 = screen.getByRole('option', { name: 'Option 3' });
    
    expect(option1).toBeInTheDocument();
    expect(option2).toBeInTheDocument();
    expect(option3).toBeInTheDocument();
  });

  it('應該顯示標籤', () => {
    render(<Select label="Choose option" options={mockOptions} />);
    
    const label = screen.getByText('Choose option');
    const select = screen.getByLabelText('Choose option');
    
    expect(label).toBeInTheDocument();
    expect(select).toBeInTheDocument();
  });

  it('應該顯示佔位符', () => {
    render(<Select options={mockOptions} placeholder="Select an option" />);
    
    const placeholder = screen.getByRole('option', { name: 'Select an option' });
    expect(placeholder).toBeInTheDocument();
    expect(placeholder).toHaveAttribute('value', '');
    expect(placeholder).toBeDisabled();
  });

  it('應該顯示錯誤訊息', () => {
    render(<Select label="Category" options={mockOptions} error="Please select a category" />);
    
    const select = screen.getByLabelText('Category');
    const errorMessage = screen.getByText('Please select a category');
    
    expect(select.className).toContain('error');
    expect(errorMessage).toBeInTheDocument();
    expect(errorMessage.className).toContain('errorText');
  });

  it('應該顯示輔助文字', () => {
    render(<Select label="Priority" options={mockOptions} helperText="Choose priority level" />);
    
    const helperText = screen.getByText('Choose priority level');
    expect(helperText).toBeInTheDocument();
    expect(helperText.className).toContain('helperText');
  });

  it('錯誤訊息應該優先於輔助文字', () => {
    render(
      <Select 
        label="Status" 
        options={mockOptions}
        error="Invalid status" 
        helperText="Select current status" 
      />
    );
    
    expect(screen.getByText('Invalid status')).toBeInTheDocument();
    expect(screen.queryByText('Select current status')).not.toBeInTheDocument();
  });

  it('應該支援全寬度', () => {
    render(<Select options={mockOptions} fullWidth data-testid="full-width-select" />);
    
    const select = screen.getByTestId('full-width-select');
    const container = select.parentElement?.parentElement; // selectWrapper -> container
    expect(container?.className).toContain('fullWidth');
  });

  it('應該正確處理選擇事件', () => {
    const handleChange = vi.fn();
    render(<Select options={mockOptions} onChange={handleChange} />);
    
    const select = screen.getByRole('combobox');
    fireEvent.change(select, { target: { value: 'option2' } });
    
    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(select).toHaveValue('option2');
  });

  it('應該支援禁用狀態', () => {
    render(<Select options={mockOptions} disabled />);
    
    const select = screen.getByRole('combobox');
    expect(select).toBeDisabled();
  });

  it('應該正確處理禁用的選項', () => {
    render(<Select options={mockOptions} />);
    
    const disabledOption = screen.getByRole('option', { name: 'Option 3' });
    expect(disabledOption).toBeDisabled();
  });

  it('應該支援預設值', () => {
    render(<Select options={mockOptions} defaultValue="option2" />);
    
    const select = screen.getByRole('combobox');
    expect(select).toHaveValue('option2');
  });

  it('應該支援受控值', () => {
    const { rerender } = render(<Select options={mockOptions} value="option1" onChange={() => {}} />);
    
    let select = screen.getByRole('combobox');
    expect(select).toHaveValue('option1');

    rerender(<Select options={mockOptions} value="option2" onChange={() => {}} />);
    select = screen.getByRole('combobox');
    expect(select).toHaveValue('option2');
  });

  it('應該支援自訂 className', () => {
    render(<Select options={mockOptions} className="custom-select" data-testid="custom-select" />);
    
    const select = screen.getByTestId('custom-select');
    const container = select.parentElement?.parentElement; // selectWrapper -> container
    expect(container?.className).toContain('custom-select');
  });

  it('應該傳遞其他 HTML 屬性', () => {
    render(<Select options={mockOptions} data-testid="custom-select" required />);
    
    const select = screen.getByTestId('custom-select');
    expect(select).toBeRequired();
  });

  it('應該生成唯一的 ID', () => {
    render(
      <div>
        <Select label="First" options={mockOptions} />
        <Select label="Second" options={mockOptions} />
      </div>
    );
    
    const firstSelect = screen.getByLabelText('First');
    const secondSelect = screen.getByLabelText('Second');
    
    expect(firstSelect.id).toBeTruthy();
    expect(secondSelect.id).toBeTruthy();
    expect(firstSelect.id).not.toBe(secondSelect.id);
  });

  it('應該使用提供的 ID', () => {
    render(<Select id="custom-id" label="Custom ID" options={mockOptions} />);
    
    const select = screen.getByLabelText('Custom ID');
    expect(select).toHaveAttribute('id', 'custom-id');
  });

  it('應該顯示下拉箭頭', () => {
    render(<Select options={mockOptions} data-testid="select-with-chevron" />);
    
    const selectWrapper = screen.getByTestId('select-with-chevron').parentElement;
    const chevron = selectWrapper?.querySelector('[class*="chevron"]');
    expect(chevron).toBeInTheDocument();
  });
});