/**
 * Print Styles
 * 打印樣式文件
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

/* Print Media Query */
@media print {
    /* Reset and base styles for print */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    /* Page setup */
    @page {
        size: A4;
        margin: 0.5in;
        orphans: 3;
        widows: 3;
    }
    
    @page :first {
        margin-top: 0.5in;
    }
    
    @page :left {
        margin-left: 0.75in;
        margin-right: 0.5in;
    }
    
    @page :right {
        margin-left: 0.5in;
        margin-right: 0.75in;
    }
    
    /* Hide non-printable elements */
    .print-hide,
    .kms-header,
    .kms-footer,
    .kms-section-header,
    .kms-form-section,
    .kms-loading-overlay,
    .kms-modal-container,
    .kms-alert-container,
    .form-actions,
    .kms-btn,
    button,
    input[type="button"],
    input[type="submit"],
    input[type="reset"],
    .kms-skip-link {
        display: none !important;
    }
    
    /* Show only receipt content */
    .print-show {
        display: block !important;
    }
    
    /* Body and layout */
    body {
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
        margin: 0;
        padding: 0;
    }
    
    .kms-main {
        padding: 0;
        margin: 0;
    }
    
    .kms-layout-container {
        max-width: none;
        padding: 0;
        margin: 0;
    }
    
    .kms-layout-grid {
        display: block;
        margin: 0;
    }
    
    .kms-preview-section {
        padding: 0;
        margin: 0;
        background: none;
        border: none;
        box-shadow: none;
    }
    
    /* Receipt document styles */
    .receipt-document {
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 0;
        background: #fff;
        color: #000;
        font-family: 'Times New Roman', serif;
        font-size: 11pt;
        line-height: 1.3;
        box-shadow: none;
        border: none;
    }
    
    /* Receipt header */
    .receipt-header {
        padding: 0 0 12pt 0;
        border-bottom: 2pt solid #000;
        background: none;
        page-break-inside: avoid;
    }
    
    .receipt-header-content {
        display: block;
    }
    
    .receipt-store-info {
        margin-bottom: 12pt;
    }
    
    .receipt-store-name {
        font-size: 18pt;
        font-weight: bold;
        color: #000;
        margin-bottom: 6pt;
    }
    
    .receipt-store-details {
        font-size: 10pt;
        color: #000;
        line-height: 1.4;
    }
    
    .receipt-meta {
        text-align: right;
        margin-top: -60pt;
        font-size: 10pt;
    }
    
    .receipt-number {
        font-size: 14pt;
        font-weight: bold;
        color: #000;
        margin-bottom: 6pt;
    }
    
    .receipt-date {
        color: #000;
    }
    
    /* Receipt customer info */
    .receipt-customer {
        padding: 12pt 0;
        background: none;
        border-bottom: 1pt solid #ccc;
        page-break-inside: avoid;
    }
    
    .receipt-customer-title {
        font-size: 12pt;
        font-weight: bold;
        color: #000;
        margin-bottom: 6pt;
    }
    
    .receipt-customer-details {
        display: block;
        font-size: 10pt;
        color: #000;
    }
    
    .receipt-customer-field {
        display: inline-block;
        margin-right: 24pt;
        margin-bottom: 3pt;
    }
    
    .receipt-customer-label {
        font-weight: bold;
        color: #000;
    }
    
    .receipt-customer-value {
        color: #000;
    }
    
    /* Receipt items table */
    .receipt-items {
        padding: 12pt 0;
    }
    
    .receipt-items-title {
        font-size: 12pt;
        font-weight: bold;
        color: #000;
        margin-bottom: 6pt;
    }
    
    .receipt-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 10pt;
        page-break-inside: auto;
    }
    
    .receipt-table th {
        background: #f0f0f0;
        color: #000;
        font-weight: bold;
        padding: 6pt 4pt;
        text-align: left;
        border: 1pt solid #000;
        page-break-inside: avoid;
        page-break-after: avoid;
    }
    
    .receipt-table th:last-child {
        text-align: right;
    }
    
    .receipt-table td {
        padding: 4pt;
        border: 0.5pt solid #ccc;
        vertical-align: top;
        page-break-inside: avoid;
    }
    
    .receipt-table td:last-child {
        text-align: right;
    }
    
    .receipt-table tbody tr {
        page-break-inside: avoid;
    }
    
    .receipt-item-name {
        font-weight: bold;
        color: #000;
        margin-bottom: 2pt;
    }
    
    .receipt-item-category {
        font-size: 8pt;
        color: #666;
        background: #f0f0f0;
        padding: 1pt 3pt;
        border-radius: 2pt;
        display: inline-block;
    }
    
    .receipt-item-price,
    .receipt-item-quantity,
    .receipt-item-total {
        font-weight: bold;
        color: #000;
    }
    
    /* Receipt summary */
    .receipt-summary {
        padding: 12pt 0 0 0;
        background: none;
        border-top: 2pt solid #000;
        page-break-inside: avoid;
    }
    
    .receipt-summary-table {
        width: 100%;
        max-width: 200pt;
        margin-left: auto;
        font-size: 11pt;
    }
    
    .receipt-summary-table td {
        padding: 3pt 6pt;
        border: none;
    }
    
    .receipt-summary-table .receipt-summary-label {
        color: #000;
        text-align: left;
    }
    
    .receipt-summary-table .receipt-summary-value {
        color: #000;
        font-weight: bold;
        text-align: right;
    }
    
    .receipt-summary-subtotal {
        border-bottom: 1pt solid #ccc;
    }
    
    .receipt-summary-total {
        border-top: 2pt solid #000;
        background: #f0f0f0;
    }
    
    .receipt-summary-total .receipt-summary-label,
    .receipt-summary-total .receipt-summary-value {
        font-size: 12pt;
        font-weight: bold;
        color: #000;
    }
    
    /* Receipt footer */
    .receipt-footer {
        padding: 12pt 0 0 0;
        text-align: center;
        border-top: 1pt solid #ccc;
        background: none;
        page-break-inside: avoid;
    }
    
    .receipt-footer-text {
        font-size: 9pt;
        color: #666;
        line-height: 1.4;
    }
    
    .receipt-footer-signature {
        margin-top: 24pt;
        display: block;
    }
    
    .receipt-signature-section {
        display: inline-block;
        width: 45%;
        text-align: center;
        margin: 0 2.5%;
    }
    
    .receipt-signature-line {
        border-bottom: 1pt solid #000;
        height: 24pt;
        margin-bottom: 6pt;
    }
    
    .receipt-signature-label {
        font-size: 9pt;
        color: #000;
    }
    
    /* Receipt logo */
    .receipt-logo {
        max-width: 60pt;
        max-height: 45pt;
        object-fit: contain;
    }
    
    /* Receipt status - hide in print or show as text */
    .receipt-status {
        display: none;
    }
    
    /* Receipt notes */
    .receipt-notes {
        padding: 12pt 0;
        background: none;
        border-top: 1pt solid #ccc;
        page-break-inside: avoid;
    }
    
    .receipt-notes-title {
        font-size: 11pt;
        font-weight: bold;
        color: #000;
        margin-bottom: 6pt;
    }
    
    .receipt-notes-content {
        font-size: 10pt;
        color: #000;
        line-height: 1.4;
    }
    
    /* Page breaks */
    .print-page-break {
        page-break-before: always;
    }
    
    .print-no-break {
        page-break-inside: avoid;
    }
    
    /* Avoid breaking these elements */
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    /* Ensure good contrast for printing */
    .receipt-document * {
        color: #000 !important;
        background: transparent !important;
    }
    
    /* Exception for backgrounds that need to print */
    .receipt-summary-total,
    .receipt-table th,
    .receipt-item-category {
        background: #f0f0f0 !important;
    }
    
    /* Links should not be underlined in print */
    a {
        text-decoration: none;
        color: #000;
    }
    
    /* Remove any remaining shadows or effects */
    * {
        box-shadow: none !important;
        text-shadow: none !important;
    }
}
