import React from 'react';
import { Input } from '../ui';
import { useReceipt } from '../../hooks/useReceipt';
import { useValidation } from '../../hooks/useValidation';
import styles from './CustomerInfoSection.module.css';

export const CustomerInfoSection: React.FC = () => {
  const { receiptData, updateCustomerInfo } = useReceipt();
  const {
    validateCustomerInfoForm,
    getFieldError,
    validatePhoneField,
    validateEmailField
  } = useValidation();

  const handleInputChange = (field: keyof typeof receiptData.customerInfo, value: string) => {
    updateCustomerInfo({ [field]: value });

    // 即時驗證 - 所有欄位都是選填
    switch (field) {
      case 'phone':
        validatePhoneField('customerPhone', value);
        break;
      case 'email':
        validateEmailField('customerEmail', value);
        break;
    }
  };

  const handleBlur = () => {
    // 當失去焦點時驗證整個表單
    validateCustomerInfoForm(receiptData.customerInfo);
  };

  return (
    <div className={styles.section}>
      <div className={styles.header}>
        <h2>Customer Information</h2>
        <p className={styles.description}>
          Please fill in the customer's basic information, which will be displayed on the receipt
        </p>
      </div>

      <div className={styles.formGrid}>
        <Input
          label="Customer Name"
          value={receiptData.customerInfo.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          onBlur={handleBlur}
          error={getFieldError('customerName')}
          placeholder="Enter customer name"
          fullWidth
        />

        <Input
          label="Contact Phone"
          type="tel"
          value={receiptData.customerInfo.phone}
          onChange={(e) => handleInputChange('phone', e.target.value)}
          onBlur={handleBlur}
          error={getFieldError('customerPhone')}
          placeholder="e.g.: 0912-345-678"
          helperText="Optional, for follow-up contact"
          fullWidth
        />

        <Input
          label="Email"
          type="email"
          value={receiptData.customerInfo.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          onBlur={handleBlur}
          error={getFieldError('customerEmail')}
          placeholder="e.g.: <EMAIL>"
          helperText="Optional, for sending electronic receipts"
          fullWidth
        />

        <Input
          label="Address"
          value={receiptData.customerInfo.address}
          onChange={(e) => handleInputChange('address', e.target.value)}
          onBlur={handleBlur}
          error={getFieldError('customerAddress')}
          placeholder="Enter customer address"
          helperText="Optional, customer's address"
          fullWidth
        />
      </div>

      <div className={styles.preview}>
        <h3>Preview</h3>
        <div className={styles.previewCard}>
          <div className={styles.customerName}>
            Customer: {receiptData.customerInfo.name || 'Customer Name'}
          </div>
          <div className={styles.customerDetails}>
            {receiptData.customerInfo.phone && (
              <div>Phone: {receiptData.customerInfo.phone}</div>
            )}
            {receiptData.customerInfo.email && (
              <div>Email: {receiptData.customerInfo.email}</div>
            )}
            {receiptData.customerInfo.address && (
              <div>Address: {receiptData.customerInfo.address}</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};