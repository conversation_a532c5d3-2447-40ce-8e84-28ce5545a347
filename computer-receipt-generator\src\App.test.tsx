import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from './App';

// Mock html2pdf to avoid issues in tests
vi.mock('html2pdf.js', () => ({
  default: () => ({
    set: () => ({
      from: () => ({
        save: vi.fn()
      })
    })
  })
}));

describe('App', () => {
  it('renders the main application structure', () => {
    render(<App />);
    
    // 檢查標題是否存在
    expect(screen.getByText('電腦收據生成器')).toBeInTheDocument();
    
    // 檢查主要區塊是否存在
    expect(screen.getByText('收據生成器')).toBeInTheDocument();
    expect(screen.getByText('收據預覽')).toBeInTheDocument();
    
    // 檢查操作按鈕是否存在
    expect(screen.getByText('列印收據')).toBeInTheDocument();
    expect(screen.getByText('儲存 PDF')).toBeInTheDocument();
  });

  it('renders form sections', () => {
    render(<App />);
    
    // 檢查表單區塊
    expect(screen.getByText('商店資訊')).toBeInTheDocument();
    expect(screen.getByText('客戶資訊')).toBeInTheDocument();
    expect(screen.getByText('收據項目')).toBeInTheDocument();
    expect(screen.getByText('稅率設定')).toBeInTheDocument();
  });

  it('renders preview section with empty state', () => {
    render(<App />);
    
    // 檢查預覽區塊的空狀態
    expect(screen.getByText('請先新增項目來查看收據預覽')).toBeInTheDocument();
  });

  it('renders action buttons in disabled state initially', () => {
    render(<App />);
    
    // 檢查按鈕初始狀態為禁用
    const printButton = screen.getByRole('button', { name: /列印收據/i });
    const pdfButton = screen.getByRole('button', { name: /儲存 PDF/i });
    
    expect(printButton).toBeDisabled();
    expect(pdfButton).toBeDisabled();
  });

  it('shows validation warning when receipt is incomplete', () => {
    render(<App />);
    
    // 檢查驗證警告訊息
    expect(screen.getByText('收據資訊不完整')).toBeInTheDocument();
    expect(screen.getByText(/請確保已填寫商店資訊、客戶資訊/)).toBeInTheDocument();
  });

  it('renders header features', () => {
    render(<App />);
    
    // 檢查標題區域的功能說明
    expect(screen.getByText('Letter 紙張格式')).toBeInTheDocument();
    expect(screen.getByText('列印友善')).toBeInTheDocument();
    expect(screen.getByText('PDF 匯出')).toBeInTheDocument();
  });

  it('has correct receipt preview element id for actions', () => {
    render(<App />);
    
    // 檢查收據預覽元素是否有正確的 ID
    const previewElement = document.getElementById('receipt-preview-element');
    expect(previewElement).toBeInTheDocument();
  });
});