.footer {
  margin-top: 2rem;
  border-top: 2px solid #000000;
  padding-top: 1rem;
}

.calculationSummary {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.95rem;
}

.summaryRow:last-of-type {
  border-bottom: none;
  margin-bottom: 0.5rem;
}

.summaryLabel {
  font-weight: 500;
  color: #555;
}

.summaryValue {
  font-weight: 600;
  color: #000000;
  font-family: 'Courier New', monospace;
}

.totalRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-top: 2px solid #000000;
  margin-top: 0.5rem;
}

.totalLabel {
  font-size: 1.1rem;
  font-weight: bold;
  color: #000000;
}

.totalValue {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2e7d32;
  font-family: 'Courier New', monospace;
}

.footerMessage {
  text-align: center;
  margin-bottom: 1.5rem;
}

.thankYouMessage {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2e7d32;
  margin-bottom: 0.5rem;
  padding: 0.75rem;
  background-color: #e8f5e8;
  border-radius: 6px;
  border: 1px solid #c8e6c9;
}

.warrantyMessage {
  font-size: 0.9rem;
  font-weight: 500;
  color: #1976d2;
  margin-bottom: 2rem;
  padding: 0.5rem;
  background-color: #e3f2fd;
  border-radius: 4px;
  border: 1px solid #bbdefb;
}

/* 簽名欄位樣式 */
.signatureSection {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #ddd;
}

.signatureField {
  width: 45%;
  text-align: center;
}

.signatureLine {
  height: 1px;
  background-color: #000000;
  margin-bottom: 0.5rem;
  width: 100%;
}

.signatureLabel {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 日期區域樣式 */
.dateSection {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #ddd;
}

.dateLabel {
  font-size: 0.9rem;
  color: #000000;
  font-weight: 500;
}

.additionalInfo {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.5;
}

.additionalInfo p {
  margin: 0.5rem 0;
}

.returnPolicy {
  font-weight: 500;
}

.warrantyInfo {
  font-style: italic;
}

.footerDivider {
  height: 1px;
  background-color: #ddd;
  margin: 1rem 0;
}

.receiptBottom {
  text-align: center;
  padding-top: 0.5rem;
}

.printInfo {
  font-size: 0.8rem;
  color: #999;
  font-style: italic;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .calculationSummary {
    padding: 0.75rem;
  }
  
  .summaryRow {
    padding: 0.4rem 0;
    font-size: 0.9rem;
  }
  
  .totalRow {
    padding: 0.6rem 0;
  }
  
  .totalLabel {
    font-size: 1rem;
  }
  
  .totalValue {
    font-size: 1.1rem;
  }
  
  .thankYouMessage {
    font-size: 1rem;
    padding: 0.6rem;
  }
  
  .additionalInfo {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .footer {
    margin-top: 1.5rem;
  }
  
  .calculationSummary {
    padding: 0.6rem;
    margin-bottom: 1rem;
  }
  
  .summaryRow {
    flex-direction: column;
    align-items: flex-start;
    padding: 0.3rem 0;
  }
  
  .summaryValue {
    margin-top: 0.2rem;
    align-self: flex-end;
  }
  
  .totalRow {
    flex-direction: column;
    align-items: flex-start;
    padding: 0.5rem 0;
  }
  
  .totalValue {
    margin-top: 0.3rem;
    align-self: flex-end;
  }
  
  .thankYouMessage {
    font-size: 0.95rem;
    padding: 0.5rem;
  }
  
  .additionalInfo {
    font-size: 0.75rem;
  }
}

/* 列印樣式 */
@media print {
  .footer {
    border-top: 2px solid #000;
    page-break-inside: avoid;
    margin-top: 16pt;
  }
  
  .calculationSummary {
    background-color: transparent;
    border: 1px solid #000;
    padding: 8pt;
    margin-bottom: 12pt;
  }
  
  .summaryRow {
    font-size: 10pt;
    border-bottom: 1px solid #000;
    padding: 4pt 0;
  }
  
  .summaryRow:last-of-type {
    border-bottom: none;
  }
  
  .totalRow {
    border-top: 2px solid #000;
    padding: 6pt 0;
    margin-top: 4pt;
  }
  
  .totalLabel {
    font-size: 12pt;
  }
  
  .totalValue {
    font-size: 14pt;
  }
  
  .thankYouMessage {
    background-color: transparent;
    border: 1px solid #000;
    font-size: 11pt;
    padding: 6pt;
    margin-bottom: 8pt;
  }
  
  .additionalInfo {
    font-size: 8pt;
  }
  
  .footerDivider {
    background-color: #000;
  }
  
  .printInfo {
    font-size: 7pt;
  }
}