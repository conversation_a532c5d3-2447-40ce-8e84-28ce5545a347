# KMS PC Receipt Maker | 電腦收據生成器

一個專業的電腦硬件收據生成系統，支持中英文雙語，具備完整的商店管理、客戶管理和收據生成功能。

A professional computer hardware receipt generation system with bilingual support (Chinese/English), featuring complete store management, customer management, and receipt generation capabilities.

## 功能特點 | Features

### 🏪 商店管理 | Store Management
- 多商店支持
- 商店信息管理（名稱、地址、電話、郵箱等）
- 商店統計數據

### 👥 客戶管理 | Customer Management
- 客戶信息管理
- 客戶搜索功能
- 客戶歷史記錄

### 📄 收據生成 | Receipt Generation
- 專業收據模板
- 項目分類管理（硬件/服務）
- 自動計算稅額和總計
- 支持打印和PDF導出

### 🌐 國際化支持 | Internationalization
- 中文繁體 / English
- 動態語言切換
- 本地化日期和貨幣格式

### 🎨 用戶界面 | User Interface
- 響應式設計
- 深色/淺色主題切換
- 無障礙設計
- 移動設備友好

## 技術架構 | Technical Architecture

### 後端 | Backend
- **PHP 8.0+** - 服務器端語言
- **MySQL 8.0+** - 數據庫
- **PDO** - 數據庫抽象層
- **RESTful API** - API設計

### 前端 | Frontend
- **HTML5** - 標記語言
- **CSS3** - 樣式設計
- **JavaScript ES6+** - 交互邏輯
- **響應式設計** - 多設備支持

### 數據庫結構 | Database Structure
- `stores` - 商店信息
- `customers` - 客戶信息
- `categories` - 項目分類
- `receipts` - 收據主表
- `receipt_items` - 收據項目明細

## 安裝說明 | Installation

### 系統要求 | System Requirements
- PHP 8.0 或更高版本
- MySQL 8.0 或更高版本
- Web服務器 (Apache/Nginx)
- 現代瀏覽器支持

### 安裝步驟 | Installation Steps

1. **克隆項目 | Clone Project**
   ```bash
   git clone https://github.com/your-repo/kms-receipt-maker.git
   cd kms-receipt-maker
   ```

2. **配置數據庫 | Configure Database**
   - 創建MySQL數據庫
   - 導入數據庫結構：`database/schema.sql`
   - 配置數據庫連接：`config/database.php`

3. **配置Web服務器 | Configure Web Server**
   - 將項目文件放置在Web根目錄
   - 確保PHP擴展已啟用：PDO, PDO_MySQL
   - 設置適當的文件權限

4. **測試安裝 | Test Installation**
   - 訪問 `http://your-domain/test.html`
   - 運行功能測試
   - 確保所有測試通過

## 使用說明 | Usage Guide

### 基本操作 | Basic Operations

1. **添加商店信息**
   - 進入商店管理頁面
   - 填寫商店基本信息
   - 保存設置

2. **管理客戶**
   - 添加新客戶或搜索現有客戶
   - 維護客戶聯繫信息

3. **生成收據**
   - 選擇商店
   - 填寫客戶信息
   - 添加收據項目
   - 設置稅率
   - 生成並打印收據

### API使用 | API Usage

#### 商店API | Store API
```javascript
// 獲取所有商店
GET /api/stores

// 創建商店
POST /api/stores
{
  "name": "商店名稱",
  "address": "商店地址",
  "phone": "電話號碼",
  "email": "郵箱地址"
}
```

#### 客戶API | Customer API
```javascript
// 搜索客戶
GET /api/customers?search=關鍵詞

// 創建客戶
POST /api/customers
{
  "name": "客戶姓名",
  "phone": "電話號碼",
  "email": "郵箱地址"
}
```

#### 收據API | Receipt API
```javascript
// 創建收據
POST /api/receipts
{
  "store_id": 1,
  "customer_name": "客戶姓名",
  "items": [
    {
      "name": "項目名稱",
      "price": 1000,
      "quantity": 1
    }
  ]
}
```

## 文件結構 | File Structure

```
kms-receipt-maker/
├── api/                    # API接口文件
│   ├── init.php           # API初始化
│   ├── stores.php         # 商店API
│   ├── customers.php      # 客戶API
│   ├── categories.php     # 分類API
│   └── receipts.php       # 收據API
├── config/                # 配置文件
│   └── database.php       # 數據庫配置
├── css/                   # 樣式文件
│   ├── main.css          # 主樣式
│   ├── forms.css         # 表單樣式
│   ├── receipt.css       # 收據樣式
│   ├── responsive.css    # 響應式樣式
│   └── print.css         # 打印樣式
├── includes/              # 公共文件
│   ├── functions.php     # 通用函數
│   └── validation.php    # 驗證函數
├── js/                    # JavaScript文件
│   ├── utils.js          # 工具函數
│   ├── i18n.js           # 國際化
│   ├── api.js            # API通信
│   ├── forms.js          # 表單處理
│   ├── receipt.js        # 收據管理
│   ├── print.js          # 打印功能
│   └── app.js            # 主應用
├── models/                # 數據模型
│   ├── BaseModel.php     # 基礎模型
│   ├── Store.php         # 商店模型
│   ├── Customer.php      # 客戶模型
│   ├── Category.php      # 分類模型
│   ├── Receipt.php       # 收據模型
│   └── ReceiptItem.php   # 收據項目模型
├── index.html             # 主頁面
├── test.html              # 測試頁面
└── README.md              # 說明文檔
```

## 開發指南 | Development Guide

### 代碼規範 | Code Standards
- PHP遵循PSR-12標準
- JavaScript使用ES6+語法
- CSS使用BEM命名規範
- 所有函數和類都有中英文註釋

### 自定義開發 | Custom Development
- 模塊化設計，易於擴展
- 完整的API接口
- 響應式CSS框架
- 國際化支持

## 故障排除 | Troubleshooting

### 常見問題 | Common Issues

1. **數據庫連接失敗**
   - 檢查MySQL服務狀態
   - 驗證數據庫配置
   - 確認用戶權限

2. **API請求失敗**
   - 檢查Web服務器配置
   - 驗證PHP擴展
   - 查看錯誤日誌

3. **打印功能異常**
   - 確認瀏覽器支持
   - 檢查打印樣式
   - 測試PDF生成

## 許可證 | License

MIT License - 詳見 LICENSE 文件

## 支持 | Support

如有問題或建議，請聯繫：
- 郵箱：<EMAIL>
- 電話：+886-2-1234-5678

For questions or suggestions, please contact:
- Email: <EMAIL>
- Phone: +886-2-1234-5678

## 更新日誌 | Changelog

### v1.0.0 (2025-01-14)
- 初始版本發布
- 完整的收據生成功能
- 中英文雙語支持
- 響應式設計
- 打印和PDF導出功能

---

© 2025 KMS PC Store. All rights reserved.
