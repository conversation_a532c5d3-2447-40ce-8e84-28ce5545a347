/* 容器樣式 */
.container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.fullWidth {
  width: 100%;
}

/* 標籤樣式 */
.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

/* 選擇框包裝器 */
.selectWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

/* 選擇框樣式 */
.select {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  line-height: 1.5;
  color: #111827;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.select:focus {
  outline: none;
  border-color: #646cff;
  box-shadow: 0 0 0 3px rgba(100, 108, 255, 0.1);
}

.select:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* 錯誤狀態 */
.error {
  border-color: #dc3545;
}

.error:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* 下拉箭頭 */
.chevron {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  pointer-events: none;
  transition: transform 0.2s ease-in-out;
}

.select:focus + .chevron {
  transform: translateY(-50%) rotate(180deg);
}

/* 輔助文字樣式 */
.helperText {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.errorText {
  font-size: 0.75rem;
  color: #dc3545;
  margin-top: 0.25rem;
}