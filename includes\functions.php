<?php
/**
 * Common Functions
 * 通用函數庫
 * 
 * KMS PC Receipt Maker
 * Created: 2025-01-14
 */

// 防止直接訪問
if (!defined('KMS_RECEIPT_MAKER')) {
    die('Direct access not permitted');
}

/**
 * 響應處理函數
 * Response handling functions
 */

/**
 * 發送JSON響應
 * Send JSON response
 * 
 * @param mixed $data 響應數據
 * @param int $status HTTP狀態碼
 * @param string $message 消息
 */
function sendJsonResponse($data = null, $status = 200, $message = '') {
    http_response_code($status);
    header('Content-Type: application/json; charset=utf-8');
    
    $response = [
        'success' => $status >= 200 && $status < 300,
        'status' => $status,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

/**
 * 發送成功響應
 * Send success response
 * 
 * @param mixed $data 數據
 * @param string $message 消息
 */
function sendSuccess($data = null, $message = 'Success') {
    sendJsonResponse($data, 200, $message);
}

/**
 * 發送錯誤響應
 * Send error response
 * 
 * @param string $message 錯誤消息
 * @param int $status HTTP狀態碼
 * @param mixed $data 額外數據
 */
function sendError($message = 'Error', $status = 400, $data = null) {
    sendJsonResponse($data, $status, $message);
}

/**
 * 數據驗證函數
 * Data validation functions
 */

/**
 * 驗證必填字段
 * Validate required fields
 * 
 * @param array $data 數據數組
 * @param array $required 必填字段數組
 * @return array 錯誤數組
 */
function validateRequired($data, $required) {
    $errors = [];
    
    foreach ($required as $field) {
        if (!isset($data[$field]) || trim($data[$field]) === '') {
            $errors[$field] = "Field '{$field}' is required";
        }
    }
    
    return $errors;
}

/**
 * 驗證電子郵件格式
 * Validate email format
 * 
 * @param string $email 電子郵件
 * @return bool
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * 驗證電話號碼格式
 * Validate phone number format
 * 
 * @param string $phone 電話號碼
 * @return bool
 */
function validatePhone($phone) {
    // 支持台灣手機和市話格式
    $pattern = '/^(\+886|0)([2-9]\d{7,8}|[2-9]\d{2}-\d{3}-\d{3,4})$/';
    return preg_match($pattern, $phone);
}

/**
 * 驗證價格格式
 * Validate price format
 * 
 * @param mixed $price 價格
 * @return bool
 */
function validatePrice($price) {
    return is_numeric($price) && $price >= 0;
}

/**
 * 驗證數量
 * Validate quantity
 * 
 * @param mixed $quantity 數量
 * @return bool
 */
function validateQuantity($quantity) {
    return is_numeric($quantity) && $quantity > 0 && $quantity == intval($quantity);
}

/**
 * 工具函數
 * Utility functions
 */

/**
 * 清理輸入數據
 * Sanitize input data
 * 
 * @param mixed $data 輸入數據
 * @return mixed 清理後的數據
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    if (is_string($data)) {
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    return $data;
}

/**
 * 生成收據編號
 * Generate receipt number
 * 
 * @param string $prefix 前綴
 * @return string 收據編號
 */
function generateReceiptNumber($prefix = 'RCP') {
    $date = date('Ymd');
    $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    return $prefix . '-' . $date . '-' . $random;
}

/**
 * 格式化貨幣
 * Format currency
 * 
 * @param float $amount 金額
 * @param string $currency 貨幣符號
 * @return string 格式化後的金額
 */
function formatCurrency($amount, $currency = 'NT$') {
    return $currency . ' ' . number_format($amount, 0, '.', ',');
}

/**
 * 計算稅額
 * Calculate tax amount
 * 
 * @param float $subtotal 小計
 * @param float $taxRate 稅率（百分比）
 * @return float 稅額
 */
function calculateTax($subtotal, $taxRate) {
    return round($subtotal * ($taxRate / 100), 2);
}

/**
 * 計算總計
 * Calculate total
 * 
 * @param float $subtotal 小計
 * @param float $taxAmount 稅額
 * @param float $discount 折扣
 * @return float 總計
 */
function calculateTotal($subtotal, $taxAmount, $discount = 0) {
    return round($subtotal + $taxAmount - $discount, 2);
}

/**
 * 日期格式化
 * Format date
 * 
 * @param string $date 日期字符串
 * @param string $format 格式
 * @return string 格式化後的日期
 */
function formatDate($date, $format = 'Y-m-d') {
    if (empty($date)) {
        return '';
    }
    
    $timestamp = is_numeric($date) ? $date : strtotime($date);
    return date($format, $timestamp);
}

/**
 * 獲取客戶端IP地址
 * Get client IP address
 * 
 * @return string IP地址
 */
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * 記錄日誌
 * Log message
 * 
 * @param string $message 消息
 * @param string $level 級別
 * @param string $file 文件名
 */
function logMessage($message, $level = 'INFO', $file = 'app.log') {
    $timestamp = date('Y-m-d H:i:s');
    $ip = getClientIP();
    $logEntry = "[{$timestamp}] [{$level}] [{$ip}] {$message}" . PHP_EOL;
    
    $logDir = dirname(__DIR__) . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logDir . '/' . $file, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * 檢查請求方法
 * Check request method
 * 
 * @param string $method 期望的方法
 * @return bool
 */
function checkRequestMethod($method) {
    return $_SERVER['REQUEST_METHOD'] === strtoupper($method);
}

/**
 * 獲取請求數據
 * Get request data
 * 
 * @return array 請求數據
 */
function getRequestData() {
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            return $_GET;
        case 'POST':
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            if (strpos($contentType, 'application/json') !== false) {
                return json_decode(file_get_contents('php://input'), true) ?? [];
            }
            return $_POST;
        case 'PUT':
        case 'DELETE':
        case 'PATCH':
            return json_decode(file_get_contents('php://input'), true) ?? [];
        default:
            return [];
    }
}

/**
 * 設置CORS頭部
 * Set CORS headers
 */
function setCorsHeaders() {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit;
    }
}
?>
