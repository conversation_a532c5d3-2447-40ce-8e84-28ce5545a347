// 資料驗證工具函數

/**
 * 驗證價格是否為有效的正數，最多兩位小數
 */
export const validatePrice = (price: number | string): boolean => {
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
  
  // 檢查是否為有效數字
  if (isNaN(numPrice) || !isFinite(numPrice)) {
    return false;
  }
  
  // 檢查是否為正數
  if (numPrice <= 0) {
    return false;
  }
  
  // 檢查小數位數是否不超過兩位
  const decimalPlaces = (numPrice.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) {
    return false;
  }
  
  return true;
};

/**
 * 驗證必填欄位是否為空
 */
export const validateRequired = (value: string | number | undefined | null): boolean => {
  if (value === undefined || value === null) {
    return false;
  }
  
  if (typeof value === 'string') {
    return value.trim().length > 0;
  }
  
  if (typeof value === 'number') {
    return !isNaN(value) && isFinite(value);
  }
  
  return false;
};

/**
 * 驗證電子郵件格式
 */
export const validateEmail = (email: string): boolean => {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

/**
 * 驗證電話號碼格式（支援多種格式）
 */
export const validatePhone = (phone: string): boolean => {
  if (!phone || typeof phone !== 'string') {
    return false;
  }
  
  // 移除所有空格、破折號和括號
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
  
  // 檢查是否只包含數字和可選的 + 號開頭
  const phoneRegex = /^\+?[0-9]{8,15}$/;
  return phoneRegex.test(cleanPhone);
};

/**
 * 驗證數量是否為有效的正整數
 */
export const validateQuantity = (quantity: number | string): boolean => {
  // 如果是字串，先檢查是否包含小數點
  if (typeof quantity === 'string') {
    if (quantity.includes('.')) {
      return false;
    }
    const numQuantity = parseInt(quantity, 10);
    
    // 檢查是否為有效數字
    if (isNaN(numQuantity) || !isFinite(numQuantity)) {
      return false;
    }
    
    // 檢查是否為正整數
    if (numQuantity <= 0) {
      return false;
    }
    
    return true;
  }
  
  // 如果是數字
  const numQuantity = quantity;
  
  // 檢查是否為有效數字
  if (isNaN(numQuantity) || !isFinite(numQuantity)) {
    return false;
  }
  
  // 檢查是否為正整數
  if (numQuantity <= 0 || !Number.isInteger(numQuantity)) {
    return false;
  }
  
  return true;
};

/**
 * 驗證稅率是否在有效範圍內（0-100%）
 */
export const validateTaxRate = (taxRate: number | string): boolean => {
  const numTaxRate = typeof taxRate === 'string' ? parseFloat(taxRate) : taxRate;
  
  // 檢查是否為有效數字
  if (isNaN(numTaxRate) || !isFinite(numTaxRate)) {
    return false;
  }
  
  // 檢查是否在 0-100 範圍內
  if (numTaxRate < 0 || numTaxRate > 100) {
    return false;
  }
  
  return true;
};

/**
 * 取得價格驗證錯誤訊息
 */
export const getPriceErrorMessage = (price: number | string): string => {
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;

  if (isNaN(numPrice) || !isFinite(numPrice)) {
    return 'Please enter a valid number';
  }

  if (numPrice <= 0) {
    return 'Price must be greater than 0';
  }

  const decimalPlaces = (numPrice.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) {
    return 'Price can have at most two decimal places';
  }

  return '';
};

/**
 * 取得必填欄位錯誤訊息
 */
export const getRequiredErrorMessage = (fieldName: string): string => {
  return `${fieldName} is required`;
};

/**
 * 取得電子郵件錯誤訊息
 */
export const getEmailErrorMessage = (): string => {
  return 'Please enter a valid email format';
};

/**
 * 取得電話號碼錯誤訊息
 */
export const getPhoneErrorMessage = (): string => {
  return 'Please enter a valid phone number';
};

/**
 * 取得數量錯誤訊息
 */
export const getQuantityErrorMessage = (quantity: number | string): string => {
  const numQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;

  if (isNaN(numQuantity) || !isFinite(numQuantity)) {
    return 'Please enter a valid number';
  }

  if (numQuantity <= 0) {
    return 'Quantity must be greater than 0';
  }

  if (!Number.isInteger(numQuantity)) {
    return 'Quantity must be an integer';
  }

  return '';
};

/**
 * 取得稅率錯誤訊息
 */
export const getTaxRateErrorMessage = (taxRate: number | string): string => {
  const numTaxRate = typeof taxRate === 'string' ? parseFloat(taxRate) : taxRate;

  if (isNaN(numTaxRate) || !isFinite(numTaxRate)) {
    return 'Please enter a valid number';
  }

  if (numTaxRate < 0 || numTaxRate > 100) {
    return 'Tax rate must be between 0-100%';
  }

  return '';
};