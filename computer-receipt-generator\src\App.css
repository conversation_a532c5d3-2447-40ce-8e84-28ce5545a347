.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f1f5f9;
}

.app-header {
  /* Header 樣式由 Header 組件自己處理 */
}

.app-main {
  flex: 1;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  padding: 1rem;
}

.form-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.preview-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #e2e8f0;
  position: relative;
}

.app-footer {
  background: white;
  border-top: 1px solid #e2e8f0;
  padding: 2rem;
}

.action-buttons {
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
}

/* 響應式設計 */
@media (max-width: 1024px) {
  .app-main {
    padding: 0.75rem;
    gap: 0.75rem;
  }
}

@media (max-width: 768px) {
  .app-main {
    grid-template-columns: 1fr;
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .app-footer {
    padding: 1rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .app-main {
    padding: 0.75rem;
  }
  
  .app-footer {
    padding: 1rem 0.75rem;
  }
}

/* 列印樣式 */
@media print {
  .app {
    background: white;
  }
  
  .app-main {
    display: block;
    padding: 0;
    margin: 0;
    max-width: none;
  }
  
  .form-section {
    display: none;
  }
  
  .preview-section {
    box-shadow: none;
    border: none;
    border-radius: 0;
    background: white;
  }
  
  .app-footer {
    display: none;
  }
}
