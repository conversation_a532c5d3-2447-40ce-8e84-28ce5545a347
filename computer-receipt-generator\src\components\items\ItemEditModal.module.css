.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  color: #374151;
  transition: all 0.2s ease;
}

.select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.select:hover {
  border-color: #9ca3af;
}

.checkboxGroup {
  margin-top: 0.5rem;
}

.checkboxLabel {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.5;
}

.checkbox {
  width: 1rem;
  height: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.checkbox:checked {
  background: #6366f1;
  border-color: #6366f1;
}

.checkbox:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

.checkboxText {
  flex: 1;
}

.discountInfo {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 0.5rem;
}

.discountLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #0369a1;
  margin-bottom: 0.5rem;
}

.discountDetails {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.discountAmount {
  font-size: 0.875rem;
  font-weight: 600;
  color: #059669;
}

.discountPercent {
  font-size: 0.75rem;
  font-weight: 500;
  color: #dc2626;
  background: #fee2e2;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .formRow {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .checkboxLabel {
    align-items: flex-start;
  }
  
  .discountDetails {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* 錯誤狀態 */
.select.error {
  border-color: #ef4444;
}

.select.error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 高對比模式支援 */
@media (prefers-contrast: high) {
  .select {
    border-width: 2px;
  }
  
  .checkbox {
    border-width: 2px;
  }
  
  .discountInfo {
    border-width: 2px;
  }
}

/* 焦點可見性 */
@media (prefers-reduced-motion: no-preference) {
  .select,
  .checkbox {
    transition: all 0.2s ease;
  }
}

@media (prefers-reduced-motion: reduce) {
  .select,
  .checkbox {
    transition: none;
  }
}
