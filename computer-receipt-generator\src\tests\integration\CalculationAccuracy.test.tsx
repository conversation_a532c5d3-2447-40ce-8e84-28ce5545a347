import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../../App';

// Mock html2pdf
vi.mock('html2pdf.js', () => ({
  default: () => ({
    set: () => ({
      from: () => ({
        save: vi.fn().mockResolvedValue(undefined)
      })
    })
  })
}));

describe('Calculation Accuracy Tests', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
  });

  const setupBasicReceipt = async () => {
    await user.type(screen.getByLabelText(/商店名稱/i), '測試商店');
    await user.type(screen.getByLabelText(/客戶姓名/i), '測試客戶');
  };

  const addItem = async (name: string, price: string, quantity: string = '1') => {
    await user.click(screen.getByRole('button', { name: /新增項目/i }));
    await user.type(screen.getByLabelText(/項目名稱/i), name);
    await user.type(screen.getByLabelText(/單價/i), price);
    await user.type(screen.getByLabelText(/數量/i), quantity);
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));
  };

  const setTaxRate = async (rate: string) => {
    const taxRateInput = screen.getByLabelText(/稅率/i);
    await user.clear(taxRateInput);
    await user.type(taxRateInput, rate);
  };

  it('calculates simple totals correctly', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增單一項目
    await addItem('測試項目', '1000');

    await waitFor(() => {
      expect(screen.getByText('NT$ 1,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 50')).toBeInTheDocument();    // 稅額 (5%)
      expect(screen.getByText('NT$ 1,050')).toBeInTheDocument(); // 總計
    });
  });

  it('calculates multiple items correctly', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增多個項目
    await addItem('項目 1', '1500');
    await addItem('項目 2', '2500');
    await addItem('項目 3', '1000');

    await waitFor(() => {
      expect(screen.getByText('NT$ 5,000')).toBeInTheDocument(); // 小計 (1500+2500+1000)
      expect(screen.getByText('NT$ 250')).toBeInTheDocument();   // 稅額 (5000*0.05)
      expect(screen.getByText('NT$ 5,250')).toBeInTheDocument(); // 總計
    });
  });

  it('calculates quantities correctly', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增有數量的項目
    await addItem('項目 1', '100', '5');   // 100 * 5 = 500
    await addItem('項目 2', '200', '3');   // 200 * 3 = 600
    await addItem('項目 3', '150', '2');   // 150 * 2 = 300

    await waitFor(() => {
      expect(screen.getByText('NT$ 1,400')).toBeInTheDocument(); // 小計 (500+600+300)
      expect(screen.getByText('NT$ 70')).toBeInTheDocument();    // 稅額 (1400*0.05)
      expect(screen.getByText('NT$ 1,470')).toBeInTheDocument(); // 總計
    });
  });

  it('handles decimal prices correctly', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增小數價格項目
    await addItem('項目 1', '99.99');
    await addItem('項目 2', '150.50');
    await addItem('項目 3', '75.25');

    await waitFor(() => {
      expect(screen.getByText('NT$ 325.74')).toBeInTheDocument(); // 小計 (99.99+150.50+75.25)
      expect(screen.getByText('NT$ 16.29')).toBeInTheDocument();  // 稅額 (325.74*0.05, 四捨五入)
      expect(screen.getByText('NT$ 342.03')).toBeInTheDocument(); // 總計
    });
  });

  it('handles decimal quantities correctly', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增小數數量項目
    await addItem('項目 1', '100', '2.5');   // 100 * 2.5 = 250
    await addItem('項目 2', '200', '1.75');  // 200 * 1.75 = 350
    await addItem('項目 3', '80', '3.25');   // 80 * 3.25 = 260

    await waitFor(() => {
      expect(screen.getByText('NT$ 860')).toBeInTheDocument();   // 小計 (250+350+260)
      expect(screen.getByText('NT$ 43')).toBeInTheDocument();    // 稅額 (860*0.05)
      expect(screen.getByText('NT$ 903')).toBeInTheDocument();   // 總計
    });
  });

  it('calculates different tax rates correctly', async () => {
    render(<App />);
    await setupBasicReceipt();

    await addItem('測試項目', '1000');

    // 測試 0% 稅率
    await setTaxRate('0');
    await waitFor(() => {
      expect(screen.getByText('NT$ 1,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 0')).toBeInTheDocument();     // 稅額
      expect(screen.getByText('NT$ 1,000')).toBeInTheDocument(); // 總計
    });

    // 測試 10% 稅率
    await setTaxRate('10');
    await waitFor(() => {
      expect(screen.getByText('NT$ 1,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 100')).toBeInTheDocument();   // 稅額
      expect(screen.getByText('NT$ 1,100')).toBeInTheDocument(); // 總計
    });

    // 測試 8.5% 稅率 (小數稅率)
    await setTaxRate('8.5');
    await waitFor(() => {
      expect(screen.getByText('NT$ 1,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 85')).toBeInTheDocument();    // 稅額
      expect(screen.getByText('NT$ 1,085')).toBeInTheDocument(); // 總計
    });
  });

  it('handles rounding correctly', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增會產生需要四捨五入的項目
    await addItem('項目 1', '33.33', '3');  // 33.33 * 3 = 99.99

    await waitFor(() => {
      expect(screen.getByText('NT$ 99.99')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 5')).toBeInTheDocument();     // 稅額 (99.99*0.05 = 4.9995, 四捨五入為 5)
      expect(screen.getByText('NT$ 104.99')).toBeInTheDocument(); // 總計
    });
  });

  it('handles very large numbers correctly', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增大金額項目
    await addItem('昂貴項目', '999999.99');

    await waitFor(() => {
      expect(screen.getByText('NT$ 999,999.99')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 50,000')).toBeInTheDocument();     // 稅額 (999999.99*0.05)
      expect(screen.getByText('NT$ 1,049,999.99')).toBeInTheDocument(); // 總計
    });
  });

  it('handles very small numbers correctly', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增小金額項目
    await addItem('便宜項目 1', '0.01');
    await addItem('便宜項目 2', '0.02');
    await addItem('便宜項目 3', '0.03');

    await waitFor(() => {
      expect(screen.getByText('NT$ 0.06')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 0')).toBeInTheDocument();    // 稅額 (0.06*0.05 = 0.003, 四捨五入為 0)
      expect(screen.getByText('NT$ 0.06')).toBeInTheDocument(); // 總計
    });
  });

  it('recalculates correctly when items are modified', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增初始項目
    await addItem('項目 1', '1000');
    await addItem('項目 2', '2000');

    // 驗證初始計算
    await waitFor(() => {
      expect(screen.getByText('NT$ 3,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 150')).toBeInTheDocument();   // 稅額
      expect(screen.getByText('NT$ 3,150')).toBeInTheDocument(); // 總計
    });

    // 編輯第一個項目
    const editButtons = screen.getAllByRole('button', { name: /編輯/i });
    await user.click(editButtons[0]);

    // 修改價格
    const priceInput = screen.getByDisplayValue('1000');
    await user.clear(priceInput);
    await user.type(priceInput, '1500');
    await user.click(screen.getByRole('button', { name: /儲存項目/i }));

    // 驗證重新計算
    await waitFor(() => {
      expect(screen.getByText('NT$ 3,500')).toBeInTheDocument(); // 小計 (1500+2000)
      expect(screen.getByText('NT$ 175')).toBeInTheDocument();   // 稅額
      expect(screen.getByText('NT$ 3,675')).toBeInTheDocument(); // 總計
    });
  });

  it('recalculates correctly when items are deleted', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增多個項目
    await addItem('項目 1', '1000');
    await addItem('項目 2', '2000');
    await addItem('項目 3', '3000');

    // 驗證初始計算
    await waitFor(() => {
      expect(screen.getByText('NT$ 6,000')).toBeInTheDocument(); // 小計
      expect(screen.getByText('NT$ 300')).toBeInTheDocument();   // 稅額
      expect(screen.getByText('NT$ 6,300')).toBeInTheDocument(); // 總計
    });

    // 刪除中間的項目
    const deleteButtons = screen.getAllByRole('button', { name: /刪除/i });
    await user.click(deleteButtons[1]); // 刪除項目 2

    // 驗證重新計算
    await waitFor(() => {
      expect(screen.getByText('NT$ 4,000')).toBeInTheDocument(); // 小計 (1000+3000)
      expect(screen.getByText('NT$ 200')).toBeInTheDocument();   // 稅額
      expect(screen.getByText('NT$ 4,200')).toBeInTheDocument(); // 總計
    });
  });

  it('maintains precision with complex calculations', async () => {
    render(<App />);
    await setupBasicReceipt();

    // 新增複雜的計算案例
    await addItem('項目 1', '123.45', '2.3');  // 123.45 * 2.3 = 283.935
    await addItem('項目 2', '67.89', '1.7');   // 67.89 * 1.7 = 115.413
    await addItem('項目 3', '99.99', '0.5');   // 99.99 * 0.5 = 49.995

    // 設定複雜稅率
    await setTaxRate('7.25');

    await waitFor(() => {
      // 小計: 283.935 + 115.413 + 49.995 = 449.343 ≈ 449.34
      expect(screen.getByText('NT$ 449.34')).toBeInTheDocument();
      
      // 稅額: 449.34 * 0.0725 = 32.577 ≈ 32.58
      expect(screen.getByText('NT$ 32.58')).toBeInTheDocument();
      
      // 總計: 449.34 + 32.58 = 481.92
      expect(screen.getByText('NT$ 481.92')).toBeInTheDocument();
    });
  });
});